# دليل المرجع السريع - المبادئ الأربعة

## 🎯 نظرة سريعة

### المبادئ الأربعة:
1. **🕐 الختم الزمني** - تسجيل وقت الإنشاء تلقائياً
2. **🛡️ منع الحذف الذكي** - فحص العلاقات قبل الحذف
3. **🔐 نظام الصلاحيات** - تحكم في الوصول حسب الدور
4. **📝 وضع الإنشاء** - بدء في وضع القراءة فقط

---

## ⚡ خطوات التطبيق السريع

### 1. تحديث نوع البيانات
```typescript
export type YourDataType = {
  // ... الحقول الموجودة
  createdAt: string; // ← إضافة
};
```

### 2. تحديث دالة الإضافة
```typescript
const addYourData = (data: Omit<YourDataType, 'id' | 'createdAt'>) => {
  const newData: YourDataType = {
    ...data,
    id: generateId(),
    createdAt: new Date().toISOString(), // ← إضافة
  };
  // ...
};
```

### 3. إضافة فحص العلاقات
```typescript
const checkYourDataRelations = (id: number) => {
  const relatedOperations: string[] = [];
  
  // فحص العلاقات مع الجداول الأخرى
  const relatedItems = otherTable.filter(item => 
    item.yourDataId === id
  );
  
  if (relatedItems.length > 0) {
    relatedOperations.push(`${relatedItems.length} عملية مرتبطة`);
  }
  
  return {
    canDelete: relatedOperations.length === 0,
    reason: relatedOperations.length > 0 ? 'يوجد عمليات مرتبطة' : undefined,
    relatedOperations
  };
};
```

### 4. تطبيق الصلاحيات
```typescript
const canView = currentUser?.permissions?.yourModule?.view ?? false;
const canCreate = currentUser?.permissions?.yourModule?.create ?? false;
const canEdit = currentUser?.permissions?.yourModule?.edit ?? false;
const canDelete = currentUser?.permissions?.yourModule?.delete ?? false;

// في الواجهة
{canCreate && (
  <Button onClick={startCreating}>إضافة جديد</Button>
)}

{canDelete && (
  <Button onClick={handleDelete}>حذف</Button>
)}
```

### 5. تطبيق وضع الإنشاء
```typescript
const [isCreating, setIsCreating] = useState(false);

const startCreating = () => {
  resetPage();
  setIsCreating(true);
};

// في الحقول
<Input
  value={formState.field}
  onChange={handleChange}
  disabled={!isCreating && !loadedItem} // ← تعطيل في وضع القراءة
/>
```

### 6. تحسين التاريخ والوقت
```typescript
const formatDateTime = (dateTimeString: string): string => {
  const date = new Date(dateTimeString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// في الحقل
<Input
  type="datetime-local"
  value={formState.dateTime}
  className="font-mono"
  style={{ direction: 'ltr' }}
/>

// في العرض
<TableCell className="font-mono" style={{ direction: 'ltr' }}>
  {formatDateTime(item.dateTime)}
</TableCell>
```

---

## 🔧 قوالب الكود الجاهزة

### قالب فحص الصلاحيات
```typescript
if (!canView) {
  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-600">غير مصرح لك بالوصول</h2>
        <p className="text-gray-500 mt-2">ليس لديك صلاحية لعرض هذه الصفحة</p>
      </div>
    </div>
  );
}
```

### قالب معالجة الحذف
```typescript
const handleDelete = () => {
  if (selectedId) {
    try {
      const relationCheck = checkYourDataRelations(selectedId);
      if (!relationCheck.canDelete) {
        toast({
          variant: 'destructive',
          title: 'لا يمكن الحذف',
          description: relationCheck.reason + 
            (relationCheck.relatedOperations ? 
              '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : 
              ''),
        });
        return;
      }

      deleteYourData(selectedId);
      toast({
        title: 'تم الحذف',
        description: 'تم الحذف بنجاح.',
      });
      resetPage();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في الحذف',
        description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
      });
    }
  }
};
```

### قالب رسالة وضع القراءة
```typescript
{!isCreating && !loadedItem && canCreate && (
  <div className="text-sm text-muted-foreground bg-blue-50 px-3 py-2 rounded-md border border-blue-200">
    💡 اضغط على "إضافة جديد" لبدء إنشاء عنصر جديد
  </div>
)}
```

---

## ✅ قائمة المراجعة السريعة

### الختم الزمني
- [ ] إضافة `createdAt: string` لنوع البيانات
- [ ] تحديث دالة الإضافة لتسجيل الوقت تلقائياً
- [ ] الحفاظ على الختم الأصلي عند التحديث

### منع الحذف الذكي
- [ ] إنشاء دالة `checkRelations()`
- [ ] فحص جميع الجداول المرتبطة
- [ ] تطبيق الفحص قبل الحذف
- [ ] عرض رسائل خطأ واضحة

### نظام الصلاحيات
- [ ] فحص صلاحيات العرض، الإنشاء، التعديل، الحذف
- [ ] تطبيق على جميع الأزرار والحقول
- [ ] منع الوصول بدون صلاحية
- [ ] رسائل واضحة للمستخدم

### وضع الإنشاء
- [ ] متغير حالة `isCreating`
- [ ] دالة `startCreating()`
- [ ] تعطيل الحقول في وضع القراءة
- [ ] رسالة توضيحية
- [ ] العودة لوضع القراءة بعد الحفظ

### التاريخ والوقت
- [ ] استخدام `datetime-local`
- [ ] دالة `formatDateTime()`
- [ ] عرض بالأرقام الإنجليزية
- [ ] تنسيق موحد في كل مكان

---

## 🚨 أخطاء شائعة وحلولها

### 1. localStorage في بيئة الخادم
```typescript
// ❌ خطأ
localStorage.getItem('key')

// ✅ صحيح
if (typeof window !== 'undefined') {
  localStorage.getItem('key')
}
```

### 2. عدم فحص الصلاحيات
```typescript
// ❌ خطأ
<Button onClick={handleDelete}>حذف</Button>

// ✅ صحيح
{canDelete && (
  <Button onClick={handleDelete}>حذف</Button>
)}
```

### 3. عدم تحديث الختم الزمني
```typescript
// ❌ خطأ - يفقد الختم الأصلي
const updateData = (data) => {
  return { ...data, updatedAt: new Date().toISOString() };
};

// ✅ صحيح - يحافظ على الختم الأصلي
const updateData = (data) => {
  const original = findOriginal(data.id);
  return { 
    ...data, 
    createdAt: original?.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString() 
  };
};
```

### 4. عدم فحص العلاقات بشكل شامل
```typescript
// ❌ خطأ - فحص جدول واحد فقط
const checkRelations = (id) => {
  return relatedTable.some(item => item.parentId === id);
};

// ✅ صحيح - فحص جميع الجداول المرتبطة
const checkRelations = (id) => {
  const relations = [];
  
  // فحص جميع الجداول المحتملة
  if (table1.some(item => item.parentId === id)) relations.push('جدول 1');
  if (table2.some(item => item.parentId === id)) relations.push('جدول 2');
  // ... المزيد
  
  return {
    canDelete: relations.length === 0,
    relatedOperations: relations
  };
};
```

---

## 📞 للمساعدة

راجع الدليل الشامل في `docs/supply-implementation-guide.md` للحصول على تفاصيل أكثر وأمثلة متقدمة.

**تذكر**: طبق المبادئ تدريجياً واختبر كل مبدأ قبل الانتقال للتالي!
