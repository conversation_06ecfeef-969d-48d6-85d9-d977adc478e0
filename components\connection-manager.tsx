'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { 
  Wifi, 
  WifiOff, 
  Globe, 
  ServerCrash, 
  Server, 
  UserX, 
  UserCheck, 
  RefreshCw,
  Network,
  Shield,
  ShieldOff,
  Monitor,
  Smartphone,
  Tablet
} from "lucide-react";
import { ConnectedDevice, ServerInfo } from '@/lib/types';
import localServer from '@/lib/local-server';
import { checkInternetConnection, formatConnectionTime } from '@/lib/network';
import { useToast } from '@/hooks/use-toast';

export function ConnectionManager() {
  const [serverInfo, setServerInfo] = useState<ServerInfo>({
    port: 3000,
    ipAddress: '*************',
    isActive: false,
    remoteAccessEnabled: false
  });
  const [internetConnected, setInternetConnected] = useState(false);
  const [connectedDevices, setConnectedDevices] = useState<ConnectedDevice[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // تحديث معلومات الخادم
    setServerInfo(localServer.getServerInfo());
    setConnectedDevices(localServer.getConnectedDevices());

    // التحقق من الاتصال بالإنترنت
    const checkConnection = async () => {
      const isConnected = await checkInternetConnection();
      setInternetConnected(isConnected);
    };

    checkConnection();
    const connectionInterval = setInterval(checkConnection, 30000); // كل 30 ثانية

    // إضافة مستمع للأجهزة المتصلة
    const handleDevicesUpdate = (devices: ConnectedDevice[]) => {
      setConnectedDevices(devices);
    };

    localServer.addConnectionListener(handleDevicesUpdate);

    return () => {
      clearInterval(connectionInterval);
      localServer.removeConnectionListener(handleDevicesUpdate);
    };
  }, []);

  const handleServerToggle = (enabled: boolean) => {
    if (enabled) {
      const info = localServer.start();
      setServerInfo(info);
      toast({
        title: 'تم تشغيل الخادم المحلي',
        description: `الخادم يعمل على العنوان ${info.ipAddress}:${info.port}`,
        duration: 3000,
      });
    } else {
      localServer.stop();
      setServerInfo(localServer.getServerInfo());
      toast({
        title: 'تم إيقاف الخادم المحلي',
        description: 'تم قطع اتصال جميع الأجهزة',
        duration: 3000,
      });
    }
  };

  const handleRemoteAccessToggle = (enabled: boolean) => {
    if (enabled) {
      if (localServer.enableRemoteAccess()) {
        setServerInfo(localServer.getServerInfo());
        toast({
          title: 'تم تمكين الوصول عن بُعد',
          description: 'يمكن الآن الوصول للنظام من خارج الشبكة المحلية',
          duration: 3000,
        });
      } else {
        toast({
          title: 'فشل في تمكين الوصول عن بُعد',
          description: 'تأكد من تشغيل الخادم المحلي أولاً',
          variant: 'destructive',
          duration: 3000,
        });
      }
    } else {
      localServer.disableRemoteAccess();
      setServerInfo(localServer.getServerInfo());
      toast({
        title: 'تم تعطيل الوصول عن بُعد',
        description: 'الوصول متاح فقط من الشبكة المحلية',
        duration: 3000,
      });
    }
  };

  const handleBlockDevice = (deviceId: string) => {
    const device = connectedDevices.find(d => d.id === deviceId);
    if (localServer.blockDevice(deviceId)) {
      toast({
        title: 'تم حظر الجهاز',
        description: `تم حظر ${device?.name || 'الجهاز'} بنجاح`,
        duration: 3000,
      });
    }
  };

  const handleAllowDevice = (deviceId: string) => {
    const device = connectedDevices.find(d => d.id === deviceId);
    if (localServer.allowDevice(deviceId)) {
      toast({
        title: 'تم السماح للجهاز',
        description: `تم السماح لـ ${device?.name || 'الجهاز'} بالوصول`,
        duration: 3000,
      });
    }
  };

  const handleDisconnectDevice = (deviceId: string) => {
    const device = connectedDevices.find(d => d.id === deviceId);
    if (localServer.disconnectDevice(deviceId)) {
      toast({
        title: 'تم قطع الاتصال',
        description: `تم قطع اتصال ${device?.name || 'الجهاز'}`,
        duration: 3000,
      });
    }
  };

  const handleRefreshDevices = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000)); // محاكاة التحميل
    localServer.refreshDevices();
    setIsRefreshing(false);
    toast({
      title: 'تم تحديث قائمة الأجهزة',
      duration: 2000,
    });
  };

  const getDeviceIcon = (deviceType?: string) => {
    switch (deviceType) {
      case 'Mobile':
        return <Smartphone className="h-4 w-4" />;
      case 'Tablet':
        return <Tablet className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'متصل':
        return 'text-green-700 bg-gradient-to-r from-green-100 to-green-200 border border-green-300 shadow-sm';
      case 'محظور':
        return 'text-red-700 bg-gradient-to-r from-red-100 to-red-200 border border-red-300 shadow-sm';
      case 'مسموح':
        return 'text-blue-700 bg-gradient-to-r from-blue-100 to-blue-200 border border-blue-300 shadow-sm';
      default:
        return 'text-gray-700 bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 shadow-sm';
    }
  };

  return (
    <div className="space-y-6">
      {/* كارت إدارة الخادم المحلي */}
      <Card className="shadow-lg border-l-4 border-l-teal-500 hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-teal-50 to-cyan-100">
        <CardHeader className="bg-gradient-to-r from-teal-100 to-cyan-200 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-teal-100 rounded-lg">
                {serverInfo.isActive ? 
                  <Server className="h-6 w-6 text-teal-600" /> : 
                  <ServerCrash className="h-6 w-6 text-gray-400" />
                }
              </div>
              <div>
                <h3 className="text-lg font-bold text-teal-800">إعدادات الخادم المحلي</h3>
                <p className="text-sm text-teal-600">
                  {serverInfo.isActive ? 'الخادم يعمل' : 'الخادم متوقف'}
                </p>
              </div>
            </div>
            <Switch 
              checked={serverInfo.isActive} 
              onCheckedChange={handleServerToggle} 
            />
          </div>
        </CardHeader>
        <CardContent className="pt-4 bg-gradient-to-br from-teal-25 to-cyan-50">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between bg-gradient-to-r from-teal-100 to-teal-200 p-3 rounded-md shadow-sm">
                <span className="font-medium text-teal-800">عنوان IP المحلي:</span>
                <div className="flex items-center gap-2">
                  <span className="font-mono text-sm text-teal-700">{serverInfo.ipAddress}</span>
                  <Button variant="ghost" size="sm" onClick={handleRefreshDevices} className="hover:bg-teal-200">
                    <RefreshCw className={`h-4 w-4 text-teal-600 ${isRefreshing ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between bg-gradient-to-r from-cyan-100 to-cyan-200 p-3 rounded-md shadow-sm">
                <span className="font-medium text-cyan-800">حالة الإنترنت:</span>
                <div className="flex items-center gap-2">
                  {internetConnected ? (
                    <>
                      <Globe className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-600 font-medium">متصل</span>
                    </>
                  ) : (
                    <>
                      <WifiOff className="h-4 w-4 text-red-600" />
                      <span className="text-sm text-red-600 font-medium">غير متصل</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-teal-100 to-cyan-100 p-4 rounded-lg shadow-sm border border-teal-200">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-teal-800">الوصول عن بُعد:</span>
                <Switch
                  checked={serverInfo.remoteAccessEnabled}
                  onCheckedChange={handleRemoteAccessToggle}
                  disabled={!serverInfo.isActive}
                />
              </div>
              <p className="text-xs text-teal-700 leading-relaxed">
                تمكين هذا الخيار يسمح بالوصول للنظام عن بُعد عندما يكون متصلاً بالإنترنت.
                تأكد من إعداد إعادة توجيه المنافذ على الراوتر.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* كارت الأجهزة المتصلة */}
      <Card className="shadow-lg border-l-4 border-l-blue-500 hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-blue-50 to-indigo-100">
        <CardHeader className="bg-gradient-to-r from-blue-100 to-indigo-200 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Network className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-blue-800">الأجهزة المتصلة</h3>
                <p className="text-sm text-blue-600">
                  {connectedDevices.length} {connectedDevices.length === 1 ? 'جهاز متصل' : 'أجهزة متصلة'}
                </p>
              </div>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="border-blue-300 text-blue-600 hover:bg-blue-50"
              disabled={!serverInfo.isActive || isRefreshing}
              onClick={handleRefreshDevices}
            >
              <RefreshCw className={`h-4 w-4 ml-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-4 bg-gradient-to-br from-blue-25 to-indigo-50">
          <div className="rounded-lg border border-blue-200 max-h-96 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100">
            <Table className="border-collapse border border-blue-300">
              <TableHeader>
                <TableRow>
                  <TableHead className="border border-blue-300 text-center font-bold text-blue-800 bg-gradient-to-r from-blue-200 to-indigo-300 py-2 text-sm w-12">
                    #
                  </TableHead>
                  <TableHead className="border border-blue-300 text-right font-semibold text-blue-800 bg-gradient-to-r from-blue-150 to-indigo-200 py-2 text-sm">
                    اسم الجهاز
                  </TableHead>
                  <TableHead className="border border-blue-300 text-right font-semibold text-blue-800 bg-gradient-to-r from-blue-150 to-indigo-200 py-2 text-sm">
                    عنوان IP
                  </TableHead>
                  <TableHead className="border border-blue-300 text-right font-semibold text-blue-800 bg-gradient-to-r from-blue-150 to-indigo-200 py-2 text-sm">
                    آخر اتصال
                  </TableHead>
                  <TableHead className="border border-blue-300 text-right font-semibold text-blue-800 bg-gradient-to-r from-blue-150 to-indigo-200 py-2 text-sm">
                    الحالة
                  </TableHead>
                  <TableHead className="border border-blue-300 text-right font-semibold text-blue-800 bg-gradient-to-r from-blue-150 to-indigo-200 py-2 text-sm">
                    الإجراءات
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {connectedDevices.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-blue-600 bg-gradient-to-r from-blue-100 to-indigo-100">
                      <div className="flex flex-col items-center gap-2">
                        <WifiOff className="h-8 w-8 text-blue-400" />
                        <span className="font-medium">لا توجد أجهزة متصلة حالياً</span>
                        {!serverInfo.isActive && (
                          <span className="text-xs text-blue-500">قم بتشغيل الخادم المحلي لرؤية الأجهزة المتصلة</span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  connectedDevices.map((device, index) => (
                    <TableRow key={device.id} className={index % 2 === 0 ? 'bg-gradient-to-r from-blue-50 to-indigo-50' : 'bg-gradient-to-r from-blue-100 to-indigo-100'}>
                      <TableCell className="border border-blue-300 text-center py-2 text-sm font-medium text-blue-800">
                        {index + 1}
                      </TableCell>
                      <TableCell className="border border-blue-300 py-2 text-sm">
                        <div className="flex items-center gap-2">
                          <div className="text-blue-600">
                            {getDeviceIcon(device.deviceType)}
                          </div>
                          <span className="font-medium text-blue-800">{device.name}</span>
                        </div>
                      </TableCell>
                      <TableCell className="border border-blue-300 py-2 text-sm font-mono text-blue-700">
                        {device.ip}
                      </TableCell>
                      <TableCell className="border border-blue-300 py-2 text-sm text-blue-700">
                        {device.lastActivity ? formatConnectionTime(device.lastActivity) : 'غير معروف'}
                      </TableCell>
                      <TableCell className="border border-blue-300 py-2 text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(device.status)}`}>
                          {device.status}
                        </span>
                      </TableCell>
                      <TableCell className="border border-blue-300 py-2 text-sm">
                        <div className="flex gap-1">
                          {device.status === 'متصل' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleBlockDevice(device.id)}
                              className="h-8 w-8 p-0 text-red-600 hover:bg-red-100 rounded-full"
                              title="حظر الجهاز"
                            >
                              <ShieldOff className="h-4 w-4" />
                            </Button>
                          )}
                          {device.status === 'محظور' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleAllowDevice(device.id)}
                              className="h-8 w-8 p-0 text-green-600 hover:bg-green-100 rounded-full"
                              title="السماح للجهاز"
                            >
                              <Shield className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDisconnectDevice(device.id)}
                            className="h-8 w-8 p-0 text-blue-600 hover:bg-blue-200 rounded-full"
                            title="قطع الاتصال"
                          >
                            <UserX className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
