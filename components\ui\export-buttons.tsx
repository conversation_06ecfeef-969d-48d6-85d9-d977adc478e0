"use client";

import { Button } from "@/components/ui/button";
import { FilePdf, FileSpreadsheet, Printer } from "lucide-react";
import { exportToCSV } from "@/lib/export-utils/arabic-export";
import { exportHTMLToPDF, exportDataToPDF } from "@/lib/export-utils/html-to-pdf";

interface ExportButtonsProps {
  data: any[];
  headers: string[];
  fileName: string;
  title: string;
  tableId: string;
}

export function ExportButtons({ 
  data, 
  headers, 
  fileName, 
  title, 
  tableId 
}: ExportButtonsProps) {
  
  const handlePdfExport = () => {
    // استخدام طريقة تحويل البيانات مباشرة إلى PDF
    exportDataToPDF(data, headers, fileName, title);
  };

  const handleCsvExport = () => {
    exportToCSV(data, headers, fileName);
  };

  const handlePrint = () => {
    // استخدام طريقة تحويل HTML مباشرة للطباعة
    exportHTMLToPDF(tableId, fileName, title);
  };

  return (
    <div className="flex space-x-2 rtl:space-x-reverse">
      <Button variant="outline" size="sm" onClick={handlePdfExport}>
        <FilePdf className="ml-1 mr-2 h-4 w-4" />
        تصدير PDF
      </Button>
      <Button variant="outline" size="sm" onClick={handleCsvExport}>
        <FileSpreadsheet className="ml-1 mr-2 h-4 w-4" />
        تصدير CSV
      </Button>
      <Button variant="outline" size="sm" onClick={handlePrint}>
        <Printer className="ml-1 mr-2 h-4 w-4" />
        طباعة
      </Button>
    </div>
  );
}
