import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع الأجهزة (بدون authentication للاختبار)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '1000');
    const search = searchParams.get('search') || '';
    
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where = search ? {
      OR: [
        { id: { contains: search, mode: 'insensitive' as const } },
        { storage: { contains: search, mode: 'insensitive' as const } },
        { status: { contains: search, mode: 'insensitive' as const } },
        { deviceModel: { name: { contains: search, mode: 'insensitive' as const } } },
        { manufacturer: { name: { contains: search, mode: 'insensitive' as const } } }
      ]
    } : {};

    const [devices, total] = await Promise.all([
      prisma.device.findMany({
        where,
        include: {
          manufacturer: { select: { id: true, name: true } },
          deviceModel: { select: { id: true, name: true } },
          warehouse: { select: { id: true, name: true } },
          supplier: { select: { id: true, name: true } }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.device.count({ where })
    ]);

    const hasMore = skip + devices.length < total;

    // تحويل البيانات للصيغة المتوافقة
    const formattedDevices = devices.map(device => ({
      id: device.id,
      model: device.deviceModel.name,
      manufacturer: device.manufacturer.name,
      warehouseName: device.warehouse.name,
      supplierName: device.supplier?.name || 'غير محدد',
      status: device.status,
      storage: device.storage,
      price: device.price,
      condition: device.condition,
      grade: device.grade,
      color: device.color,
      costPrice: device.costPrice,
      warrantyPeriod: device.warrantyPeriod,
      notes: device.notes,
      dateAdded: device.createdAt, // للتوافق مع الكود القديم
      createdAt: device.createdAt,
      updatedAt: device.updatedAt,
      warehouseId: device.warehouseId,
      manufacturerId: device.manufacturerId,
      deviceModelId: device.deviceModelId,
      supplierId: device.supplierId
    }));

    return NextResponse.json({
      data: formattedDevices,
      total,
      hasMore,
      page,
      limit
    });
  } catch (error) {
    console.error('خطأ في جلب الأجهزة:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الأجهزة' },
      { status: 500 }
    );
  }
}

// POST - إنشاء جهاز جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, manufacturerId, deviceModelId, warehouseId, supplierId, status, storage, price, condition, grade, color, costPrice, warrantyPeriod, notes } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !manufacturerId || !deviceModelId || !warehouseId) {
      return NextResponse.json(
        { error: 'رقم الجهاز والمصنع والموديل والمخزن مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار رقم الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (existingDevice) {
      return NextResponse.json(
        { error: 'يوجد جهاز بهذا الرقم بالفعل' },
        { status: 400 }
      );
    }

    const device = await prisma.device.create({
      data: {
        id,
        manufacturerId,
        deviceModelId,
        warehouseId,
        supplierId,
        status: status || 'في المخزن',
        condition: condition || 'جديد',
        storage,
        grade,
        color,
        price,
        costPrice,
        warrantyPeriod,
        notes
      },
      include: {
        manufacturer: { select: { id: true, name: true } },
        deviceModel: { select: { id: true, name: true } },
        warehouse: { select: { id: true, name: true } },
        supplier: { select: { id: true, name: true } }
      }
    });

    return NextResponse.json(device, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الجهاز' },
      { status: 500 }
    );
  }
}

// PUT - تحديث جهاز موجود
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, manufacturerId, deviceModelId, warehouseId, supplierId, status, storage, price, condition, grade, color, costPrice, warrantyPeriod, notes } = body;

    // التحقق من البيانات المطلوبة
    if (!id) {
      return NextResponse.json(
        { error: 'رقم الجهاز مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (!existingDevice) {
      return NextResponse.json(
        { error: 'الجهاز غير موجود' },
        { status: 404 }
      );
    }

    const device = await prisma.device.update({
      where: { id },
      data: {
        manufacturerId: manufacturerId !== undefined ? manufacturerId : existingDevice.manufacturerId,
        deviceModelId: deviceModelId !== undefined ? deviceModelId : existingDevice.deviceModelId,
        warehouseId: warehouseId !== undefined ? warehouseId : existingDevice.warehouseId,
        supplierId: supplierId !== undefined ? supplierId : existingDevice.supplierId,
        status: status || existingDevice.status,
        condition: condition || existingDevice.condition,
        storage: storage !== undefined ? storage : existingDevice.storage,
        grade: grade !== undefined ? grade : existingDevice.grade,
        color: color !== undefined ? color : existingDevice.color,
        price: price !== undefined ? price : existingDevice.price,
        costPrice: costPrice !== undefined ? costPrice : existingDevice.costPrice,
        warrantyPeriod: warrantyPeriod !== undefined ? warrantyPeriod : existingDevice.warrantyPeriod,
        notes: notes !== undefined ? notes : existingDevice.notes
      },
      include: {
        manufacturer: { select: { id: true, name: true } },
        deviceModel: { select: { id: true, name: true } },
        warehouse: { select: { id: true, name: true } },
        supplier: { select: { id: true, name: true } }
      }
    });

    return NextResponse.json(device);
  } catch (error) {
    console.error('خطأ في تحديث الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الجهاز' },
      { status: 500 }
    );
  }
}

// DELETE - حذف جهاز
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'رقم الجهاز مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id },
      include: {
        deviceModel: { select: { name: true } }
      }
    });

    if (!existingDevice) {
      return NextResponse.json(
        { error: 'الجهاز غير موجود' },
        { status: 404 }
      );
    }

    // فحص العلاقات المرتبطة باستخدام النماذج الجديدة
    const relatedRecords = {
      supplyOrderItems: 0,
      saleItems: 0,
      returnItems: 0,
      maintenanceOrders: 0,
      evaluationOrders: 0,
      movements: 0
    };

    try {
      relatedRecords.supplyOrderItems = await prisma.supplyOrderItem.count({ where: { deviceId: id } });
      relatedRecords.saleItems = await prisma.saleItem.count({ where: { deviceId: id } });
      relatedRecords.returnItems = await prisma.returnItem.count({ 
        where: { 
          OR: [
            { deviceId: id },
            { replacementDeviceId: id }
          ]
        }
      });
      relatedRecords.maintenanceOrders = await prisma.maintenanceOrderItem.count({ where: { deviceId: id } });
      relatedRecords.evaluationOrders = await prisma.evaluationOrderItem.count({ where: { deviceId: id } });
      relatedRecords.movements = await prisma.deviceMovement.count({ where: { deviceId: id } });
    } catch (error) {
      console.warn('Error checking related records:', error);
    }

    // التحقق من وجود علاقات مرتبطة
    const totalRelated = Object.values(relatedRecords).reduce((sum, count) => sum + count, 0);

    if (totalRelated > 0) {
      const relatedMessages = [];
      if (relatedRecords.supplyOrderItems > 0) relatedMessages.push(`${relatedRecords.supplyOrderItems} عنصر توريد`);
      if (relatedRecords.saleItems > 0) relatedMessages.push(`${relatedRecords.saleItems} عنصر بيع`);
      if (relatedRecords.returnItems > 0) relatedMessages.push(`${relatedRecords.returnItems} عنصر إرجاع`);
      if (relatedRecords.maintenanceOrders > 0) relatedMessages.push(`${relatedRecords.maintenanceOrders} أمر صيانة`);
      if (relatedRecords.evaluationOrders > 0) relatedMessages.push(`${relatedRecords.evaluationOrders} أمر تقييم`);
      if (relatedRecords.movements > 0) relatedMessages.push(`${relatedRecords.movements} حركة جهاز`);

      return NextResponse.json({
        error: 'لا يمكن حذف الجهاز',
        reason: 'الجهاز مرتبط بعمليات أخرى في النظام',
        relatedRecords,
        details: `الجهاز مرتبط بـ: ${relatedMessages.join('، ')}`,
        suggestion: 'يمكنك تغيير حالة الجهاز إلى "غير فعال" بدلاً من حذفه',
        deviceInfo: {
          id: existingDevice.id,
          model: existingDevice.deviceModel.name,
          status: existingDevice.status
        }
      }, { status: 400 });
    }

    // إذا لم توجد علاقات مرتبطة، يمكن الحذف بأمان
    await prisma.device.delete({
      where: { id }
    });

    return NextResponse.json({ 
      message: 'تم حذف الجهاز بنجاح',
      deletedDevice: {
        id: existingDevice.id,
        model: existingDevice.deviceModel.name
      }
    });
  } catch (error) {
    console.error('خطأ في حذف الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الجهاز' },
      { status: 500 }
    );
  }
}
