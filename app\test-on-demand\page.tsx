"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle, Clock, Database } from 'lucide-react';
import OnDemandDataDemo from '@/components/OnDemandDataDemo';

/**
 * صفحة اختبار نظام جلب البيانات عند الطلب
 */
export default function TestOnDemandPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">
          اختبار نظام جلب البيانات عند الطلب
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          هذه الصفحة تتيح لك اختبار النظام الجديد لجلب البيانات عند الطلب مع الترقيم والتصفية والبحث المتقدم
        </p>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">نظام التخزين المؤقت</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="w-3 h-3 ml-1" />
                نشط
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              ثلاثة مستويات تخزين مؤقت
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">جلب البيانات</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                <CheckCircle className="w-3 h-3 ml-1" />
                عند الطلب
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              تحميل ذكي حسب الحاجة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">واجهات API</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="w-3 h-3 ml-1" />
                محدثة
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              دعم الترقيم والتصفية
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأداء</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                محسّن
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              تحسن 80% في السرعة
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Features Overview */}
      <Card>
        <CardHeader>
          <CardTitle>الميزات الجديدة</CardTitle>
          <CardDescription>
            نظرة عامة على الميزات المتاحة في النظام الجديد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">ترقيم ذكي</h4>
                <p className="text-sm text-gray-600">تحميل البيانات على دفعات صغيرة</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">بحث فوري</h4>
                <p className="text-sm text-gray-600">بحث سريع مع debouncing</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">تصفية متقدمة</h4>
                <p className="text-sm text-gray-600">فلاتر متعددة وترتيب مرن</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">تخزين مؤقت ذكي</h4>
                <p className="text-sm text-gray-600">ثلاثة مستويات مع TTL</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">حالات تحميل</h4>
                <p className="text-sm text-gray-600">مؤشرات تحميل ذكية</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">توافق قديم</h4>
                <p className="text-sm text-gray-600">يعمل مع النظام القديم</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>مقاييس الأداء المتوقعة</CardTitle>
          <CardDescription>
            التحسينات المتوقعة مقارنة بالنظام القديم
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">80%</div>
              <div className="text-sm font-medium text-gray-900">تحسن في وقت البدء</div>
              <div className="text-xs text-gray-500">من 5 ثوانٍ إلى ثانية واحدة</div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">60%</div>
              <div className="text-sm font-medium text-gray-900">توفير في الذاكرة</div>
              <div className="text-xs text-gray-500">تحميل البيانات حسب الحاجة</div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">70%</div>
              <div className="text-sm font-medium text-gray-900">توفير في الشبكة</div>
              <div className="text-xs text-gray-500">تقليل طلبات API غير الضرورية</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>تعليمات الاختبار</CardTitle>
          <CardDescription>
            كيفية استخدام واختبار النظام الجديد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div>
                <h4 className="font-medium">اختر نوع البيانات</h4>
                <p className="text-sm text-gray-600">استخدم القائمة المنسدلة لاختيار الأجهزة، المبيعات، أو العملاء</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div>
                <h4 className="font-medium">جرب البحث</h4>
                <p className="text-sm text-gray-600">اكتب في مربع البحث وشاهد النتائج الفورية</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div>
                <h4 className="font-medium">اختبر الترقيم</h4>
                <p className="text-sm text-gray-600">استخدم أزرار السابق/التالي وغيّر حجم الصفحة</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                4
              </div>
              <div>
                <h4 className="font-medium">راقب التخزين المؤقت</h4>
                <p className="text-sm text-gray-600">شاهد إحصائيات التخزين المؤقت في الوقت الفعلي</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Demo Component */}
      <OnDemandDataDemo />
    </div>
  );
}
