import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استخراج معاملات البحث
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const warehouseId = url.searchParams.get('warehouseId');
    const status = url.searchParams.get('status');
    const condition = url.searchParams.get('condition');

    // بناء شروط البحث
    const where: any = {
      isActive: true
    };

    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { storage: { contains: search, mode: 'insensitive' } },
        { color: { contains: search, mode: 'insensitive' } },
        { deviceModel: { name: { contains: search, mode: 'insensitive' } } },
        { manufacturer: { name: { contains: search, mode: 'insensitive' } } }
      ];
    }

    if (warehouseId) {
      where.warehouseId = parseInt(warehouseId);
    }

    if (status) {
      where.status = status;
    }

    if (condition) {
      where.condition = condition;
    }

    // حساب الإزاحة
    const skip = (page - 1) * limit;

    // جلب العدد الإجمالي
    const total = await prisma.device.count({ where });

    // جلب البيانات مع العلاقات
    const devices = await prisma.device.findMany({
      where,
      include: {
        manufacturer: {
          select: { id: true, name: true, code: true }
        },
        deviceModel: {
          select: { id: true, name: true, category: true }
        },
        warehouse: {
          select: { id: true, name: true, location: true, type: true }
        },
        supplier: {
          select: { id: true, name: true, code: true }
        }
      },
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' }
    });

    // تحويل البيانات للتوافق مع الواجهة الأمامية
    const transformedDevices = devices.map(device => ({
      id: device.id,
      model: device.deviceModel.name,
      manufacturer: device.manufacturer.name,
      warehouseName: device.warehouse.name,
      supplierName: device.supplier?.name || 'غير محدد',
      status: device.status,
      condition: device.condition,
      grade: device.grade,
      storage: device.storage,
      color: device.color,
      price: device.price,
      costPrice: device.costPrice,
      warrantyPeriod: device.warrantyPeriod,
      notes: device.notes,
      dateAdded: device.createdAt,
      updatedAt: device.updatedAt,
      // الخصائص الإضافية للتوافق
      warehouseId: device.warehouseId,
      manufacturerId: device.manufacturerId,
      deviceModelId: device.deviceModelId,
      supplierId: device.supplierId
    }));

    // إنشاء الاستجابة المرقمة
    const response = {
      data: transformedDevices,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Failed to fetch devices:', error);
    return NextResponse.json(
      { 
        error: 'فشل في جلب الأجهزة', 
        details: error instanceof Error ? error.message : 'خطأ غير معروف' 
      }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newDevice = await request.json();

    // Basic validation
    if (!newDevice.id || !newDevice.manufacturerId || !newDevice.deviceModelId || !newDevice.warehouseId) {
      return NextResponse.json(
        { message: 'Device ID, manufacturer, model, and warehouse are required' },
        { status: 400 }
      );
    }

    try {
      // Check if device already exists
      const existingDevice = await prisma.device.findUnique({
        where: { id: newDevice.id }
      });

      if (existingDevice) {
        return NextResponse.json(
          { message: 'Device with this ID already exists' },
          { status: 409 }
        );
      }

      // Create new device
      const device = await prisma.device.create({
        data: {
          id: newDevice.id,
          manufacturerId: newDevice.manufacturerId,
          deviceModelId: newDevice.deviceModelId,
          warehouseId: newDevice.warehouseId,
          supplierId: newDevice.supplierId,
          status: newDevice.status || 'في المخزن',
          condition: newDevice.condition || 'جديد',
          grade: newDevice.grade,
          storage: newDevice.storage,
          color: newDevice.color,
          price: newDevice.price,
          costPrice: newDevice.costPrice,
          warrantyPeriod: newDevice.warrantyPeriod,
          notes: newDevice.notes
        },
        include: {
          manufacturer: true,
          deviceModel: true,
          warehouse: true,
          supplier: true
        }
      });

      // تحديث مخزون المخزن
      await prisma.warehouseStock.upsert({
        where: {
          warehouseId_manufacturerId_deviceModelId_condition: {
            warehouseId: device.warehouseId,
            manufacturerId: device.manufacturerId,
            deviceModelId: device.deviceModelId,
            condition: device.condition
          }
        },
        update: {
          totalQuantity: { increment: 1 },
          availableQuantity: { increment: 1 }
        },
        create: {
          warehouseId: device.warehouseId,
          manufacturerId: device.manufacturerId,
          deviceModelId: device.deviceModelId,
          condition: device.condition,
          totalQuantity: 1,
          availableQuantity: 1
        }
      });

      // تسجيل حركة الجهاز
      await prisma.deviceMovement.create({
        data: {
          deviceId: device.id,
          movementType: 'add',
          toWarehouseId: device.warehouseId,
          toStatus: device.status,
          employeeId: authResult.user!.id,
          employeeName: authResult.user!.name || authResult.user!.username,
          notes: 'إضافة جهاز جديد'
        }
      });

      return NextResponse.json(device, { status: 201 });
    } catch (error) {
      console.error('Failed to create device:', error);
      return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to create device:', error);
    return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedDevice = await request.json();

    if (!updatedDevice.id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 }
      );
    }

    try {
      // Check if device exists
      const existingDevice = await prisma.device.findUnique({
        where: { id: updatedDevice.id }
      });

      if (!existingDevice) {
        return NextResponse.json({ message: 'Device not found' }, { status: 404 });
      }

      // Update device
      const device = await prisma.device.update({
        where: { id: updatedDevice.id },
        data: {
          manufacturerId: updatedDevice.manufacturerId || existingDevice.manufacturerId,
          deviceModelId: updatedDevice.deviceModelId || existingDevice.deviceModelId,
          warehouseId: updatedDevice.warehouseId || existingDevice.warehouseId,
          supplierId: updatedDevice.supplierId !== undefined ? updatedDevice.supplierId : existingDevice.supplierId,
          status: updatedDevice.status || existingDevice.status,
          condition: updatedDevice.condition || existingDevice.condition,
          grade: updatedDevice.grade !== undefined ? updatedDevice.grade : existingDevice.grade,
          storage: updatedDevice.storage !== undefined ? updatedDevice.storage : existingDevice.storage,
          color: updatedDevice.color !== undefined ? updatedDevice.color : existingDevice.color,
          price: updatedDevice.price !== undefined ? updatedDevice.price : existingDevice.price,
          costPrice: updatedDevice.costPrice !== undefined ? updatedDevice.costPrice : existingDevice.costPrice,
          warrantyPeriod: updatedDevice.warrantyPeriod !== undefined ? updatedDevice.warrantyPeriod : existingDevice.warrantyPeriod,
          notes: updatedDevice.notes !== undefined ? updatedDevice.notes : existingDevice.notes
        },
        include: {
          manufacturer: true,
          deviceModel: true,
          warehouse: true,
          supplier: true
        }
      });

      return NextResponse.json(device);
    } catch (error) {
      console.error('Failed to update device:', error);
      return NextResponse.json({ error: 'Failed to update device' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to update device:', error);
    return NextResponse.json({ error: 'Failed to update device' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 }
      );
    }

    try {
      // Check if device exists
      const existingDevice = await prisma.device.findUnique({
        where: { id }
      });

      if (!existingDevice) {
        return NextResponse.json({ message: 'Device not found' }, { status: 404 });
      }

      // Soft delete - mark as inactive instead of actual deletion
      await prisma.device.update({
        where: { id },
        data: { isActive: false }
      });

      return NextResponse.json({ message: 'Device deleted successfully' });
    } catch (error) {
      console.error('Failed to delete device:', error);
      return NextResponse.json({ error: 'Failed to delete device' }, { status: 500 });
    }
  } catch (error) {
    console.error('Failed to delete device:', error);
    return NextResponse.json({ error: 'Failed to delete device' }, { status: 500 });
  }
}