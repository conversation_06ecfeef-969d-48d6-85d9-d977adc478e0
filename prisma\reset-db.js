const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // حذف السجلات من جميع الجداول بالترتيب الصحيح
    console.log('تفريغ قاعدة البيانات...');
    
    // حذف الأجهزة أولاً
    await prisma.device.deleteMany({});
    console.log('- تم حذف الأجهزة');
    
    // حذف طلبات التوريد
    await prisma.supplyOrder.deleteMany({});
    console.log('- تم حذف طلبات التوريد');
    
    // حذف المبيعات
    await prisma.sale.deleteMany({});
    console.log('- تم حذف المبيعات');
    
    console.log('تم تفريغ قاعدة البيانات بنجاح');
    
    // إعادة إنشاء المخازن الافتراضية
    const warehouseCount = await prisma.warehouse.count();
    
    if (warehouseCount === 0) {
      console.log('إنشاء مخازن افتراضية...');
      
      await prisma.warehouse.create({
        data: {
          name: 'المخزن الرئيسي',
          type: 'رئيسي',
          location: 'المقر الرئيسي'
        }
      });
      
      await prisma.warehouse.create({
        data: {
          name: 'مخزن الصيانة',
          type: 'فرعي',
          location: 'قسم الصيانة'
        }
      });
      
      console.log('- تم إنشاء المخازن الافتراضية');
    } else {
      console.log(`- المخازن موجودة بالفعل (${warehouseCount} مخزن)`);
    }
  } catch (error) {
    console.error('حدث خطأ أثناء تفريغ قاعدة البيانات:', error);
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
