import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع API الجرد
export async function GET() {
  try {
    const records = await prisma.stocktake.findMany({
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json(records);
  } catch (error) {
    console.error('خطأ في جلب API الجرد:', error);
    return NextResponse.json(
      { error: 'فشل في جلب API الجرد' },
      { status: 500 }
    );
  }
}

// POST - إنشاء API الجرد جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const record = await prisma.stocktake.create({
      data: body
    });

    return NextResponse.json(record, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء API الجرد:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء API الجرد' },
      { status: 500 }
    );
  }
}

// PUT - تحديث API الجرد
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...data } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف السجل مطلوب' },
        { status: 400 }
      );
    }

    const record = await prisma.stocktake.update({
      where: { id: parseInt(id) },
      data
    });

    return NextResponse.json(record);
  } catch (error) {
    console.error('خطأ في تحديث API الجرد:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث API الجرد' },
      { status: 500 }
    );
  }
}

// DELETE - حذف API الجرد
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف السجل مطلوب' },
        { status: 400 }
      );
    }

    await prisma.stocktake.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'تم حذف السجل بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف API الجرد:', error);
    return NextResponse.json(
      { error: 'فشل في حذف السجل' },
      { status: 500 }
    );
  }
}