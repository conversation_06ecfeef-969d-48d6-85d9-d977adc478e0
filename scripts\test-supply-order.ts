#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testSupplyOrder() {
  try {
    console.log('🧪 اختبار إنشاء أمر توريد...');

    // الحصول على بيانات أساسية
    const manufacturer = await prisma.manufacturer.findFirst();
    const deviceModel = await prisma.deviceModel.findFirst();
    const warehouse = await prisma.warehouse.findFirst();
    const supplier = await prisma.supplier.findFirst();
    const user = await prisma.user.findFirst();

    if (!manufacturer || !deviceModel || !warehouse || !supplier || !user) {
      console.error('❌ البيانات الأساسية غير متوفرة');
      return;
    }

    console.log('📊 البيانات الأساسية:');
    console.log(`  - الشركة المصنعة: ${manufacturer.name}`);
    console.log(`  - الموديل: ${deviceModel.name}`);
    console.log(`  - المخزن: ${warehouse.name}`);
    console.log(`  - المورد: ${supplier.name}`);
    console.log(`  - المستخدم: ${user.name}`);

    // إنشاء أمر توريد
    const orderNumber = `SUP-TEST-${Date.now()}`;
    const imei = `TEST${Date.now()}`;

    const supplyOrder = await prisma.$transaction(async (tx) => {
      // إنشاء أمر التوريد
      const order = await tx.supplyOrder.create({
        data: {
          orderNumber: orderNumber,
          supplierId: supplier.id,
          warehouseId: warehouse.id,
          employeeId: user.id,
          supplyDate: new Date(),
          totalAmount: 500,
          totalQuantity: 1,
          status: 'confirmed',
          notes: 'اختبار إنشاء أمر توريد'
        }
      });

      // إنشاء الجهاز
      const device = await tx.device.create({
        data: {
          id: imei,
          manufacturerId: manufacturer.id,
          deviceModelId: deviceModel.id,
          warehouseId: warehouse.id,
          supplierId: supplier.id,
          status: 'في المخزن',
          condition: 'جديد',
          costPrice: 400,
          price: 500
        }
      });

      // إنشاء عنصر أمر التوريد
      const orderItem = await tx.supplyOrderItem.create({
        data: {
          supplyOrderId: order.id,
          deviceId: device.id,
          manufacturerId: manufacturer.id,
          deviceModelId: deviceModel.id,
          condition: 'جديد',
          costPrice: 400,
          sellingPrice: 500
        }
      });

      return { order, device, orderItem };
    });

    console.log('✅ تم إنشاء أمر التوريد بنجاح:');
    console.log(`  - رقم الأمر: ${supplyOrder.order.orderNumber}`);
    console.log(`  - IMEI الجهاز: ${supplyOrder.device.id}`);
    console.log(`  - المبلغ الإجمالي: ${supplyOrder.order.totalAmount}`);
    
    // التحقق من وجود الجهاز في المخزون
    const deviceInInventory = await prisma.device.findUnique({
      where: { id: imei },
      include: {
        manufacturer: true,
        deviceModel: true,
        warehouse: true
      }
    });

    if (deviceInInventory) {
      console.log('✅ الجهاز متاح في المخزون:');
      console.log(`  - ${deviceInInventory.manufacturer.name} ${deviceInInventory.deviceModel.name}`);
      console.log(`  - المخزن: ${deviceInInventory.warehouse.name}`);
      console.log(`  - الحالة: ${deviceInInventory.status}`);
    }

    console.log('🎉 تم اختبار إنشاء أمر التوريد بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في اختبار أمر التوريد:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSupplyOrder();
