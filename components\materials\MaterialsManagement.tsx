'use client';

import { useState } from 'react';
import ManufacturersList from './ManufacturersList';
import ModelsList from './ModelsList';
import AddModelDialog from './AddModelDialog';
import type { Manufacturer } from '@/lib/types';

export default function MaterialsManagement() {
  const [selectedManufacturer, setSelectedManufacturer] = useState<Manufacturer | null>(null);
  const [isAddModelDialogOpen, setIsAddModelDialogOpen] = useState(false);
  const [preselectedManufacturer, setPreselectedManufacturer] = useState<Manufacturer | null>(null);

  const handleManufacturerSelect = (manufacturer: Manufacturer) => {
    setSelectedManufacturer(manufacturer);
  };

  const handleBackToManufacturers = () => {
    setSelectedManufacturer(null);
  };

  const handleAddModel = (manufacturer: Manufacturer) => {
    setPreselectedManufacturer(manufacturer);
    setIsAddModelDialogOpen(true);
  };

  const handleCloseAddModelDialog = () => {
    setIsAddModelDialogOpen(false);
    setPreselectedManufacturer(null);
  };

  const handleAddNewManufacturer = () => {
    setPreselectedManufacturer(null);
    setIsAddModelDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* قائمة الشركات */}
        <div className={selectedManufacturer ? 'lg:block hidden' : 'block'}>
          <ManufacturersList
            onManufacturerSelect={handleManufacturerSelect}
            selectedManufacturer={selectedManufacturer}
          />
        </div>

        {/* عرض الموديلات */}
        <div className={selectedManufacturer ? 'block' : 'lg:block hidden'}>
          <ModelsList
            manufacturer={selectedManufacturer}
            onBack={handleBackToManufacturers}
            onAddModel={handleAddModel}
            onAddNewManufacturer={handleAddNewManufacturer}
          />
        </div>
      </div>

      {/* نافذة إضافة موديل جديد */}
      <AddModelDialog
        isOpen={isAddModelDialogOpen}
        onClose={handleCloseAddModelDialog}
        preselectedManufacturer={preselectedManufacturer}
      />
    </div>
  );
}
