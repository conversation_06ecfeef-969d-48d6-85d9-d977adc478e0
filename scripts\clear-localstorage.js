// سكربت لمسح localStorage للمتصفح
// يجب تشغيل هذا في console المتصفح

console.log('🧹 مسح localStorage لأوامر التوريد...');

// مسح المسودة المحفوظة
localStorage.removeItem('supplyOrderDraft');

// مسح أي بيانات أخرى متعلقة بأوامر التوريد
const keysToRemove = [];
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key && (key.includes('supply') || key.includes('Supply') || key.includes('draft'))) {
    keysToRemove.push(key);
  }
}

keysToRemove.forEach(key => {
  localStorage.removeItem(key);
  console.log(`✅ تم حذف: ${key}`);
});

console.log('✅ تم مسح localStorage');
console.log('🔄 أعد تحميل الصفحة لرؤية البيانات الصحيحة');

// إعادة تحميل الصفحة
window.location.reload();
