const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAddClient() {
  try {
    console.log('اختبار إضافة عميل جديد...');
    
    // إضافة عميل جديد
    const newClient = await prisma.client.create({
      data: {
        name: 'عميل اختبار',
        phone: '0501234567',
        email: '<EMAIL>'
      }
    });
    
    console.log('تم إنشاء العميل:', newClient);
    
    // جلب جميع العملاء
    const allClients = await prisma.client.findMany();
    console.log('إجمالي العملاء:', allClients.length);
    
    allClients.forEach((client, index) => {
      console.log(`${index + 1}. ${client.name} - ${client.phone}`);
    });
    
  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAddClient();
