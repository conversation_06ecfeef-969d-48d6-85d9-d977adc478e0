'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  Smartphone,
  CheckCircle,
  Wrench,
  DollarSign,
} from 'lucide-react';

// نسخة مبسطة وآمنة من صفحة Dashboard
export default function DashboardPage() {
  const [stats, setStats] = useState({
    totalDevices: 0,
    readyForSale: 0,
    inMaintenance: 0,
    totalSalesValue: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاكاة تحميل البيانات بطريقة آمنة
    const loadStats = async () => {
      try {
        setIsLoading(true);
        
        // تأخير بسيط لمحاكاة تحميل البيانات
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // بيانات افتراضية آمنة
        setStats({
          totalDevices: 0,
          readyForSale: 0,
          inMaintenance: 0,
          totalSalesValue: 0.00
        });
        
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        // تعيين قيم افتراضية في حالة الخطأ
        setStats({
          totalDevices: 0,
          readyForSale: 0,
          inMaintenance: 0,
          totalSalesValue: 0.00
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, []);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">لوحة التحكم</h1>
        {isLoading && (
          <div className="flex items-center gap-2">
            <LoadingSpinner size="sm" />
            <span className="text-sm text-muted-foreground">جاري التحميل...</span>
          </div>
        )}
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي الأجهزة
            </CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-8">
                <LoadingSpinner size="sm" />
              </div>
            ) : (
              <div className="text-2xl font-bold">{stats.totalDevices}</div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">جاهز للبيع</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-8">
                <LoadingSpinner size="sm" />
              </div>
            ) : (
              <div className="text-2xl font-bold">{stats.readyForSale}</div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تحتاج صيانة</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-8">
                <LoadingSpinner size="sm" />
              </div>
            ) : (
              <div className="text-2xl font-bold">{stats.inMaintenance}</div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي المبيعات
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-8">
                <LoadingSpinner size="sm" />
              </div>
            ) : (
              <div className="text-2xl font-bold">
                ${stats.totalSalesValue.toFixed(2)}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>الأجهزة الحديثة</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Smartphone className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>لا توجد أجهزة حديثة</p>
                <p className="text-sm mt-1">سيتم عرض آخر 5 أجهزة هنا</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>المبيعات الحديثة</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <DollarSign className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>لا توجد مبيعات حديثة</p>
                <p className="text-sm mt-1">سيتم عرض آخر 5 مبيعات هنا</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>سجل الأنشطة</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>لا توجد أنشطة حديثة</p>
              <p className="text-sm mt-1">سيتم عرض سجل الأنشطة هنا</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
