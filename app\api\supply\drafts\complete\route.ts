import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

// POST - تحويل مسودة إلى أمر توريد فعلي
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const userId = authResult.user!.id;
    const { draftId } = await request.json();

    if (!draftId) {
      return NextResponse.json({ error: 'معرف المسودة مطلوب' }, { status: 400 });
    }

    console.log('🔄 تحويل مسودة إلى أمر فعلي:', { draftId, userId });

    // جلب المسودة
    const draft = await prisma.$queryRaw`
      SELECT * FROM "SupplyOrderDraft" 
      WHERE "draftId" = ${draftId} AND "userId" = ${userId}
    `;

    if (!Array.isArray(draft) || draft.length === 0) {
      return NextResponse.json({ error: 'المسودة غير موجودة' }, { status: 404 });
    }

    const draftData = draft[0] as any;

    // التحقق من البيانات المطلوبة
    if (!draftData.items || !Array.isArray(JSON.parse(draftData.items)) || JSON.parse(draftData.items).length === 0) {
      return NextResponse.json({ error: 'يجب إضافة عنصر واحد على الأقل' }, { status: 400 });
    }

    const items = JSON.parse(draftData.items);

    // بدء Transaction لضمان سلامة البيانات
    const result = await prisma.$transaction(async (tx) => {
      // 1. توليد رقم أمر التوريد
      const latestOrder = await tx.supplyOrder.findFirst({
        orderBy: { orderNumber: 'desc' },
        select: { orderNumber: true }
      });

      const nextOrderNumber = latestOrder ? parseInt(latestOrder.orderNumber) + 1 : 1;

      // 2. إنشاء أمر التوريد
      const supplyOrder = await tx.supplyOrder.create({
        data: {
          orderNumber: nextOrderNumber.toString(),
          supplierName: draftData.supplierName || 'غير محدد',
          supplierId: draftData.supplierId,
          warehouseId: draftData.warehouseId,
          employeeName: draftData.employeeName,
          supplyDate: new Date(draftData.supplyDate),
          notes: draftData.notes,
          status: 'active',
          totalDevices: items.length,
          attachments: draftData.attachments ? JSON.parse(draftData.attachments) : []
        }
      });

      // 3. إضافة الأجهزة للمخزون
      for (const item of items) {
        if (!item.serialNumber || !item.modelId) {
          throw new Error(`بيانات غير مكتملة للجهاز: ${JSON.stringify(item)}`);
        }

        // التحقق من عدم تكرار الرقم التسلسلي
        const existingDevice = await tx.device.findUnique({
          where: { serialNumber: item.serialNumber }
        });

        if (existingDevice) {
          throw new Error(`الرقم التسلسلي ${item.serialNumber} موجود مسبقاً`);
        }

        // إضافة الجهاز
        await tx.device.create({
          data: {
            serialNumber: item.serialNumber,
            modelId: parseInt(item.modelId),
            warehouseId: draftData.warehouseId,
            supplierId: draftData.supplierId,
            supplyOrderId: supplyOrder.id,
            status: 'available',
            condition: item.condition || 'new',
            price: item.price ? parseFloat(item.price) : null,
            warrantyExpiry: item.warrantyExpiry ? new Date(item.warrantyExpiry) : null,
            notes: item.notes
          }
        });
      }

      // 4. حذف المسودة بعد التحويل الناجح
      await prisma.$executeRaw`
        DELETE FROM "SupplyOrderDraft" 
        WHERE "draftId" = ${draftId} AND "userId" = ${userId}
      `;

      return supplyOrder;
    });

    console.log('✅ تم تحويل المسودة إلى أمر فعلي بنجاح:', result.orderNumber);

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء أمر التوريد بنجاح',
      orderId: result.id,
      orderNumber: result.orderNumber
    });

  } catch (error) {
    console.error('خطأ في تحويل المسودة:', error);
    
    // إرجاع رسالة خطأ واضحة
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'فشل في إنشاء أمر التوريد' }, { status: 500 });
  }
}
