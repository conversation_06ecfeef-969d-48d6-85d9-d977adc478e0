import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateUniqueId } from '@/lib/transaction-utils';

export async function POST(request: NextRequest) {
  try {
    // استخدام الدالة الجديدة بدون معاملة
    const supplyNumber = await generateUniqueId('supplyOrder', 'SUP-');

    return NextResponse.json({ supplyNumber });
  } catch (error) {
    console.error('خطأ في توليد رقم أمر التوريد:', error);
    return NextResponse.json(
      { error: 'فشل في توليد رقم أمر التوريد' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // استخدام الدالة الجديدة بدون معاملة
    const supplyNumber = await generateUniqueId('supplyOrder', 'SUP-');

    return NextResponse.json({ supplyNumber });
  } catch (error) {
    console.error('خطأ في توليد رقم أمر التوريد:', error);
    return NextResponse.json(
      { error: 'فشل في توليد رقم أمر التوريد' },
      { status: 500 }
    );
  }
}
