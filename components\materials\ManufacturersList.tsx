'use client';

import { useState, useEffect } from 'react';
import { useStore } from '@/context/store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Building2, ChevronRight, Smartphone } from 'lucide-react';
import type { Manufacturer, DeviceModel } from '@/lib/types';

interface ManufacturersListProps {
  onManufacturerSelect: (manufacturer: Manufacturer) => void;
  selectedManufacturer: Manufacturer | null;
}

export default function ManufacturersList({ 
  onManufacturerSelect, 
  selectedManufacturer 
}: ManufacturersListProps) {
  const { 
    manufacturers, 
    deviceModels, 
    fetchManufacturersData, 
    fetchDeviceModelsData,
    isLoading 
  } = useStore();

  useEffect(() => {
    // جلب بيانات الشركات والموديلات عند تحميل المكون
    fetchManufacturersData();
    fetchDeviceModelsData();
  }, [fetchManufacturersData, fetchDeviceModelsData]);

  // حساب عدد الموديلات لكل شركة
  const getModelCount = (manufacturerId: number) => {
    return deviceModels.filter(model => model.manufacturerId === manufacturerId).length;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            الشركات المصنعة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground mt-2">جاري تحميل الشركات...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          الشركات المصنعة
        </CardTitle>
      </CardHeader>
      <CardContent>
        {manufacturers.length === 0 ? (
          <div className="text-center py-8">
            <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">لا توجد شركات مصنعة</p>
          </div>
        ) : (
          <div className="space-y-2">
            {manufacturers.map((manufacturer) => {
              const modelCount = getModelCount(manufacturer.id);
              const isSelected = selectedManufacturer?.id === manufacturer.id;
              
              return (
                <Button
                  key={manufacturer.id}
                  variant={isSelected ? "default" : "outline"}
                  className={`w-full justify-between h-auto p-4 ${
                    isSelected ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                  }`}
                  onClick={() => onManufacturerSelect(manufacturer)}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-muted">
                      <Smartphone className="h-5 w-5" />
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{manufacturer.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {modelCount} موديل
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {modelCount}
                    </Badge>
                    <ChevronRight className="h-4 w-4" />
                  </div>
                </Button>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
