const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testModels() {
  try {
    console.log('Available Prisma models:');
    console.log(Object.keys(prisma));
    
    // Test different possible names
    const testNames = ['supplyOrder', 'supplyOrders', 'supply_orders', 'SupplyOrder'];
    
    for (const name of testNames) {
      if (prisma[name]) {
        console.log(`✓ Found model: ${name}`);
        try {
          const count = await prisma[name].count();
          console.log(`  - Count: ${count}`);
        } catch (error) {
          console.log(`  - Error: ${error.message}`);
        }
      } else {
        console.log(`✗ Model not found: ${name}`);
      }
    }
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testModels();
