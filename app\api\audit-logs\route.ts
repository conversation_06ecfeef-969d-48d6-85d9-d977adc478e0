import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction } from '@/lib/transaction-utils';

interface AuditLogRequest {
  userId: string;
  username: string;
  operation: string;
  details: string;
  tableName?: string;
  recordId?: string;
}

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية لعرض audit logs
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const auditLogs = await prisma.auditLog.findMany({
      orderBy: { timestamp: 'desc' },
      take: 1000 // تحديد عدد السجلات لتجنب استهلاك الذاكرة
    });

    return NextResponse.json(auditLogs);
  } catch (error) {
    console.error('Failed to fetch audit logs:', error);
    return NextResponse.json({ error: 'Failed to fetch audit logs' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { userId, username, operation, details, tableName, recordId }: AuditLogRequest = await request.json();

    // التحقق من البيانات المطلوبة
    if (!userId || !username || !operation || !details) {
      return NextResponse.json(
        { error: 'userId, username, operation, and details are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      const newLog = await tx.auditLog.create({
        data: {
          userId,
          username,
          operation,
          details,
          tableName,
          recordId,
          timestamp: new Date()
        },
      });
      return newLog;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create audit log:', error);
    return NextResponse.json({ error: 'Failed to create audit log' }, { status: 500 });
  }
}
