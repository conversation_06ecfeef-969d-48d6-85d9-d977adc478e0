"use client";

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  PrintExportButtons, 
  QuickPrint, 
  TablePrint, 
  ReportPrint 
} from '@/components/ui/print-export-buttons';
import { createSupplyTemplate, TemplateHelpers } from '@/lib/print-templates';

// مثال عملي لاستخدام النظام في قسم التوريد
export default function SupplyDepartmentExample() {
  // بيانات تجريبية لطلب توريد
  const [supplyOrder] = useState({
    id: 'PO-2024-001',
    supplier: {
      'اسم المورد': 'شركة التقنية المتقدمة',
      'رقم الهاتف': '0112345678',
      'البريد الإلكتروني': '<EMAIL>',
      'العنوان': 'الرياض، حي الملك فهد',
      'رقم السجل التجاري': '1010123456'
    },
    orderInfo: {
      'رقم الطلب': 'PO-2024-001',
      'تاريخ الطلب': '2024-01-15',
      'تاريخ التسليم المتوقع': '2024-01-25',
      'حالة الطلب': 'قيد المراجعة',
      'المسؤول': 'أحمد محمد علي',
      'القسم': 'المشتريات'
    },
    items: [
      ['iPhone 14 Pro Max 256GB', '10', '4,200', '42,000', 'اللون الأزرق الفاتح'],
      ['iPad Air 5th Gen 256GB', '5', '2,800', '14,000', 'اللون الرمادي الفلكي'],
      ['MacBook Air M2 512GB', '3', '5,500', '16,500', 'اللون الفضي'],
      ['AirPods Pro 2nd Gen', '15', '850', '12,750', 'مع علبة الشحن'],
      ['Apple Watch Series 9', '8', '1,800', '14,400', 'مقاس 45mm']
    ],
    timeline: [
      {
        title: 'تم إنشاء الطلب',
        description: 'تم إنشاء طلب التوريد من قبل قسم المشتريات',
        date: '2024-01-15',
        user: 'أحمد محمد علي'
      },
      {
        title: 'مراجعة المدير',
        description: 'تم إرسال الطلب للمدير للمراجعة والموافقة',
        date: '2024-01-15',
        user: 'نظام المشتريات'
      },
      {
        title: 'طلب عروض أسعار',
        description: 'تم طلب عروض أسعار من موردين متعددين',
        date: '2024-01-16',
        user: 'سارة أحمد'
      },
      {
        title: 'مقارنة العروض',
        description: 'تم مقارنة العروض واختيار أفضل عرض',
        date: '2024-01-17',
        user: 'علي حسن'
      }
    ]
  });

  // حساب الملخص المالي
  const financialSummary = React.useMemo(() => {
    const subtotal = supplyOrder.items.reduce((sum, item) => {
      return sum + parseFloat(item[3].replace(/,/g, ''));
    }, 0);
    
    const tax = subtotal * 0.15; // ضريبة 15%
    const total = subtotal + tax;

    return {
      'المبلغ الفرعي': TemplateHelpers.formatCurrency(subtotal),
      'الضريبة (15%)': TemplateHelpers.formatCurrency(tax),
      'المبلغ الإجمالي': TemplateHelpers.formatCurrency(total),
      'عدد الأصناف': supplyOrder.items.length.toString(),
      'إجمالي الكمية': supplyOrder.items.reduce((sum, item) => sum + parseInt(item[1]), 0).toString()
    };
  }, [supplyOrder.items]);

  // إنشاء بيانات الطباعة باستخدام القالب
  const supplyPrintData = createSupplyTemplate({
    supplierInfo: supplyOrder.supplier,
    orderInfo: { ...supplyOrder.orderInfo, ...financialSummary },
    items: supplyOrder.items,
    timeline: supplyOrder.timeline
  });

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          مثال قسم التوريد
        </h1>
        <p className="text-gray-600">
          مثال عملي لاستخدام نظام الطباعة والتصدير الموحد في قسم التوريد
        </p>
      </div>

      {/* معلومات الطلب */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>طلب التوريد رقم: {supplyOrder.id}</span>
            <div className="flex gap-2">
              {/* الطباعة الشاملة */}
              <PrintExportButtons
                data={supplyPrintData}
                options={{ 
                  fileName: `supply_order_${supplyOrder.id}`,
                  title: `طلب التوريد ${supplyOrder.id}`
                }}
                variant="compact"
              />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* معلومات المورد */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center justify-between">
              معلومات المورد
              <QuickPrint
                title="معلومات المورد"
                data={supplyOrder.supplier}
                fileName={`supplier_${supplyOrder.id}`}
                className="ml-2"
              />
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg">
              {Object.entries(supplyOrder.supplier).map(([key, value]) => (
                <div key={key}>
                  <span className="font-medium text-gray-700">{key}:</span>
                  <span className="mr-2">{value}</span>
                </div>
              ))}
            </div>
          </div>

          {/* تفاصيل الطلب */}
          <div>
            <h3 className="text-lg font-semibold mb-3">تفاصيل الطلب</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-blue-50 p-4 rounded-lg">
              {Object.entries(supplyOrder.orderInfo).map(([key, value]) => (
                <div key={key}>
                  <span className="font-medium text-blue-700">{key}:</span>
                  <span className="mr-2">{value}</span>
                </div>
              ))}
            </div>
          </div>

          {/* الأصناف المطلوبة */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center justify-between">
              الأصناف المطلوبة
              <TablePrint
                title="قائمة الأصناف المطلوبة"
                data={supplyOrder.items}
                columns={['الصنف', 'الكمية', 'السعر', 'الإجمالي', 'ملاحظات']}
                fileName={`items_${supplyOrder.id}`}
              />
            </h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border border-gray-300 p-3 text-right">الصنف</th>
                    <th className="border border-gray-300 p-3 text-center">الكمية</th>
                    <th className="border border-gray-300 p-3 text-center">السعر</th>
                    <th className="border border-gray-300 p-3 text-center">الإجمالي</th>
                    <th className="border border-gray-300 p-3 text-right">ملاحظات</th>
                  </tr>
                </thead>
                <tbody>
                  {supplyOrder.items.map((item, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="border border-gray-300 p-3">{item[0]}</td>
                      <td className="border border-gray-300 p-3 text-center">{item[1]}</td>
                      <td className="border border-gray-300 p-3 text-center">{item[2]} ريال</td>
                      <td className="border border-gray-300 p-3 text-center font-semibold">{item[3]} ريال</td>
                      <td className="border border-gray-300 p-3">{item[4]}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* الملخص المالي */}
          <div>
            <h3 className="text-lg font-semibold mb-3">الملخص المالي</h3>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(financialSummary).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center">
                    <span className="font-medium text-green-700">{key}:</span>
                    <span className="font-bold text-green-800">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* سجل المتابعة */}
          <div>
            <h3 className="text-lg font-semibold mb-3">سجل المتابعة</h3>
            <div className="space-y-4">
              {supplyOrder.timeline.map((event, index) => (
                <div key={index} className="flex items-start space-x-4 space-x-reverse">
                  <div className="flex-shrink-0 w-3 h-3 bg-blue-500 rounded-full mt-2"></div>
                  <div className="flex-1 bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold text-gray-800">{event.title}</h4>
                      <span className="text-sm text-gray-500">{event.date}</span>
                    </div>
                    <p className="text-gray-600 mb-2">{event.description}</p>
                    <p className="text-sm text-blue-600">بواسطة: {event.user}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* أمثلة أخرى للاستخدام */}
      <Card>
        <CardHeader>
          <CardTitle>أمثلة أخرى للطباعة</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* تقرير شامل */}
            <ReportPrint
              title="تقرير التوريد الشامل"
              subtitle={`طلب رقم: ${supplyOrder.id}`}
              info={supplyOrder.orderInfo}
              tableData={{
                title: 'الأصناف المطلوبة',
                data: supplyOrder.items,
                columns: ['الصنف', 'الكمية', 'السعر', 'الإجمالي', 'ملاحظات']
              }}
              timelineData={{
                title: 'سجل المتابعة',
                events: supplyOrder.timeline
              }}
              fileName={`comprehensive_report_${supplyOrder.id}`}
              className="w-full"
            />

            {/* طباعة سريعة للملخص */}
            <QuickPrint
              title="الملخص المالي"
              data={financialSummary}
              fileName={`financial_summary_${supplyOrder.id}`}
              className="w-full"
            />

            {/* طباعة قائمة الأصناف فقط */}
            <TablePrint
              title="قائمة الأصناف"
              data={supplyOrder.items}
              columns={['الصنف', 'الكمية', 'السعر', 'الإجمالي']}
              fileName={`items_list_${supplyOrder.id}`}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* معلومات الاستخدام */}
      <Card>
        <CardHeader>
          <CardTitle>كيفية تطبيق هذا في قسمك</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">خطوات التطبيق:</h4>
            <ol className="list-decimal list-inside space-y-2 text-blue-700">
              <li>استورد المكونات المطلوبة من <code>@/components/ui/print-export-buttons</code></li>
              <li>استورد القالب المناسب من <code>@/lib/print-templates</code></li>
              <li>حضر بياناتك بالتنسيق المطلوب</li>
              <li>استخدم المكونات في صفحتك</li>
              <li>خصص أسماء الملفات والعناوين حسب احتياجاتك</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
