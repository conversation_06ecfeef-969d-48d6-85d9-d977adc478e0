#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanAndRebuildDatabase() {
  try {
    console.log('🧹 تنظيف وإعادة بناء قاعدة البيانات...');

    // 1. حذف جميع البيانات
    console.log('\n🗑️ حذف جميع البيانات الموجودة...');
    
    await prisma.auditLog.deleteMany();
    await prisma.supplyOrderItem.deleteMany();
    await prisma.supplyOrder.deleteMany();
    await prisma.device.deleteMany();
    await prisma.deviceModel.deleteMany();
    await prisma.manufacturer.deleteMany();
    await prisma.warehouse.deleteMany();
    await prisma.supplier.deleteMany();
    await prisma.client.deleteMany();
    
    console.log('✅ تم حذف جميع البيانات');

    // 2. إنشاء الشركات المصنعة
    console.log('\n🏭 إنشاء الشركات المصنعة...');
    
    const manufacturers = [
      { name: 'Apple' },
      { name: 'Samsung' },
      { name: 'Huawei' },
      { name: 'Xiaomi' },
      { name: 'Oppo' }
    ];

    const createdManufacturers = [];
    for (const manufacturer of manufacturers) {
      const created = await prisma.manufacturer.create({
        data: manufacturer
      });
      createdManufacturers.push(created);
      console.log(`✅ تم إنشاء شركة: ${created.name}`);
    }

    // 3. إنشاء الموديلات
    console.log('\n📱 إنشاء موديلات الأجهزة...');
    
    const models = [
      { name: 'iPhone 15 Pro', manufacturerId: createdManufacturers[0].id, category: 'هاتف ذكي' },
      { name: 'iPhone 14', manufacturerId: createdManufacturers[0].id, category: 'هاتف ذكي' },
      { name: 'Galaxy S24', manufacturerId: createdManufacturers[1].id, category: 'هاتف ذكي' },
      { name: 'Galaxy A55', manufacturerId: createdManufacturers[1].id, category: 'هاتف ذكي' },
      { name: 'P60 Pro', manufacturerId: createdManufacturers[2].id, category: 'هاتف ذكي' }
    ];

    for (const model of models) {
      const created = await prisma.deviceModel.create({
        data: model
      });
      console.log(`✅ تم إنشاء موديل: ${created.name}`);
    }

    // 4. إنشاء المخازن
    console.log('\n📦 إنشاء المخازن...');
    
    const warehouses = [
      { name: 'المخزن الرئيسي', type: 'رئيسي', location: 'المقر الرئيسي - صنعاء' },
      { name: 'مخزن الفرع الشمالي', type: 'فرعي', location: 'الفرع الشمالي - عدن' },
      { name: 'مخزن الفرع الجنوبي', type: 'فرعي', location: 'الفرع الجنوبي - تعز' }
    ];

    const createdWarehouses = [];
    for (const warehouse of warehouses) {
      const created = await prisma.warehouse.create({
        data: warehouse
      });
      createdWarehouses.push(created);
      console.log(`✅ تم إنشاء مخزن: ${created.name}`);
    }

    // 5. إنشاء الموردين
    console.log('\n🚚 إنشاء الموردين...');
    
    const suppliers = [
      { name: 'مورد الأجهزة الذكية', phone: '777123456', email: '<EMAIL>' },
      { name: 'مورد التقنية المتقدمة', phone: '777789123', email: '<EMAIL>' }
    ];

    const createdSuppliers = [];
    for (const supplier of suppliers) {
      const created = await prisma.supplier.create({
        data: supplier
      });
      createdSuppliers.push(created);
      console.log(`✅ تم إنشاء مورد: ${created.name}`);
    }

    // 6. إنشاء العملاء
    console.log('\n👥 إنشاء العملاء...');
    
    const clients = [
      { name: 'أحمد محمد', phone: '777111222', email: '<EMAIL>' },
      { name: 'فاطمة علي', phone: '777333444', email: '<EMAIL>' }
    ];

    for (const client of clients) {
      const created = await prisma.client.create({
        data: client
      });
      console.log(`✅ تم إنشاء عميل: ${created.name}`);
    }

    // 7. إنشاء أمر توريد تجريبي
    console.log('\n📋 إنشاء أمر توريد تجريبي...');
    
    const supplyOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: 'SUP-001',
        supplierId: createdSuppliers[0].id,
        supplyDate: new Date().toISOString(),
        warehouseId: createdWarehouses[0].id,
        employeeName: 'موظف النظام',
        notes: 'أمر توريد تجريبي',
        status: 'completed'
      }
    });

    console.log(`✅ تم إنشاء أمر توريد: ${supplyOrder.supplyOrderId}`);

    // 8. إنشاء عناصر أمر التوريد والأجهزة
    console.log('\n📱 إنشاء أجهزة تجريبية...');
    
    const devices = [
      { imei: '111111111111111', model: 'iPhone 15 Pro', manufacturer: 'Apple', condition: 'جديد', price: 4500 },
      { imei: '222222222222222', model: 'Galaxy S24', manufacturer: 'Samsung', condition: 'جديد', price: 3500 },
      { imei: '333333333333333', model: 'iPhone 14', manufacturer: 'Apple', condition: 'مستخدم', price: 3000 }
    ];

    for (const deviceData of devices) {
      // إنشاء عنصر في أمر التوريد
      await prisma.supplyOrderItem.create({
        data: {
          supplyOrderId: supplyOrder.id,
          imei: deviceData.imei,
          model: deviceData.model,
          manufacturer: deviceData.manufacturer,
          condition: deviceData.condition
        }
      });

      // إنشاء الجهاز في المخزون
      await prisma.device.create({
        data: {
          id: deviceData.imei,
          model: `${deviceData.manufacturer} ${deviceData.model}`,
          status: 'متاح للبيع',
          storage: '128GB',
          price: deviceData.price,
          condition: deviceData.condition,
          warehouseId: createdWarehouses[0].id,
          supplierId: createdSuppliers[0].id
        }
      });

      console.log(`✅ تم إنشاء جهاز: ${deviceData.imei} - ${deviceData.model}`);
    }

    console.log('\n✅ تم الانتهاء من تنظيف وإعادة بناء قاعدة البيانات بنجاح!');

    // 9. عرض الإحصائيات النهائية
    console.log('\n📊 إحصائيات قاعدة البيانات:');
    console.log('═'.repeat(50));
    
    const stats = {
      manufacturers: await prisma.manufacturer.count(),
      models: await prisma.deviceModel.count(),
      warehouses: await prisma.warehouse.count(),
      suppliers: await prisma.supplier.count(),
      clients: await prisma.client.count(),
      supplyOrders: await prisma.supplyOrder.count(),
      devices: await prisma.device.count()
    };

    console.log(`🏭 الشركات المصنعة: ${stats.manufacturers}`);
    console.log(`📱 موديلات الأجهزة: ${stats.models}`);
    console.log(`📦 المخازن: ${stats.warehouses}`);
    console.log(`🚚 الموردين: ${stats.suppliers}`);
    console.log(`👥 العملاء: ${stats.clients}`);
    console.log(`📋 أوامر التوريد: ${stats.supplyOrders}`);
    console.log(`📱 الأجهزة: ${stats.devices}`);

    console.log('\n🎉 قاعدة البيانات جاهزة للاستخدام!');

  } catch (error) {
    console.error('❌ خطأ في تنظيف وإعادة بناء قاعدة البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanAndRebuildDatabase();
