/**
 * سكريبت لإصلاح مشكلة المصادقة والصلاحيات بعد تحديث بيانات المستخدم
 * يستخدم Prisma للتعامل مع قاعدة البيانات
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🔧 بدء إصلاح مشكلة المصادقة والصلاحيات...');

  try {
    // البحث عن جميع المستخدمين الإداريين
    console.log('📋 فحص المستخدمين الإداريين الحاليين...');
    
    const adminUsers = await prisma.user.findMany({
      where: {
        OR: [
          { role: 'admin' },
          { username: 'admin' },
          { name: { contains: 'مدير' } }
        ]
      }
    });

    console.log(`📊 تم العثور على ${adminUsers.length} مستخدم إداري:`);
    adminUsers.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id}, الاسم: "${user.name}", اسم المستخدم: "${user.username}", الإيميل: "${user.email}", الصلاحية: "${user.role}", الحالة: "${user.status}"`);
    });

    if (adminUsers.length === 0) {
      console.log('⚠️  لم يتم العثور على أي مستخدم إداري. سيتم إنشاء مستخدم إداري افتراضي...');
      await createDefaultAdmin();
    } else {
      console.log('🔍 فحص المستخدمين لإصلاح أي مشاكل...');
      await fixUsersIfNeeded(adminUsers);
    }

    // التحقق النهائي
    await verifyFixes();

  } catch (error) {
    console.error('❌ خطأ في تشغيل السكريبت:', error);
  } finally {
    await prisma.$disconnect();
    console.log('✅ تم إغلاق الاتصال بقاعدة البيانات');
  }
}

async function createDefaultAdmin() {
  try {
    const adminData = {
      username: 'admin',
      email: '<EMAIL>',
      name: 'مدير النظام',
      role: 'admin',
      status: 'Active'
    };

    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: adminData,
      create: adminData
    });

    console.log('✅ تم إنشاء/تحديث المستخدم الإداري بنجاح (ID:', admin.id, ')');
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم الإداري:', error);
  }
}

async function fixUsersIfNeeded(users: any[]) {
  let fixesCompleted = 0;

  for (const user of users) {
    try {
      let needsUpdate = false;
      const updateData: any = {};

      // التأكد من أن المستخدم الإداري له username صحيح
      if (user.role === 'admin' && (!user.username || user.username === '')) {
        updateData.username = 'admin';
        needsUpdate = true;
        console.log(`🔧 سيتم تحديث username للمستخدم "${user.name}" (ID: ${user.id})`);
      }

      // التأكد من أن المستخدم الإداري له email صحيح
      if (user.role === 'admin' && (!user.email || user.email === '')) {
        updateData.email = '<EMAIL>';
        needsUpdate = true;
        console.log(`🔧 سيتم تحديث email للمستخدم "${user.name}" (ID: ${user.id})`);
      }

      // التأكد من أن الحالة نشطة
      if (user.status !== 'Active') {
        updateData.status = 'Active';
        needsUpdate = true;
        console.log(`🔧 سيتم تحديث الحالة للمستخدم "${user.name}" (ID: ${user.id})`);
      }

      // التأكد من أن الصلاحية admin
      if (user.name && user.name.includes('مدير') && user.role !== 'admin') {
        updateData.role = 'admin';
        needsUpdate = true;
        console.log(`🔧 سيتم تحديث الصلاحية للمستخدم "${user.name}" (ID: ${user.id})`);
      }

      if (needsUpdate) {
        await prisma.user.update({
          where: { id: user.id },
          data: updateData
        });
        console.log(`✅ تم تحديث المستخدم "${user.name}" بنجاح`);
        fixesCompleted++;
      }
    } catch (error) {
      console.error(`❌ خطأ في تحديث المستخدم "${user.name}":`, error);
    }
  }

  if (fixesCompleted === 0) {
    console.log('✅ جميع المستخدمين الإداريين في حالة جيدة، لا توجد حاجة لإصلاحات');
  } else {
    console.log(`🎉 تم الانتهاء من ${fixesCompleted} إصلاح!`);
  }
}

async function verifyFixes() {
  console.log('🔍 التحقق من النتائج النهائية...');
  
  try {
    const finalAdminUsers = await prisma.user.findMany({
      where: {
        OR: [
          { role: 'admin' },
          { username: 'admin' },
          { name: { contains: 'مدير' } }
        ]
      }
    });

    console.log(`📊 المستخدمون الإداريون بعد الإصلاح (${finalAdminUsers.length}):`);
    finalAdminUsers.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id}, الاسم: "${user.name}", اسم المستخدم: "${user.username}", الإيميل: "${user.email}", الصلاحية: "${user.role}", الحالة: "${user.status}"`);
    });
    
    // إعطاء توصيات
    console.log('\n💡 توصيات:');
    console.log('1. تأكد من أن التطبيق يستخدم بيانات المستخدم المحدثة');
    console.log('2. قم بإعادة تشغيل الخادم للتأكد من تطبيق التغييرات');
    console.log('3. إذا استمرت المشكلة، تحقق من آلية إنشاء التوكن في النظام');
    console.log('4. امسح cache المتصفح وتحقق من console المطور');
  } catch (error) {
    console.error('❌ خطأ في التحقق النهائي:', error);
  }
}

// تشغيل السكريبت
main()
  .catch((error) => {
    console.error('❌ خطأ غير متوقع:', error);
    process.exit(1);
  });
