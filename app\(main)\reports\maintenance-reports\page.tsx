
'use client';

import { useState, useMemo } from 'react';
import { useStore } from '@/context/store';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { format, differenceInDays } from 'date-fns';
import {
  Printer,
  FileDown,
  TrendingUp,
  AlertCircle,
  Wrench,
  PackageX,
  Clock,
  UserCheck,
  Puzzle,
  BarChart,
} from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MaintenanceLog, Device, SystemSettings, User, EmployeeRequest } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { exportDataToPDF } from '@/lib/export-utils/html-to-pdf';
import {
  BarChart as RechartsBarChart,
  Bar as RechartsBar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';


export default function MaintenanceReports() {
  const {
    maintenanceHistory = [],
    maintenanceOrders = [],
    employeeRequests = [],
    users = [],
  } = useStore();

  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    employee: 'all',
  });

  const maintenanceEmployees = useMemo(() => {
    const employeeIds = new Set(maintenanceOrders.map(o => o.maintenanceEmployeeId));
    return users.filter(u => u.id === Number(employeeIds.values().next().value) || u.role === 'فني صيانة');
  }, [maintenanceOrders, users]);

  const filteredMaintenanceLogs = useMemo(() => {
    return maintenanceHistory.filter((log) => {
      const repairDate = new Date(log.repairDate);
      if (filters.dateFrom && repairDate < new Date(filters.dateFrom)) return false;
      if (filters.dateTo) {
        const toDate = new Date(filters.dateTo);
        toDate.setDate(toDate.getDate() + 1);
        if (repairDate > toDate) return false;
      }

      const order = maintenanceOrders.find(o => (Array.isArray(o.items) && o.items.some(i => i.id === log.deviceId)));
      if (filters.employee !== 'all' && (!order || order.maintenanceEmployeeId?.toString() !== filters.employee)) {
        return false;
      }
      return true;
    });
  }, [maintenanceHistory, filters, maintenanceOrders]);
  
  const sparePartRequests = useMemo(() => {
      return employeeRequests.filter(req => req.requestType === 'طلب قطع غيار');
  }, [employeeRequests]);


  const stats = useMemo(() => {
    const acknowledgedLogs = filteredMaintenanceLogs.filter(
      (log) => log.status === 'acknowledged'
    );
    const repaired = acknowledgedLogs.filter(
      (log) => log.result === 'Repaired'
    );
    const defective = acknowledgedLogs.filter(
      (log) => log.result === 'Unrepairable-Defective'
    );
    const damaged = acknowledgedLogs.filter(
      (log) => log.result === 'Unrepairable-Damaged'
    );
    const totalProcessed = repaired.length + defective.length + damaged.length;
    const successRate = totalProcessed > 0 ? Math.round((repaired.length / totalProcessed) * 100) : 0;
    
    const totalRepairTime = repaired.reduce((acc, log) => {
        if(log.acknowledgedDate) {
            return acc + differenceInDays(new Date(log.acknowledgedDate), new Date(log.repairDate));
        }
        return acc;
    }, 0);
    const avgRepairTime = repaired.length > 0 ? (totalRepairTime / repaired.length).toFixed(1) : 0;
    
    const commonFaults = [...defective, ...damaged].reduce((acc, log) => {
        const fault = log.notes || 'غير محدد';
        acc[fault] = (acc[fault] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    return { 
        repaired: repaired.length, 
        defective: defective.length, 
        damaged: damaged.length, 
        successRate,
        avgRepairTime,
        commonFaults: Object.entries(commonFaults).sort((a,b) => b[1] - a[1]).slice(0, 5),
    };
  }, [filteredMaintenanceLogs]);
  
  const employeePerformance = useMemo(() => {
    const performanceData: Record<string, { repaired: number, total: number, totalTime: number }> = {};

    filteredMaintenanceLogs.forEach(log => {
      const order = maintenanceOrders.find(o => (Array.isArray(o.items) && o.items.some(i => i.id === log.deviceId)));
      const employee = users.find(u => u.id === order?.maintenanceEmployeeId);

      if (employee) {
        if (!performanceData[employee.name]) {
          performanceData[employee.name] = { repaired: 0, total: 0, totalTime: 0 };
        }
        performanceData[employee.name].total++;
        if (log.result === 'Repaired') {
          performanceData[employee.name].repaired++;
          if (log.acknowledgedDate) {
              performanceData[employee.name].totalTime += differenceInDays(new Date(log.acknowledgedDate), new Date(log.repairDate));
          }
        }
      }
    });

    return Object.entries(performanceData).map(([name, data]) => ({
      name,
      repaired: data.repaired,
      total: data.total,
      successRate: data.total > 0 ? Math.round((data.repaired / data.total) * 100) : 0,
      avgTime: data.repaired > 0 ? (data.totalTime / data.repaired).toFixed(1) : 0
    })).sort((a,b) => b.repaired - a.repaired);

  }, [filteredMaintenanceLogs, maintenanceOrders, users]);

  return (
    <>
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>فلاتر تقرير الصيانة</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
                <Label htmlFor="dateFrom">من تاريخ</Label>
                <Input
                id="dateFrom"
                type="date"
                value={filters.dateFrom}
                onChange={(e) => setFilters(f => ({ ...f, dateFrom: e.target.value }))}
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="dateTo">إلى تاريخ</Label>
                <Input
                id="dateTo"
                type="date"
                value={filters.dateTo}
                onChange={(e) => setFilters(f => ({ ...f, dateTo: e.target.value }))}
                />
            </div>
            <div className="space-y-2">
              <Label>الموظف</Label>
              <Select
                dir="rtl"
                value={filters.employee}
                onValueChange={(value) => setFilters(f => ({...f, employee: value}))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر الموظف..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">كل الموظفين</SelectItem>
                   {maintenanceEmployees.map((employee) => (
                    <SelectItem key={employee.id} value={employee.id.toString()}>
                      {employee.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">نسبة نجاح الإصلاحات</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{stats.successRate}%</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">متوسط وقت الصيانة</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{stats.avgRepairTime} يوم</p>
            </CardContent>
          </Card>
           <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">طلبات قطع غيار</CardTitle>
              <Puzzle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{sparePartRequests.length}</p>
            </CardContent>
          </Card>
        </div>
        
        <div className="grid gap-4 lg:grid-cols-2">
            <Card>
                <CardHeader>
                    <CardTitle>تقرير أداء الموظفين</CardTitle>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>الموظف</TableHead>
                                <TableHead>الإجمالي</TableHead>
                                <TableHead>تم إصلاحها</TableHead>
                                <TableHead>نسبة النجاح</TableHead>
                                <TableHead>متوسط الوقت</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {employeePerformance.map(emp => (
                                <TableRow key={emp.name}>
                                    <TableCell>{emp.name}</TableCell>
                                    <TableCell>{emp.total}</TableCell>
                                    <TableCell>{emp.repaired}</TableCell>
                                    <TableCell>{emp.successRate}%</TableCell>
                                    <TableCell>{emp.avgTime} يوم</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
            <Card>
                <CardHeader>
                    <CardTitle>تقرير الأعطال الشائعة</CardTitle>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>العطل</TableHead>
                                <TableHead>عدد المرات</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {stats.commonFaults.map(([fault, count]) => (
                                <TableRow key={fault}>
                                    <TableCell>{fault}</TableCell>
                                    <TableCell>{count}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>سجل عمليات الصيانة</CardTitle>
          </CardHeader>
          <CardContent>
             <ScrollArea className="h-[400px]">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>رقم الأمر</TableHead>
                            <TableHead>الجهاز</TableHead>
                            <TableHead>التاريخ</TableHead>
                            <TableHead>النتيجة</TableHead>
                            <TableHead>الموظف المسؤول</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredMaintenanceLogs.length === 0 ? (
                             <TableRow>
                                <TableCell colSpan={5} className="h-24 text-center">
                                    لا توجد سجلات تطابق الفلاتر المحددة.
                                </TableCell>
                            </TableRow>
                        ) : (
                            filteredMaintenanceLogs.map((log) => {
                                const order = maintenanceOrders.find(o => (Array.isArray(o.items) && o.items.some(i => i.id === log.deviceId)));
                                const employee = users.find(u => u.id === order?.maintenanceEmployeeId);
                                return(
                                <TableRow key={`${log.deviceId}-${log.repairDate}`}>
                                    <TableCell>{order?.orderNumber || 'N/A'}</TableCell>
                                    <TableCell>{log.model} ({log.deviceId})</TableCell>
                                    <TableCell>{new Date(log.repairDate).toLocaleDateString('ar-EG')}</TableCell>
                                    <TableCell>
                                        <Badge variant={log.result === 'Repaired' ? 'default' : 'destructive'}>
                                        {log.result === 'Repaired' ? 'تم الإصلاح' : 'لم يتم الإصلاح'}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>{employee?.name || 'غير محدد'}</TableCell>
                                </TableRow>
                            )})
                        )}
                    </TableBody>
                </Table>
             </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </>
  );
}