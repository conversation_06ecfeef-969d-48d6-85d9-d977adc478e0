# 🚀 الدليل المبسط لتطبيق Store API

## 📋 **خطة التطبيق المرحلية**

---

## 🎯 **المرحلة الثانية: Warehouses Page**

### **المشكلة الحالية:**
```typescript
// ❌ استدعاء fetch مباشر
const fetchWarehouses = async () => {
  setIsLoading(true);
  const response = await fetch('/api/warehouses-simple');
  const data = await response.json();
  setWarehouses(data);
  setIsLoading(false);
};
```

### **الحل المحسّن:**
```typescript
// ✅ استخدام Store API
const WarehousesPageOptimized = () => {
  const { warehouses, isLoading, fetchWarehousesData } = useStore();
  
  // إحصائيات محسّنة
  const warehouseStats = useMemo(() => ({
    total: warehouses.length,
    active: warehouses.filter(w => w.status === 'active').length,
    inactive: warehouses.filter(w => w.status === 'inactive').length
  }), [warehouses]);

  // تحديث البيانات عند الحاجة
  const refreshData = useCallback(async () => {
    await fetchWarehousesData({ forceRefresh: true });
  }, [fetchWarehousesData]);

  return (
    <div>
      <h1>المخازن ({warehouseStats.total})</h1>
      {/* باقي المحتوى */}
    </div>
  );
};
```

---

## 🎯 **المرحلة الثالثة: Audit Logs Page**

### **المشكلة الحالية:**
```typescript
// ❌ استدعاء fetch مباشر
const fetchAuditLogs = async () => {
  const res = await fetch('/api/audit-logs');
  const data = await res.json();
  setAuditLogs(data);
};
```

### **الحل المحسّن:**
```typescript
// ✅ استخدام Store API
const AuditLogsPageOptimized = () => {
  const { auditLogs, fetchAuditLogsData, isLoading } = useStore();
  const [dateFilter, setDateFilter] = useState('');
  const [userFilter, setUserFilter] = useState('all');
  
  // تصفية محسّنة
  const filteredLogs = useMemo(() => {
    return auditLogs.filter(log => {
      if (dateFilter && log.timestamp < dateFilter) return false;
      if (userFilter !== 'all' && log.userId !== userFilter) return false;
      return true;
    });
  }, [auditLogs, dateFilter, userFilter]);

  return (
    <div>
      <h1>سجلات المراجعة ({filteredLogs.length})</h1>
      {/* عرض السجلات المفلترة */}
    </div>
  );
};
```

---

## 🎯 **المرحلة الرابعة: Grading Page**

### **المشكلة الحالية:**
```typescript
// ❌ استدعاء fetch مباشر
const fetchEvaluationData = async () => {
  const response = await fetch('/api/evaluations');
  const data = await response.json();
  setEvaluations(data);
};
```

### **الحل المحسّن:**
```typescript
// ✅ استخدام Store API
const GradingPageOptimized = () => {
  const { 
    evaluationOrders, 
    devices, 
    fetchEvaluationOrdersData,
    isLoading 
  } = useStore();
  
  // ربط البيانات بذكاء
  const evaluationsWithDevices = useMemo(() => {
    return evaluationOrders.map(evaluation => ({
      ...evaluation,
      deviceInfo: devices.find(d => d.id === evaluation.deviceId)
    }));
  }, [evaluationOrders, devices]);

  // إحصائيات التقييم
  const gradingStats = useMemo(() => ({
    total: evaluationOrders.length,
    pending: evaluationOrders.filter(e => e.status === 'pending').length,
    completed: evaluationOrders.filter(e => e.status === 'completed').length
  }), [evaluationOrders]);

  return (
    <div>
      <h1>تقييم الأجهزة</h1>
      <div className="stats">
        <span>المجموع: {gradingStats.total}</span>
        <span>قيد المراجعة: {gradingStats.pending}</span>
        <span>مكتمل: {gradingStats.completed}</span>
      </div>
      {/* باقي المحتوى */}
    </div>
  );
};
```

---

## 🎯 **المرحلة الخامسة: Settings Page**

### **المشكلة الحالية:**
```typescript
// ❌ استدعاءات متعددة
const fetchSettings = async () => {
  const [usersRes, rolesRes] = await Promise.all([
    fetch('/api/users'),
    fetch('/api/roles')
  ]);
  // معالجة معقدة
};
```

### **الحل المحسّن:**
```typescript
// ✅ استخدام Store API
const SettingsPageOptimized = () => {
  const { 
    users, 
    roles, 
    fetchUsersData, 
    fetchRolesData,
    isLoading 
  } = useStore();
  
  // إعدادات المستخدمين
  const userSettings = useMemo(() => {
    return users.map(user => ({
      ...user,
      roleName: roles.find(r => r.id === user.roleId)?.name || 'غير محدد'
    }));
  }, [users, roles]);

  // دالة حفظ الإعدادات
  const saveSettings = useCallback(async (settings) => {
    // حفظ الإعدادات
    await fetchUsersData({ forceRefresh: true }); // تحديث البيانات
  }, [fetchUsersData]);

  return (
    <div>
      <h1>الإعدادات</h1>
      {/* واجهة الإعدادات */}
    </div>
  );
};
```

---

## 📊 **قالب عام للتطبيق**

### **الخطوات الأساسية:**

#### **1. استبدال الاستيرادات:**
```typescript
// ❌ احذف هذا
import { fetchData } from '@/lib/data-fetcher';

// ✅ أضف هذا
import { useStore } from '@/context/store';
```

#### **2. استبدال useState:**
```typescript
// ❌ احذف هذا
const [data, setData] = useState([]);
const [isLoading, setIsLoading] = useState(false);

// ✅ أضف هذا
const { data, isLoading, fetchDataFunction } = useStore();
```

#### **3. حذف fetch functions:**
```typescript
// ❌ احذف جميع دوال fetch
const fetchData = async () => {
  // حذف هذا بالكامل
};

useEffect(() => {
  fetchData(); // احذف هذا أيضاً
}, []);
```

#### **4. إضافة معالجة محسّنة:**
```typescript
// ✅ أضف معالجة ذكية
const processedData = useMemo(() => {
  // معالجة البيانات هنا
  return data.filter(item => /* شروط التصفية */);
}, [data, filters]);

const handleAction = useCallback((params) => {
  // دوال التفاعل
}, [dependencies]);
```

---

## 🔧 **أدوات المساعدة**

### **قالب سريع للنسخ:**
```typescript
'use client';

import { useMemo, useCallback, useState } from 'react';
import { useStore } from '@/context/store';

export default function OptimizedPage() {
  // 1. استخدام Store API
  const { 
    dataProperty, 
    isLoading, 
    fetchDataFunction 
  } = useStore();

  // 2. حالات محلية
  const [filters, setFilters] = useState({});
  const [searchQuery, setSearchQuery] = useState('');

  // 3. معالجة البيانات
  const processedData = useMemo(() => {
    return dataProperty.filter(item => {
      // منطق التصفية
      return true;
    });
  }, [dataProperty, filters, searchQuery]);

  // 4. دوال التفاعل
  const handleSearch = useCallback((query) => {
    setSearchQuery(query);
  }, []);

  const handleFilter = useCallback((newFilters) => {
    setFilters(newFilters);
  }, []);

  // 5. عرض المحتوى
  if (isLoading) {
    return <div>جاري التحميل...</div>;
  }

  return (
    <div>
      <h1>الصفحة المحسّنة</h1>
      {/* المحتوى */}
    </div>
  );
}
```

---

## ✅ **قائمة التحقق السريعة**

### **لكل صفحة:**
- [ ] **حذف** جميع استيرادات `data-fetcher`
- [ ] **إضافة** `useStore()` hook
- [ ] **حذف** جميع `useState` للبيانات
- [ ] **حذف** جميع دوال `fetch`
- [ ] **حذف** `useEffect` للتحميل
- [ ] **إضافة** `useMemo` للمعالجة
- [ ] **إضافة** `useCallback` للدوال
- [ ] **اختبار** الوظائف الأساسية

### **بعد التطبيق:**
- [ ] **اختبار** تحميل البيانات
- [ ] **اختبار** التصفية والبحث
- [ ] **اختبار** التفاعل مع UI
- [ ] **قياس** تحسن الأداء
- [ ] **مراجعة** Console للأخطاء

---

## 🎯 **الأولويات**

### **أولوية عالية (ابدأ بها):**
1. **Track Page** ✅ (تم إنشاء النسخة المحسّنة)
2. **Warehouses Page** 
3. **Audit Logs Page**

### **أولوية متوسطة:**
4. **Grading Page**
5. **Settings Page**
6. **Messaging Page**

### **أولوية منخفضة:**
7. **Maintenance Transfer**
8. **Inventory Backup**

---

## 🚀 **ابدأ الآن!**

**الخطوة التالية:** اختر إحدى الصفحات واتبع القالب أعلاه. ابدأ بـ **Warehouses Page** لأنها الأبسط!

**تذكر:** صفحات التقارير هي المرجع الذهبي - ارجع إليها دائماً! 🏆
