const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestDevices() {
  try {
    console.log('Creating test devices...');
    
    const testDevices = [
      {
        id: 'DEV001',
        model: 'iPhone 14 Pro',
        status: 'متاح للبيع',
        storage: '128GB',
        price: 1200,
        condition: 'جديد',
        dateAdded: new Date().toISOString()
      },
      {
        id: 'DEV002', 
        model: 'Samsung Galaxy S23',
        status: 'متاح للبيع',
        storage: '256GB',
        price: 1000,
        condition: 'جديد',
        dateAdded: new Date().toISOString()
      },
      {
        id: 'DEV003',
        model: 'iPad Air',
        status: 'متاح للبيع', 
        storage: '64GB',
        price: 600,
        condition: 'مستعمل',
        dateAdded: new Date().toISOString()
      },
      {
        id: 'DEV004',
        model: 'MacBook Pro',
        status: 'متاح للبيع',
        storage: '512GB',
        price: 2500,
        condition: 'جديد',
        dateAdded: new Date().toISOString()
      },
      {
        id: 'DEV005',
        model: 'Apple Watch',
        status: 'متاح للبيع',
        storage: '32GB',
        price: 400,
        condition: 'مستعمل',
        dateAdded: new Date().toISOString()
      }
    ];
    
    for (const device of testDevices) {
      try {
        await prisma.device.upsert({
          where: { id: device.id },
          update: device,
          create: device
        });
        console.log(`Created device: ${device.id} - ${device.model}`);
      } catch (error) {
        console.log(`Device ${device.id} already exists or error:`, error.message);
      }
    }
    
    console.log('Test devices creation completed');
    
    // Check total devices
    const totalDevices = await prisma.device.count();
    console.log(`Total devices in database: ${totalDevices}`);
    
  } catch (error) {
    console.error('Error creating test devices:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestDevices();
