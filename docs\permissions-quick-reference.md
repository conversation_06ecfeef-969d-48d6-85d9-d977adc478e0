# ⚡ مرجع سريع لتطبيق الصلاحيات

## 🎯 الهدف
تطبيق نظام الصلاحيات على 17 صفحة غير محمية في التطبيق

## 📊 الوضع الحالي
- **الصفحات المحمية**: 3/20 (15%)
- **المخاطر الأمنية**: عالية جداً
- **الأولوية**: حرجة

---

## 🚀 خطوات التطبيق السريع

### 1. الاستيرادات (نسخ ولصق في كل صفحة)
```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';
```

### 2. القالب الأساسي
```typescript
export default function PageName() {
  const { canCreate, canEdit, canDelete } = usePermission('pageKey');

  return (
    <PermissionGuard pageKey="pageKey">
      {/* المحتوى الحالي هنا */}
      
      <ActionGuard pageKey="pageKey" action="create">
        <Button>إنشاء</Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

---

## 📋 قائمة الصفحات والمفاتيح

| الصفحة | المسار | pageKey | الأولوية |
|---------|--------|---------|----------|
| المستخدمين | `/users/page.tsx` | `users` | 🔴 حرجة |
| الإعدادات | `/settings/page.tsx` | `settings` | 🔴 حرجة |
| المبيعات | `/sales/page.tsx` | `sales` | 🔴 حرجة |
| المخزون | `/inventory/page.tsx` | `inventory` | 🔴 حرجة |
| التوريد | `/supply/page.tsx` | `supply` | 🟡 متوسطة |
| المرتجعات | `/returns/page.tsx` | `returns` | 🟡 متوسطة |
| العملاء | `/clients/page.tsx` | `clients` | 🟡 متوسطة |
| المخازن | `/warehouses/page.tsx` | `warehouses` | 🟡 متوسطة |
| التتبع | `/track/page.tsx` | `track` | 🟢 عادية |
| التقييم | `/grading/page.tsx` | `grading` | 🟢 عادية |
| الجرد | `/stocktaking/page.tsx` | `stocktaking` | 🟢 عادية |
| التقارير | `/reports/page.tsx` | `reports` | 🟢 عادية |
| الرسائل | `/messaging/page.tsx` | `messaging` | 🔵 إضافية |
| الطلبات | `/requests/page.tsx` | `requests` | 🔵 إضافية |
| قبول الأجهزة | `/accept-devices/page.tsx` | `acceptDevices` | 🔵 إضافية |
| أداة التسعير | `/pricing/page.tsx` | `pricing` | 🔵 إضافية |
| التحويل المخزني | `/warehouse-transfer/page.tsx` | `warehouseTransfer` | 🔵 إضافية |

---

## ⚡ أمثلة سريعة

### صفحة بسيطة (عرض فقط)
```typescript
import { PermissionGuard } from '@/components/PermissionGuard';

export default function TrackPage() {
  return (
    <PermissionGuard pageKey="track">
      {/* المحتوى الحالي */}
    </PermissionGuard>
  );
}
```

### صفحة متوسطة (مع أزرار)
```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function SalesPage() {
  return (
    <PermissionGuard pageKey="sales">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="sales" action="create">
        <Button onClick={handleCreate}>إنشاء</Button>
      </ActionGuard>
      
      <ActionGuard pageKey="sales" action="edit">
        <Button onClick={handleEdit}>تعديل</Button>
      </ActionGuard>
      
      <ActionGuard pageKey="sales" action="delete">
        <Button onClick={handleDelete}>حذف</Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

### صفحة متقدمة (مع صلاحيات خاصة)
```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

export default function ReturnsPage() {
  const { canAcceptWithoutWarranty } = usePermission('returns');

  return (
    <PermissionGuard pageKey="returns">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="returns" action="create">
        <Button>إنشاء مرتجع</Button>
      </ActionGuard>
      
      {/* صلاحية خاصة */}
      {canAcceptWithoutWarranty && (
        <Button variant="destructive">
          قبول بدون ضمان
        </Button>
      )}
    </PermissionGuard>
  );
}
```

---

## 🔧 حالات خاصة

### 1. المرتجعات (صلاحية خاصة)
```typescript
const { canAcceptWithoutWarranty } = usePermission('returns');
```

### 2. الرسائل (عرض الكل)
```typescript
const { canViewAll } = usePermission('messaging');
```

### 3. التحويل المخزني (مخازن محددة)
```typescript
const { canManage } = usePermission('warehouseTransfer');
// canManage هو array من IDs المخازن المسموحة
```

---

## 📝 قائمة تحقق سريعة

### لكل صفحة:
- [ ] إضافة `import { PermissionGuard, ActionGuard }`
- [ ] لف المحتوى في `<PermissionGuard pageKey="...">`
- [ ] حماية جميع الأزرار بـ `<ActionGuard>`
- [ ] اختبار الصفحة

### اختبار سريع:
- [ ] إنشاء مستخدم بدون صلاحيات
- [ ] محاولة الوصول للصفحة
- [ ] التأكد من ظهور رسالة الخطأ

---

## 🚨 أخطاء شائعة

### ❌ خطأ: pageKey خاطئ
```typescript
<PermissionGuard pageKey="sale"> // خطأ
```
### ✅ صحيح:
```typescript
<PermissionGuard pageKey="sales"> // صحيح
```

### ❌ خطأ: نسيان الاستيرادات
```typescript
// لا توجد استيرادات - خطأ
export default function Page() {
  return <PermissionGuard>...</PermissionGuard>
}
```

### ✅ صحيح:
```typescript
import { PermissionGuard } from '@/components/PermissionGuard';

export default function Page() {
  return <PermissionGuard>...</PermissionGuard>
}
```

---

## ⏱️ جدول زمني مقترح

### يوم 1 (4 ساعات):
- [ ] المستخدمين
- [ ] الإعدادات  
- [ ] المبيعات
- [ ] المخزون

### يوم 2 (4 ساعات):
- [ ] التوريد
- [ ] المرتجعات
- [ ] العملاء
- [ ] المخازن

### يوم 3 (4 ساعات):
- [ ] التتبع
- [ ] التقييم
- [ ] الجرد
- [ ] التقارير

### يوم 4 (3 ساعات):
- [ ] الرسائل
- [ ] الطلبات
- [ ] قبول الأجهزة
- [ ] أداة التسعير
- [ ] التحويل المخزني

### يوم 5 (2 ساعة):
- [ ] اختبار شامل
- [ ] إصلاح المشاكل

---

## 🛠️ أدوات مساعدة

### البحث عن الأزرار غير المحمية:
```bash
grep -r "Button.*onClick" app/(main) --include="*.tsx"
```

### البحث عن الصفحات:
```bash
find app/(main) -name "page.tsx" -type f
```

### قالب سريع للنسخ:
```typescript
// إضافة في بداية الملف
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

// لف المحتوى
<PermissionGuard pageKey="PAGE_KEY">
  {/* المحتوى */}
</PermissionGuard>

// حماية الأزرار
<ActionGuard pageKey="PAGE_KEY" action="ACTION">
  <Button>النص</Button>
</ActionGuard>
```

---

## 📞 عند الحاجة للمساعدة

1. راجع الدليل الشامل: `permissions-fix-guide.md`
2. راجع الدليل المتقدم: `permissions-advanced-guide.md`
3. انظر للأمثلة في: `demo-permissions/page.tsx`
4. تحقق من: `components/PermissionGuard.tsx`

---

**تذكر**: الهدف هو حماية 17 صفحة في أسرع وقت ممكن مع ضمان الجودة!
