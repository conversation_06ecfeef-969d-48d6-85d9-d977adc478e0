const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestWarehouses() {
  try {
    console.log('Creating test warehouses...');
    
    const testWarehouses = [
      {
        name: 'المخزن الرئيسي',
        type: 'رئيسي',
        location: 'المقر الرئيسي - الطابق الأول'
      },
      {
        name: 'مخزن الصيانة',
        type: 'فرعي',
        location: 'قسم الصيانة - الطابق الثاني'
      },
      {
        name: 'مخزن الفرع الشمالي',
        type: 'فرعي',
        location: 'الفرع الشمالي - شارع الملك فهد'
      },
      {
        name: 'مخزن الفرع الجنوبي',
        type: 'فرعي',
        location: 'الفرع الجنوبي - شارع الأمير محمد'
      },
      {
        name: 'مخزن المرتجعات',
        type: 'فرعي',
        location: 'قسم المرتجعات - الطابق الأرضي'
      }
    ];
    
    for (const warehouse of testWarehouses) {
      try {
        // Check if warehouse exists
        const existing = await prisma.warehouse.findFirst({
          where: { name: warehouse.name }
        });

        if (!existing) {
          await prisma.warehouse.create({
            data: warehouse
          });
          console.log(`Created warehouse: ${warehouse.name}`);
        } else {
          console.log(`Warehouse ${warehouse.name} already exists`);
        }
      } catch (error) {
        console.log(`Error with warehouse ${warehouse.name}:`, error.message);
      }
    }
    
    console.log('Test warehouses creation completed');
    
    // Check total warehouses
    const totalWarehouses = await prisma.warehouse.count();
    console.log(`Total warehouses in database: ${totalWarehouses}`);
    
    // List all warehouses
    const warehouses = await prisma.warehouse.findMany();
    console.log('All warehouses:');
    warehouses.forEach(w => {
      console.log(`- ID: ${w.id}, Name: ${w.name}, Type: ${w.type}, Location: ${w.location}`);
    });
    
  } catch (error) {
    console.error('Error creating test warehouses:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestWarehouses();
