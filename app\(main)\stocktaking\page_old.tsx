'use client';

import { useState, useMemo, useRef, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type { Device, SystemSettings, Stocktake, StocktakeItem, StocktakeDiscrepancy, StocktakeStatus, StocktakeFilter } from '@/lib/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ClipboardList,
  Upload,
  Trash2,
  FileDown,
  Printer,
  FileSpreadsheet,
  Check,
  X,
  AlertTriangle,
  PackageSearch,
  Play,
  Pause,
  Save,
  Eye,
  Camera,
  History,
  Filter,
  Search,
  RotateCcw,
  CheckCircle,
  XCircle,
  Clock,
  Archive,
  Plus,
  Edit,
  MessageSquare,
} from 'lucide-react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';

type StocktakeResults = {
  matching: Device[];
  missing: Device[];
  extra: (Device & { note: string })[];
  damaged: Device[];
  defective: Device[];
  wrongLocation: (Device & { actualWarehouse: string })[];
};

export default function StocktakingPage() {
  const { devices, warehouses, systemSettings } = useStore();
  const { toast } = useToast();

  const [operationId, setOperationId] = useState('');
  const [selectedWarehouseId, setSelectedWarehouseId] = useState('');
  const [selectedModel, setSelectedModel] = useState('all');
  const [scannedImeis, setScannedImeis] = useState<string[]>([]);
  const [imeiInput, setImeiInput] = useState('');
  const [results, setResults] = useState<StocktakeResults | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setOperationId(`ST-${Date.now()}`);
  }, []);

  const modelsInWarehouse = useMemo(() => {
    if (!selectedWarehouseId) return [];
    const warehouseDevices = devices.filter(
      (d) => d.warehouseId?.toString() === selectedWarehouseId
    );
    return [...new Set(warehouseDevices.map((d) => d.model))].sort();
  }, [selectedWarehouseId, devices]);

  const resetScan = () => {
    setScannedImeis([]);
    setImeiInput('');
    setResults(null);
  };

  const handleWarehouseChange = (id: string) => {
    setSelectedWarehouseId(id);
    setSelectedModel('all');
    resetScan();
  };

  const handleAddImei = () => {
    if (!imeiInput.trim()) return;
    if (scannedImeis.includes(imeiInput.trim()) {
      toast({
        variant: 'destructive',
        title: 'مكرر',
        description: 'هذا الرقم التسلسلي تم مسحه بالفعل.',
      });
      return;
    }
    setScannedImeis((prev) => [...prev, imeiInput.trim()]);
    setImeiInput('');
  };

  const handleFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    const existingScanned = new Set(scannedImeis);
    const newScans = lines.filter((line) => !existingScanned.has(line));

    setScannedImeis((prev) => [...prev, ...newScans]);

    toast({
      title: 'اكتمل الاستيراد',
      description: `تمت إضافة ${newScans.length} جهازًا. تم تخطي ${lines.length - newScans.length} جهازًا مكررًا.`,
    });

    if (event.target) event.target.value = '';
  };

  const handleRemoveImei = (imei: string) => {
    setScannedImeis((prev) => prev.filter((item) => item !== imei));
  };

  const handleStartStocktake = () => {
    if (!selectedWarehouseId) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى اختيار المخزن أولاً.',
      });
      return;
    }
    if (scannedImeis.length === 0) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى مسح أو استيراد الأجهزة الفعلية أولاً.',
      });
      return;
    }

    const warehouseId = parseInt(selectedWarehouseId, 10);

    // Devices that *should* be in the selected warehouse/model according to the system
    const expectedDevices = devices.filter(
      (d) =>
        d.warehouseId === warehouseId &&
        (selectedModel === 'all' || d.model === selectedModel)
    );

    const scannedSet = new Set(scannedImeis);
    const expectedSet = new Set(expectedDevices.map((d) => d.id));

    const matchingDevices = expectedDevices.filter((d) => scannedSet.has(d.id));
    const missingDevices = expectedDevices.filter((d) => !scannedSet.has(d.id));

    const extraImeis = scannedImeis.filter((imei) => !expectedSet.has(imei));

    const extraDevices: (Device & { note: string })[] = [];
    const wrongLocationDevices: (Device & { actualWarehouse: string })[] = [];

    extraImeis.forEach((imei) => {
      const device = devices.find((d) => d.id === imei);
      if (device) {
        if (device.warehouseId !== warehouseId) {
          const actualWarehouse =
            warehouses.find((w) => w.id === device.warehouseId)?.name ||
            'مخزن غير معروف';
          wrongLocationDevices.push({ ...device, actualWarehouse });
        } else {
          // It's in the correct warehouse but wrong model
          extraDevices.push({
            ...device,
            note: `موديل غير مطابق: ${device.model}`,
          });
        }
      } else {
        extraDevices.push({
          id: imei,
          model: 'جهاز غير مسجل',
          status: 'غير معروف',
          storage: 'N/A',
          price: 0,
          condition: 'جديد',
          note: 'غير مسجل بالنظام',
        } as any);
      }
    });

    const damagedDevices = matchingDevices.filter((d) => d.status === 'تالف');
    const defectiveDevices = matchingDevices.filter((d) => d.status === 'معيب');

    setResults({
      matching: matchingDevices,
      missing: missingDevices,
      extra: [
        ...extraDevices,
        ...wrongLocationDevices.map((d) => ({
          ...d,
          note: `موجود في: ${d.actualWarehouse}`,
        })),
      ],
      damaged: damagedDevices,
      defective: defectiveDevices,
      wrongLocation: wrongLocationDevices,
    });

    toast({
      title: 'اكتمل الجرد',
      description: 'تمت مطابقة الأجهزة. يمكنك الآن مراجعة النتائج.',
    });
  };

  const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc
        .setFontSize(16)
        .text(settings.companyName, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc
        .setFontSize(8)
        .text(
          `صفحة ${data.pageNumber} من ${pageCount - 1}`,
          data.settings.margin.left,
          doc.internal.pageSize.height - 10
    );
      if (settings.reportFooter) {
        doc.text(
          settings.reportFooter,
          195,
          doc.internal.pageSize.height - 10,
          { align: 'right' }
    );
      }
    };
    return { addHeader, addFooter };
  };

  const handleExportPdf = (action: 'print' | 'download') => {
    if (!results) return;

    const doc = new jsPDF();
    doc.setR2L(true);

    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();

    const warehouseName =
      warehouses.find((w) => w.id.toString() === selectedWarehouseId)?.name ||
      '';

    doc.setFontSize(18);
    doc.text(`تقرير الجرد - ${warehouseName}`, 190, 45, { align: 'right' });
    doc.setFontSize(12);
    doc.text(`رقم العملية: ${operationId}`, 190, 52, { align: 'right' });
    doc.text(`الموديل: ${selectedModel}`, 190, 59, { align: 'right' });

    let startY = 65;

    const createTable = (title: string, head: string[][], body: any[][]) => {
      if (body.length > 0) {
        autoTable(doc, {
          startY: startY,
          head: [
            [
              {
                content: title,
                colSpan: head[0].length,
                styles: { halign: 'center', fontStyle: 'bold' },
              },
            ],
          ],
          body: [[]], // Dummy row to render the header
          theme: 'plain',
        });
        startY = (doc as any).lastAutoTable.finalY;

        autoTable(doc, {
          startY: startY,
          head: head,
          body: body,
          theme: 'grid',
          styles: { font: 'Helvetica', halign: 'right' },
          headStyles: { halign: 'center', fillColor: [44, 51, 51] },
          didDrawPage: addFooter,
        });
        startY = (doc as any).lastAutoTable.finalY + 10;
      }
    };

    createTable(
      'الأجهزة المطابقة',
      [['الحالة', 'الموديل', 'الرقم التسلسلي']],
      results.matching.map((d) => [d.status, d.model, d.id])
    );
    createTable(
      'الأجهزة المفقودة',
      [['الحالة', 'الموديل', 'الرقم التسلسلي']],
      results.missing.map((d) => [d.status, d.model, d.id])
    );
    createTable(
      'الأجهزة الزائدة/الخاطئة',
      [['الملاحظة', 'الحالة', 'الموديل', 'الرقم التسلسلي']],
      results.extra.map((d) => [d.note, d.status, d.model, d.id])
    );

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(`Stocktake-Report-${operationId}.pdf`);
    }
  };

  const handleExportExcel = () => {
    if (!results) return;

    const wb = XLSX.utils.book_new();

    const addSheet = (sheetName: string, data: any[]) => {
      if (data.length > 0) {
        const ws = XLSX.utils.json_to_sheet(data);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
      }
    };

    addSheet(
      'مطابق',
      results.matching.map((d) => ({
        'الرقم التسلسلي': d.id,
        الموديل: d.model,
        الحالة: d.status,
      }))
    );
    addSheet(
      'مفقود',
      results.missing.map((d) => ({
        'الرقم التسلسلي': d.id,
        الموديل: d.model,
        الحالة: d.status,
      }))
    );
    addSheet(
      'زائد',
      results.extra.map((d) => ({
        'الرقم التسلسلي': d.id,
        الموديل: d.model,
        الحالة: d.status,
        ملاحظة: d.note,
      }))
    );

    XLSX.writeFile(wb, `Stocktake-${operationId}.xlsx`);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>إنشاء عملية جرد</CardTitle>
          <CardDescription>
            اختر المخزن والموديل ثم أدخل الأجهزة الموجودة فعليًا.
          </CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label>رقم العملية</Label>
            <Input value={operationId} disabled />
          </div>
          <div className="space-y-2">
            <Label>الموظف</Label>
            <Input value="مدير النظام" disabled />
          </div>
          <div className="space-y-2">
            <Label>المخزن</Label>
            <Select
              dir="rtl"
              value={selectedWarehouseId}
              onValueChange={handleWarehouseChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="اختر المخزن..." />
              </SelectTrigger>
              <SelectContent>
                {warehouses.map((w) => (
                  <SelectItem key={w.id} value={w.id.toString()}>
                    {w.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>الموديل</Label>
            <Select
              dir="rtl"
              value={selectedModel}
              onValueChange={setSelectedModel}
              disabled={!selectedWarehouseId}
            >
              <SelectTrigger>
                <SelectValue placeholder="اختر الموديل..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الموديلات</SelectItem>
                {modelsInWarehouse.map((model) => (
                  <SelectItem key={model} value={model}>
                    {model}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>قائمة الجرد الفعلية</CardTitle>
            <CardDescription>
              أدخل الأرقام التسلسلية للأجهزة الموجودة في المخزن.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-4">
              <Input
                placeholder="امسح أو أدخل الرقم التسلسلي..."
                value={imeiInput}
                onChange={(e) => setImeiInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAddImei()}
              />
              <Button onClick={handleAddImei}>إضافة</Button>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileImport}
                accept=".txt"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => fileInputRef.current?.click()}
                title="استيراد من ملف"
              >
                <Upload className="h-4 w-4" />
              </Button>
            </div>
            <div className="rounded-md border max-h-72 overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>#</TableHead>
                    <TableHead>الرقم التسلسلي</TableHead>
                    <TableHead>إجراء</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {scannedImeis.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="h-24 text-center">
                        لم يتم إضافة أجهزة بعد.
                      </TableCell>
                    </TableRow>
                  ) : (
                    scannedImeis.map((imei, index) => (
                      <TableRow key={imei}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell dir="ltr">{imei}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveImei(imei)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleStartStocktake}
              disabled={scannedImeis.length === 0}
            >
              <ClipboardList className="ml-2 h-4 w-4" />
              بدء المطابقة ({scannedImeis.length})
            </Button>
          </CardFooter>
        </Card>

        <Card className={!results ? 'flex items-center justify-center' : ''}>
          {results ? (
            <div className="space-y-4 p-4">
              <CardTitle>نتائج الجرد</CardTitle>
              <div className="grid grid-cols-2 gap-4">
                <Card className="bg-green-500/10 border-green-500/20">
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      أجهزة مطابقة
                    </CardTitle>
                    <Check className="h-4 w-4 text-green-400" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {results.matching.length}
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-red-500/10 border-red-500/20">
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      أجهزة مفقودة
                    </CardTitle>
                    <X className="h-4 w-4 text-red-400" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {results.missing.length}
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-yellow-500/10 border-yellow-500/20">
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      أجهزة زائدة/خاطئة
                    </CardTitle>
                    <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {results.extra.length}
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-orange-500/10 border-orange-500/20">
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      تالف/معيوب
                    </CardTitle>
                    <PackageSearch className="h-4 w-4 text-orange-400" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {results.damaged.length + results.defective.length}
                    </div>
                  </CardContent>
                </Card>
              </div>
              <div className="pt-4 flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleExportPdf('print')}
                >
                  <Printer className="ml-2 h-4 w-4" /> طباعة التقرير
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleExportPdf('download')}
                >
                  <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
                </Button>
                <Button variant="outline" onClick={() => handleExportExcel()}>
                  <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground p-6">
              <ClipboardList className="mx-auto h-12 w-12" />
              <p className="mt-4">نتائج الجرد ستظهر هنا بعد بدء المطابقة.</p>
            </div>
          )}
        </Card>
      </div>

      {results && (
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل نتائج الجرد</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-green-400">
                الأجهزة المطابقة ({results.matching.length})
              </h3>
              <div className="rounded-md border max-h-60 overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الموديل</TableHead>
                      <TableHead>IMEI</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.matching.map((d) => (
                      <TableRow key={d.id}>
                        <TableCell>{d.model}</TableCell>
                        <TableCell dir="ltr">{d.id}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-red-400">
                الأجهزة المفقودة ({results.missing.length})
              </h3>
              <div className="rounded-md border max-h-60 overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الموديل</TableHead>
                      <TableHead>IMEI</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.missing.map((d) => (
                      <TableRow key={d.id}>
                        <TableCell>{d.model}</TableCell>
                        <TableCell dir="ltr">{d.id}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-yellow-400">
                الأجهزة الزائدة/الخاطئة ({results.extra.length})
              </h3>
              <div className="rounded-md border max-h-60 overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الملاحظة</TableHead>
                      <TableHead>IMEI</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.extra.map((d) => (
                      <TableRow key={d.id}>
                        <TableCell>{d.note}</TableCell>
                        <TableCell dir="ltr">{d.id}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}