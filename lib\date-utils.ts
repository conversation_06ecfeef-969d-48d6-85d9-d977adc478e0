/**
 * Date utility functions for Arabic formatting and manipulation
 */

/**
 * Format date in Arabic locale
 * @param date - Date object or ISO string
 * @returns Formatted Arabic date string
 */
export function formatArabicDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Format date in Arabic locale (date only, no time)
 * @param date - Date object or ISO string
 * @returns Formatted Arabic date string without time
 */
export function formatArabicDateOnly(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * Format date in Arabic locale (time only)
 * @param date - Date object or ISO string
 * @returns Formatted Arabic time string
 */
export function formatArabicTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleTimeString('ar-EG', {
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Format date for CSV export (ISO format)
 * @param date - Date object or ISO string
 * @returns ISO formatted date string
 */
export function formatDateForCSV(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString().split('T')[0];
}

/**
 * Format date for display in tables (short format)
 * @param date - Date object or ISO string
 * @returns Short formatted date string
 */
export function formatShortDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Get relative time in Arabic (e.g., "منذ 5 دقائق")
 * @param date - Date object or ISO string
 * @returns Relative time string in Arabic
 */
export function getRelativeTimeArabic(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'منذ لحظات';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `منذ ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `منذ ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `منذ ${days} ${days === 1 ? 'يوم' : 'أيام'}`;
  } else {
    return formatArabicDateOnly(dateObj);
  }
}

/**
 * Check if date is today
 * @param date - Date object or ISO string
 * @returns True if date is today
 */
export function isToday(date: Date | string): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const today = new Date();
  return dateObj.toDateString() === today.toDateString();
}

/**
 * Get current date in Arabic format
 * @returns Current date formatted in Arabic
 */
export function getCurrentArabicDate(): string {
  return formatArabicDate(new Date());
}

/**
 * Parse date from Arabic input or return current date
 * @param input - Date input (string or Date)
 * @returns Parsed date object
 */
export function parseArabicDate(input: string | Date): Date {
  if (typeof input === 'string') {
    const parsed = new Date(input);
    return isNaN(parsed.getTime()) ? new Date() : parsed;
  }
  return input;
}
