// الطريقة الثانية: HTML محسن + طباعة مباشرة
// ملف: lib/export-utils/html-print.ts

export function printArabicContent(data: any, title: string) {
  // 1. إنشاء نافذة طباعة جديدة
  const printWindow = window.open('', '_blank', 'width=800,height=600');
  
  if (!printWindow) {
    throw new Error('فشل في فتح نافذة الطباعة. تأكد من عدم حظر النوافذ المنبثقة.');
  }

  // 2. إنشاء HTML محسن للعربية
  const htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
      <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>${title}</title>
        
        <!-- 3. تحميل الخطوط العربية -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
        
        <style>
          /* 4. إعدادات أساسية للعربية */
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          html {
            direction: rtl;
            lang: ar;
          }
          
          body {
            /* 5. ترتيب الخطوط بالأولوية */
            font-family: 'Cairo', 'Noto Sans Arabic', 'Amiri', Arial, sans-serif !important;
            direction: rtl;
            text-align: right;
            background: white;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
            padding: 20px;
            
            /* 6. إعدادات خاصة للطباعة */
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
          }
          
          /* 7. تنسيق العناوين */
          h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 15px;
            text-align: center;
          }
          
          h1 { font-size: 24px; }
          h2 { font-size: 20px; }
          h3 { font-size: 18px; }
          
          /* 8. تنسيق الجداول */
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
          }
          
          th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
            direction: rtl;
          }
          
          th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
          }
          
          tr:nth-child(even) {
            background-color: #f8f9fa;
          }
          
          /* 9. تنسيق المعلومات */
          .info-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
          }
          
          .info-item {
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
          }
          
          .info-label {
            font-weight: bold;
            color: #495057;
            min-width: 120px;
          }
          
          .info-value {
            flex: 1;
            text-align: right;
          }
          
          /* 10. إعدادات الطباعة */
          @media print {
            @page {
              size: A4;
              margin: 15mm;
              direction: rtl;
            }
            
            body {
              font-size: 12pt;
              line-height: 1.4;
            }
            
            h1 { font-size: 18pt; }
            h2 { font-size: 16pt; }
            h3 { font-size: 14pt; }
            
            /* منع تقسيم العناصر */
            .info-section, table {
              page-break-inside: avoid;
            }
            
            /* إخفاء العناصر غير المطلوبة */
            .no-print {
              display: none !important;
            }
          }
          
          /* 11. تحسينات إضافية للعربية */
          .arabic-text {
            font-family: 'Cairo', 'Noto Sans Arabic', 'Amiri', Arial, sans-serif !important;
            direction: rtl;
            text-align: right;
            unicode-bidi: bidi-override;
          }
          
          /* 12. تنسيق التاريخ والوقت */
          .timestamp {
            text-align: center;
            color: #6c757d;
            font-size: 12px;
            margin-top: 20px;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
          }
        </style>
      </head>
      
      <body class="arabic-text">
        <!-- 13. رأس التقرير -->
        <div class="header">
          <h1>${title}</h1>
          <div class="timestamp">
            تم الطباعة في: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}
          </div>
        </div>

        <!-- 14. معلومات أساسية -->
        <div class="info-section">
          <h3>المعلومات الأساسية</h3>
          <div class="info-item">
            <span class="info-label">الاسم:</span>
            <span class="info-value">${data.name || '-'}</span>
          </div>
          <div class="info-item">
            <span class="info-label">التاريخ:</span>
            <span class="info-value">${data.date || '-'}</span>
          </div>
          <div class="info-item">
            <span class="info-label">الوصف:</span>
            <span class="info-value">${data.description || '-'}</span>
          </div>
        </div>

        <!-- 15. جدول البيانات -->
        ${data.items && data.items.length > 0 ? `
          <div class="table-section">
            <h3>تفاصيل الأصناف</h3>
            <table>
              <thead>
                <tr>
                  <th>الرقم</th>
                  <th>الاسم</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>ملاحظات</th>
                </tr>
              </thead>
              <tbody>
                ${data.items.map((item: any, index: number) => `
                  <tr>
                    <td>${index + 1}</td>
                    <td class="arabic-text">${item.name || '-'}</td>
                    <td>${item.quantity || '-'}</td>
                    <td>${item.price || '-'}</td>
                    <td class="arabic-text">${item.notes || '-'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        ` : ''}

        <!-- 16. سكريبت الطباعة التلقائية -->
        <script>
          // انتظار تحميل الخطوط
          document.fonts.ready.then(() => {
            // انتظار إضافي للتأكد من تحميل كل شيء
            setTimeout(() => {
              window.print();
              
              // إغلاق النافذة بعد الطباعة
              window.onafterprint = () => {
                window.close();
              };
            }, 1500);
          });
        </script>
      </body>
    </html>
  `;

  // 17. كتابة المحتوى وإغلاق المستند
  printWindow.document.write(htmlContent);
  printWindow.document.close();
}

// مثال للاستخدام
export function printSupplyOrder(orderData: any) {
  const data = {
    name: orderData.supplierName,
    date: orderData.date,
    description: orderData.notes,
    items: orderData.items.map((item: any) => ({
      name: `${item.manufacturer} ${item.model}`,
      quantity: '1',
      price: item.price || '-',
      notes: item.condition
    }))
  };
  
  printArabicContent(data, `أمر التوريد رقم ${orderData.id}`);
}

// المميزات:
// ✅ سهولة التطبيق والتعديل
// ✅ دعم كامل للعربية مع الخطوط المناسبة
// ✅ تنسيق احترافي وقابل للتخصيص
// ✅ طباعة مباشرة بدون ملفات وسيطة
// ✅ يعمل مع جميع أنواع البيانات

// العيوب:
// ❌ يتطلب فتح نافذة جديدة
// ❌ قد يحجبها مانع النوافذ المنبثقة
// ❌ لا ينتج ملف PDF للحفظ
