// الطريقة الأولى: Canvas + PDF (الأفضل للعربية)
// ملف: lib/export-utils/canvas-pdf.ts

import jsPDF from 'jspdf';

export async function createArabicPDFWithCanvas(
  data: any,
  fileName: string,
  title: string = 'تقرير'
): Promise<void> {
  
  // 1. إنشاء Canvas للرسم
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('فشل في إنشاء Canvas');
  }

  // 2. تحديد أبعاد <PERSON> (A4 بدقة عالية)
  const dpi = 300; // دقة عالية للطباعة
  const mmToPx = dpi / 25.4; // تحويل من ملم إلى بكسل
  canvas.width = 210 * mmToPx;  // عرض A4
  canvas.height = 297 * mmToPx; // طول A4
  
  // 3. إعداد الخطوط العربية
  ctx.font = '24px Arial, "Noto Sans Arabic", "Cairo", sans-serif';
  ctx.fillStyle = '#2c3e50';
  ctx.textAlign = 'center';
  ctx.direction = 'rtl'; // اتجاه من اليمين لليسار
  
  let currentY = 60;
  
  // 4. رسم العنوان
  ctx.fillText(title, canvas.width / 2, currentY);
  currentY += 60;
  
  // 5. رسم المحتوى العربي
  ctx.font = '18px Arial, "Noto Sans Arabic", sans-serif';
  ctx.fillStyle = '#34495e';
  ctx.textAlign = 'right';
  
  // رسم مربع معلومات
  ctx.strokeStyle = '#bdc3c7';
  ctx.lineWidth = 2;
  ctx.strokeRect(50, currentY - 20, canvas.width - 100, 120);
  
  // رسم النصوص العربية
  const rightMargin = canvas.width - 70;
  ctx.fillText(`الاسم: ${data.name || 'غير محدد'}`, rightMargin, currentY + 10);
  ctx.fillText(`التاريخ: ${data.date || 'غير محدد'}`, rightMargin, currentY + 40);
  ctx.fillText(`الوصف: ${data.description || 'غير محدد'}`, rightMargin, currentY + 70);
  
  currentY += 150;
  
  // 6. رسم جدول البيانات
  if (data.items && data.items.length > 0) {
    ctx.font = '16px Arial, "Noto Sans Arabic", sans-serif';
    ctx.fillStyle = '#2c3e50';
    
    // رؤوس الجدول
    const tableHeaders = ['الرقم', 'الاسم', 'الكمية', 'السعر'];
    const colWidth = (canvas.width - 100) / tableHeaders.length;
    
    // رسم خلفية رؤوس الجدول
    ctx.fillStyle = '#ecf0f1';
    ctx.fillRect(50, currentY, canvas.width - 100, 30);
    
    // رسم رؤوس الجدول
    ctx.fillStyle = '#2c3e50';
    tableHeaders.forEach((header, index) => {
      const x = canvas.width - 50 - (index + 0.5) * colWidth;
      ctx.fillText(header, x, currentY + 20);
    });
    
    currentY += 30;
    
    // رسم صفوف البيانات
    data.items.forEach((item: any, rowIndex: number) => {
      const rowY = currentY + (rowIndex * 30);
      
      // خلفية متناوبة للصفوف
      if (rowIndex % 2 === 0) {
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(50, rowY, canvas.width - 100, 30);
      }
      
      // رسم البيانات
      ctx.fillStyle = '#2c3e50';
      const rowData = [
        (rowIndex + 1).toString(),
        item.name || '-',
        item.quantity || '-',
        item.price || '-'
      ];
      
      rowData.forEach((cellData, colIndex) => {
        const x = canvas.width - 50 - (colIndex + 0.5) * colWidth;
        ctx.fillText(cellData, x, rowY + 20);
      });
    });
  }
  
  // 7. تحويل Canvas إلى صورة
  const imgData = canvas.toDataURL('image/png', 1.0);
  
  // 8. إنشاء PDF وإضافة الصورة
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });
  
  // إضافة الصورة للـ PDF
  pdf.addImage(imgData, 'PNG', 0, 0, 210, 297);
  
  // 9. حفظ الملف
  pdf.save(`${fileName}.pdf`);
}

// مثال للاستخدام
export function exportSupplyOrderWithCanvas(orderData: any) {
  const data = {
    name: orderData.supplierName,
    date: orderData.date,
    description: orderData.notes,
    items: orderData.items.map((item: any) => ({
      name: `${item.manufacturer} ${item.model}`,
      quantity: '1',
      price: item.price || '-'
    }))
  };
  
  createArabicPDFWithCanvas(
    data,
    `supply_order_${orderData.id}`,
    `أمر التوريد رقم ${orderData.id}`
  );
}

// المميزات:
// ✅ يحل مشكلة الأحرف الغريبة تماماً
// ✅ دعم كامل للاتجاه من اليمين لليسار
// ✅ تحكم كامل في التنسيق والخطوط
// ✅ جودة عالية للطباعة
// ✅ يعمل في جميع المتصفحات

// العيوب:
// ❌ يتطلب رسم يدوي لكل عنصر
// ❌ صعوبة في التعديل والصيانة
// ❌ حجم ملف أكبر (صورة بدلاً من نص)
