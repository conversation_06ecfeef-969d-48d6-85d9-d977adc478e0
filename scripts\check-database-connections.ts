#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkDatabaseConnections() {
  try {
    console.log('🔍 فحص حالة قواعد البيانات والاتصالات...');

    // 1. فحص الاتصال الحالي
    console.log('\n📡 فحص الاتصال الحالي بقاعدة البيانات...');
    
    try {
      await prisma.$connect();
      console.log('✅ الاتصال الحالي بقاعدة البيانات يعمل بشكل صحيح');
      
      // فحص متغيرات البيئة
      const databaseUrl = process.env.DATABASE_URL;
      console.log(`🔗 DATABASE_URL: ${databaseUrl}`);
      
      if (databaseUrl) {
        const url = new URL(databaseUrl);
        console.log(`📊 قاعدة البيانات الحالية: ${url.pathname.replace('/', '')}`);
        console.log(`🏠 الخادم: ${url.hostname}:${url.port}`);
        console.log(`👤 المستخدم: ${url.username}`);
      }
    } catch (error) {
      console.error('❌ فشل في الاتصال بقاعدة البيانات:', error);
    }

    // 2. فحص جدول الاتصالات المحفوظة
    console.log('\n💾 فحص جدول الاتصالات المحفوظة...');
    
    try {
      const connections = await prisma.databaseConnection.findMany({
        orderBy: { createdAt: 'desc' }
      });
      
      console.log(`✅ تم العثور على ${connections.length} اتصال محفوظ في النظام`);
      
      if (connections.length > 0) {
        console.log('\n📋 الاتصالات المحفوظة:');
        connections.forEach((conn, index) => {
          console.log(`${index + 1}. الاسم: "${conn.name}"`);
          console.log(`   - الخادم: ${conn.host}:${conn.port}`);
          console.log(`   - قاعدة البيانات: ${conn.database}`);
          console.log(`   - المستخدم: ${conn.username}`);
          console.log(`   - نشط: ${conn.isActive ? 'نعم' : 'لا'}`);
          console.log(`   - افتراضي: ${conn.isDefault ? 'نعم' : 'لا'}`);
          console.log(`   - تاريخ الإنشاء: ${conn.createdAt.toISOString().split('T')[0]}`);
          console.log('');
        });
        
        // فحص الاتصال النشط
        const activeConnection = connections.find(c => c.isActive);
        if (activeConnection) {
          console.log(`🟢 الاتصال النشط: ${activeConnection.name}`);
        } else {
          console.log('⚠️ لا يوجد اتصال محدد كنشط');
        }
        
        // فحص الاتصال الافتراضي
        const defaultConnection = connections.find(c => c.isDefault);
        if (defaultConnection) {
          console.log(`⭐ الاتصال الافتراضي: ${defaultConnection.name}`);
        } else {
          console.log('⚠️ لا يوجد اتصال محدد كافتراضي');
        }
      } else {
        console.log('⚠️ لا توجد اتصالات محفوظة في النظام');
        console.log('💡 هذا قد يفسر عدم ظهور قواعد البيانات في تبويب الإعدادات');
      }
    } catch (error) {
      console.error('❌ خطأ في فحص جدول الاتصالات:', error);
    }

    // 3. فحص أوامر التوريد لمعرفة مكان حفظها
    console.log('\n📦 فحص أوامر التوريد...');
    
    try {
      const supplyOrders = await prisma.supplyOrder.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          items: true
        }
      });
      
      console.log(`✅ تم العثور على ${supplyOrders.length} أمر توريد`);
      
      if (supplyOrders.length > 0) {
        console.log('\n📋 آخر أوامر التوريد:');
        supplyOrders.forEach((order, index) => {
          console.log(`${index + 1}. رقم الأمر: ${order.supplyOrderId}`);
          console.log(`   - التاريخ: ${order.supplyDate}`);
          console.log(`   - المخزن: ${order.warehouseId}`);
          console.log(`   - عدد الأجهزة: ${order.items.length}`);
          console.log(`   - تاريخ الإنشاء: ${order.createdAt.toISOString().split('T')[0]}`);
          console.log('');
        });
        
        // فحص الأجهزة المرتبطة بأوامر التوريد
        const totalItems = await prisma.supplyOrderItem.count();
        console.log(`📱 إجمالي الأجهزة في أوامر التوريد: ${totalItems}`);
        
        // فحص الأجهزة في جدول Device
        const devices = await prisma.device.findMany({
          take: 5,
          orderBy: { dateAdded: 'desc' }
        });
        
        console.log(`📱 إجمالي الأجهزة في جدول Device: ${devices.length}`);
        
        if (devices.length > 0) {
          console.log('\n📱 آخر الأجهزة المضافة:');
          devices.forEach((device, index) => {
            console.log(`${index + 1}. IMEI: ${device.id}`);
            console.log(`   - الموديل: ${device.model}`);
            console.log(`   - الحالة: ${device.status}`);
            console.log(`   - المخزن: ${device.warehouseId || 'غير محدد'}`);
            console.log(`   - المورد: ${device.supplierId || 'غير محدد'}`);
            console.log(`   - تاريخ الإضافة: ${device.dateAdded.toISOString().split('T')[0]}`);
            console.log('');
          });
        } else {
          console.log('⚠️ لا توجد أجهزة في جدول Device');
          console.log('💡 هذا قد يفسر عدم ظهور الأجهزة في المخزون');
        }
      } else {
        console.log('ℹ️ لا توجد أوامر توريد في قاعدة البيانات الحالية');
      }
    } catch (error) {
      console.error('❌ خطأ في فحص أوامر التوريد:', error);
    }

    // 4. اختبار API connections
    console.log('\n🌐 اختبار API الاتصالات...');
    
    try {
      const token = Buffer.from('user:admin:admin').toString('base64');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      const response = await fetch('http://localhost:9005/api/database/connections', {
        method: 'GET',
        headers
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ API الاتصالات يعمل، عدد الاتصالات: ${data.connections?.length || 0}`);
      } else {
        console.log(`❌ API الاتصالات لا يعمل: ${response.status}`);
      }
    } catch (error) {
      console.log('❌ فشل في اختبار API الاتصالات:', error);
    }

    // 5. التحقق من جداول النظام
    console.log('\n🗄️ فحص جداول النظام...');
    
    try {
      const tables = [
        { name: 'User', model: prisma.user },
        { name: 'Warehouse', model: prisma.warehouse },
        { name: 'Device', model: prisma.device },
        { name: 'SupplyOrder', model: prisma.supplyOrder },
        { name: 'Sale', model: prisma.sale },
        { name: 'DatabaseConnection', model: prisma.databaseConnection }
      ];

      console.log('📊 إحصائيات الجداول:');
      for (const table of tables) {
        try {
          const count = await table.model.count();
          console.log(`- ${table.name}: ${count} سجل`);
        } catch (error) {
          console.log(`- ${table.name}: ❌ خطأ في الوصول`);
        }
      }
    } catch (error) {
      console.error('❌ خطأ في فحص جداول النظام:', error);
    }

    // 6. التوصيات
    console.log('\n💡 التوصيات والحلول:');
    console.log('═'.repeat(50));
    
    const connections = await prisma.databaseConnection.findMany();
    
    if (connections.length === 0) {
      console.log('🔧 لحل مشكلة عدم ظهور قواعد البيانات في الإعدادات:');
      console.log('1. إنشاء اتصال قاعدة بيانات افتراضي');
      console.log('2. انتقل إلى الإعدادات > قواعد البيانات');
      console.log('3. اضغط "إضافة اتصال جديد"');
      console.log('4. أدخل بيانات الاتصال الحالي:');
      console.log('   - الاسم: "الاتصال الرئيسي"');
      console.log('   - الخادم: localhost');
      console.log('   - المنفذ: 5432');
      console.log('   - قاعدة البيانات: deviceflow_db');
      console.log('   - المستخدم: deviceflow_user');
      console.log('   - كلمة المرور: om772828');
      console.log('   - اجعله افتراضي ونشط');
    }
    
    const devices = await prisma.device.findMany();
    if (devices.length === 0) {
      console.log('\n🔧 لحل مشكلة عدم ظهور الأجهزة:');
      console.log('1. تأكد من أن أوامر التوريد تضيف الأجهزة إلى جدول Device');
      console.log('2. فحص API إنشاء أوامر التوريد');
      console.log('3. تأكد من وجود مخازن في النظام');
    }

    console.log('\n✅ تم الانتهاء من فحص قواعد البيانات والاتصالات');

  } catch (error) {
    console.error('❌ خطأ عام في فحص النظام:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseConnections();
