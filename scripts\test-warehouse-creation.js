const { default: fetch } = require('node-fetch');

async function testWarehouseCreation() {
  try {
    console.log('Testing warehouse creation...');
    
    // Test creating a new warehouse with unique name
    const timestamp = Date.now();
    const newWarehouse = {
      name: `مخزن اختبار ${timestamp}`,
      type: 'فرعي',
      location: `موقع اختبار ${timestamp}`
    };
    
    console.log('Creating warehouse:', newWarehouse);
    
    const response = await fetch('http://localhost:9005/api/warehouses-simple', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newWarehouse)
    });
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      const createdWarehouse = await response.json();
      console.log('✅ Warehouse created successfully:', createdWarehouse);
      
      // Test fetching all warehouses to verify it appears
      console.log('\nFetching all warehouses to verify...');
      const getResponse = await fetch('http://localhost:9005/api/warehouses-simple');
      
      if (getResponse.ok) {
        const result = await getResponse.json();
        const warehouses = result.data || result;
        
        console.log(`Total warehouses: ${warehouses.length}`);
        const foundWarehouse = warehouses.find(w => w.id === createdWarehouse.id);
        
        if (foundWarehouse) {
          console.log('✅ New warehouse found in list:', foundWarehouse.name);
        } else {
          console.log('❌ New warehouse not found in list');
        }
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ Error creating warehouse:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Error testing warehouse creation:', error);
  }
}

testWarehouseCreation();
