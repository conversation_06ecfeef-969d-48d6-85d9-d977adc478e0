"use client";

import { CacheEntry, CacheConfig } from '@/lib/types';

/**
 * نظام إدارة التخزين المؤقت المتقدم
 * يدعم TTL، الحد الأقصى للحجم، والتحديث في الخلفية
 */
export class CacheManager {
  private cache = new Map<string, CacheEntry<any>>();
  private config: CacheConfig;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      ttl: 5 * 60 * 1000, // 5 دقائق افتراضي
      maxSize: 100, // 100 إدخال كحد أقصى
      staleWhileRevalidate: true,
      ...config
    };

    // تشغيل تنظيف دوري كل دقيقة
    this.startCleanup();
  }

  /**
   * حفظ البيانات في التخزين المؤقت
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const expiresAt = now + (ttl || this.config.ttl);

    // إزالة الإدخالات القديمة إذا تجاوزنا الحد الأقصى
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt,
      key
    };

    this.cache.set(key, entry);
  }

  /**
   * استرجاع البيانات من التخزين المؤقت
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    const now = Date.now();

    // إذا انتهت صلاحية البيانات
    if (now > entry.expiresAt) {
      if (this.config.staleWhileRevalidate) {
        // إرجاع البيانات المنتهية الصلاحية مع تحديد أنها تحتاج تحديث
        return entry.data;
      } else {
        // حذف البيانات المنتهية الصلاحية
        this.cache.delete(key);
        return null;
      }
    }

    return entry.data;
  }

  /**
   * فحص ما إذا كانت البيانات موجودة وصالحة
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    const now = Date.now();
    return now <= entry.expiresAt;
  }

  /**
   * فحص ما إذا كانت البيانات منتهية الصلاحية
   */
  isStale(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return true;

    const now = Date.now();
    return now > entry.expiresAt;
  }

  /**
   * حذف إدخال محدد
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * مسح جميع البيانات
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * الحصول على معلومات التخزين المؤقت
   */
  getStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const entry of this.cache.values()) {
      if (now <= entry.expiresAt) {
        validEntries++;
      } else {
        expiredEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
      maxSize: this.config.maxSize,
      hitRate: this.calculateHitRate()
    };
  }

  /**
   * إنشاء مفتاح تخزين مؤقت من المعاملات
   */
  static createKey(prefix: string, params: Record<string, any> = {}): string {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}:${JSON.stringify(params[key])}`)
      .join('|');
    
    return `${prefix}${sortedParams ? `|${sortedParams}` : ''}`;
  }

  /**
   * إزالة أقدم الإدخالات
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * تنظيف البيانات المنتهية الصلاحية
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * بدء التنظيف الدوري
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // كل دقيقة
  }

  /**
   * إيقاف التنظيف الدوري
   */
  stopCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * حساب معدل النجاح (مبسط)
   */
  private calculateHitRate(): number {
    // هذا تنفيذ مبسط - يمكن تحسينه بتتبع الطلبات
    return this.cache.size > 0 ? 0.8 : 0;
  }

  /**
   * تدمير المدير وتنظيف الموارد
   */
  destroy(): void {
    this.stopCleanup();
    this.clear();
  }
}

// إنشاء مثيل عام للتخزين المؤقت
export const globalCache = new CacheManager({
  ttl: 5 * 60 * 1000, // 5 دقائق
  maxSize: 200,
  staleWhileRevalidate: true
});

// تخزين مؤقت مخصص للبيانات الثابتة (مثل المستخدمين والمخازن)
export const staticDataCache = new CacheManager({
  ttl: 30 * 60 * 1000, // 30 دقيقة
  maxSize: 50,
  staleWhileRevalidate: true
});

// تخزين مؤقت للبيانات الديناميكية (مثل الأجهزة والمبيعات)
export const dynamicDataCache = new CacheManager({
  ttl: 2 * 60 * 1000, // دقيقتان
  maxSize: 300,
  staleWhileRevalidate: true
});
