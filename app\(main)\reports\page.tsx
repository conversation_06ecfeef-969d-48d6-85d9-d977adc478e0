'use client';

import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import {
  FileText,
  TrendingUp,
  UserCheck,
  Wrench,
  Package,
  Activity,
  Users,
  ListChecks,
} from 'lucide-react';

const reportCategories = [
  {
    title: 'تقارير الموديلات',
    description: 'تقارير شاملة عن الأجهزة حسب الموديل وحالتها.',
    icon: <Package className="h-8 w-8 text-primary" />,
    href: '/reports/model-reports',
  },
  {
    title: 'تقارير العملاء',
    description: 'تحليلات لمشتريات العملاء ومرتجعاتهم.',
    icon: <UserCheck className="h-8 w-8 text-primary" />,
    href: '/reports/client-reports',
  },
  {
    title: 'تقارير الموردين',
    description: 'تقييم أداء الموردين وجودة الأجهزة الموردة.',
    icon: <TrendingUp className="h-8 w-8 text-primary" />,
    href: '/reports/supplier-reports',
  },
  {
    title: 'تقارير الصيانة',
    description: 'سجلات مفصلة لعمليات الصيانة والأجهزة.',
    icon: <Wrench className="h-8 w-8 text-primary" />,
    href: '/reports/maintenance-reports',
  },
  {
    title: 'تقارير التقييم',
    description: 'تقارير مفصلة عن الأجهزة حسب درجات التقييم.',
    icon: <ListChecks className="h-8 w-8 text-primary" />,
    href: '/reports/grading-reports',
  },
  {
    title: 'تقارير الموظفين',
    description: 'عرض بيانات وأنشطة الموظفين.',
    icon: <Users className="h-8 w-8 text-primary" />,
    href: '/reports/employee-reports',
  },
  {
    title: 'سجل العمليات',
    description: 'عرض لجميع العمليات المسجلة في النظام.',
    icon: <Activity className="h-8 w-8 text-primary" />,
    href: '/reports/operations-log',
  },
];

export default function ReportsPage() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">أنواع التقارير</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {reportCategories.map((category) => (
          <Link href={category.href} key={category.title}>
            <Card className="h-full flex flex-col items-center justify-center text-center p-6 hover:shadow-lg transition-shadow duration-300">
              <CardHeader>{category.icon}</CardHeader>
              <CardContent className="space-y-2">
                <CardTitle>{category.title}</CardTitle>
                <p className="text-muted-foreground">{category.description}</p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
