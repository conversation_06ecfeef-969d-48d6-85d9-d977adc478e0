#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixDatabaseConnection() {
  try {
    console.log('🔧 إصلاح اتصال قاعدة البيانات...');

    // البحث عن الاتصال الافتراضي
    const defaultConnection = await prisma.databaseConnection.findFirst({
      where: { isDefault: true }
    });

    if (defaultConnection) {
      console.log(`📋 تحديث الاتصال: ${defaultConnection.name}`);
      
      // تحديث الاتصال بالمعلومات الصحيحة
      const updatedConnection = await prisma.databaseConnection.update({
        where: { id: defaultConnection.id },
        data: {
          username: 'deviceflow_user',
          password: 'om772828',
          host: 'localhost',
          port: 5432,
          database: 'deviceflow_db'
        }
      });

      console.log('✅ تم تحديث الاتصال بنجاح:');
      console.log(`   - المستخدم: ${updatedConnection.username}`);
      console.log(`   - قاعدة البيانات: ${updatedConnection.database}`);
      console.log(`   - الخادم: ${updatedConnection.host}:${updatedConnection.port}`);
    } else {
      console.log('ℹ️ لم يتم العثور على اتصال افتراضي، سيتم إنشاء اتصال جديد...');
      
      // إنشاء اتصال جديد
      const newConnection = await prisma.databaseConnection.create({
        data: {
          name: 'الاتصال الرئيسي المصحح',
          host: 'localhost',
          port: 5432,
          database: 'deviceflow_db',
          username: 'deviceflow_user',
          password: 'om772828',
          isActive: true,
          isDefault: true
        }
      });

      console.log('✅ تم إنشاء اتصال جديد:');
      console.log(`   - ID: ${newConnection.id}`);
      console.log(`   - الاسم: ${newConnection.name}`);
    }

    // التحقق من النتيجة
    console.log('\n🔍 التحقق من الاتصال المحدث...');
    const connections = await prisma.databaseConnection.findMany({
      where: { isDefault: true }
    });

    if (connections.length > 0) {
      const conn = connections[0];
      console.log('✅ الاتصال الافتراضي الحالي:');
      console.log(`   - المستخدم: ${conn.username}`);
      console.log(`   - قاعدة البيانات: ${conn.database}`);
      console.log(`   - كلمة المرور: ${conn.password ? 'محددة' : 'غير محددة'}`);
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح الاتصال:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixDatabaseConnection();
