// اختبار الواجهة من الداخل
async function testFrontendData() {
  try {
    console.log('🧪 اختبار بيانات الواجهة...\n');
    
    // اختبار 1: جلب البيانات كما تفعل الواجهة
    console.log('1️⃣ اختبار جلب المخازن...');
    const warehousesResponse = await fetch('http://localhost:9005/api/warehouses-simple?limit=100');
    if (warehousesResponse.ok) {
      const warehousesResult = await warehousesResponse.json();
      const warehouses = warehousesResult.data || warehousesResult;
      console.log(`✅ المخازن: ${warehouses.length} مخزن`);
      warehouses.forEach(w => console.log(`   - ${w.name} (${w.location})`));
    } else {
      console.log(`❌ خطأ في المخازن: ${warehousesResponse.status}`);
    }
    
    console.log('\n2️⃣ اختبار جلب العملاء...');
    const clientsResponse = await fetch('http://localhost:9005/api/clients-simple?limit=100');
    if (clientsResponse.ok) {
      const clientsResult = await clientsResponse.json();
      const clients = clientsResult.data || clientsResult;
      console.log(`✅ العملاء: ${clients.length} عميل`);
      clients.forEach(c => console.log(`   - ${c.name} (${c.phone || 'بدون رقم'})`));
    } else {
      console.log(`❌ خطأ في العملاء: ${clientsResponse.status}`);
    }
    
    console.log('\n3️⃣ اختبار جلب الموردين...');
    const suppliersResponse = await fetch('http://localhost:9005/api/suppliers-simple?limit=100');
    if (suppliersResponse.ok) {
      const suppliersResult = await suppliersResponse.json();
      const suppliers = suppliersResult.data || suppliersResult;
      console.log(`✅ الموردين: ${suppliers.length} مورد`);
      suppliers.forEach(s => console.log(`   - ${s.name} (${s.phone || 'بدون رقم'})`));
    } else {
      console.log(`❌ خطأ في الموردين: ${suppliersResponse.status}`);
    }
    
    console.log('\n🎯 ملخص: هذه هي البيانات التي ستراها الواجهة');
    
  } catch (error) {
    console.error('❌ خطأ عام في الاختبار:', error.message);
  }
}

testFrontendData();
