/* تحسينات إضافية لنموذج المستخدم */

/* تحسين مظهر الـ Avatar */
.user-avatar {
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* تحسين مظهر الحقول */
.form-input {
  transition: all 0.2s ease;
  border: 1.5px solid #e2e8f0;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* تحسين مظهر الأزرار */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* تحسين مظهر التبويبات */
.tab-trigger {
  transition: all 0.2s ease;
  position: relative;
}

.tab-trigger.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 2px;
}

/* تحسين مظهر قائمة الأقسام */
.section-item {
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.section-item:hover {
  background-color: #f8fafc;
  border-color: #e2e8f0;
  transform: translateX(-2px);
}

.section-item.selected {
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
  border-color: #3b82f6;
  transform: translateX(-4px);
}

/* تحسين مظهر بطاقات الصلاحيات */
.permission-card {
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.permission-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

/* تحسين مظهر checkboxes الصلاحيات */
.permission-checkbox {
  transition: all 0.2s ease;
  border-radius: 6px;
}

.permission-checkbox:hover {
  background-color: rgba(59, 130, 246, 0.05);
  transform: scale(1.02);
}

/* تحسين الرسائل الخطأ */
.error-message {
  animation: slideIn 0.3s ease;
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين مظهر النافذة */
.dialog-content {
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e2e8f0;
}

.dialog-header {
  border-radius: 16px 16px 0 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.dialog-footer {
  border-radius: 0 0 16px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* تحسين الـ scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* تحسين responsive design */
@media (max-width: 768px) {
  .dialog-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .permission-grid {
    grid-template-columns: 1fr;
  }
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
  .form-input {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .section-item {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .permission-card {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: #374151;
  }
}
