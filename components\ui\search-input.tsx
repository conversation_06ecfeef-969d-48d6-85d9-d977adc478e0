"use client";

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchInputProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  onClear?: () => void;
  debounceMs?: number;
  isLoading?: boolean;
  className?: string;
  showSearchButton?: boolean;
  showClearButton?: boolean;
  disabled?: boolean;
  size?: 'sm' | 'default' | 'lg';
}

export function SearchInput({
  placeholder = "البحث...",
  value = "",
  onChange,
  onSearch,
  onClear,
  debounceMs = 300,
  isLoading = false,
  className = "",
  showSearchButton = false,
  showClearButton = true,
  disabled = false,
  size = 'default'
}: SearchInputProps) {
  const [internalValue, setInternalValue] = useState(value);
  const [debouncedValue, setDebouncedValue] = useState(value);

  // Update internal value when external value changes
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // Debounce the search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(internalValue);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [internalValue, debounceMs]);

  // Call onChange when debounced value changes
  useEffect(() => {
    if (debouncedValue !== value) {
      onChange?.(debouncedValue);
      onSearch?.(debouncedValue);
    }
  }, [debouncedValue, onChange, onSearch, value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
  };

  const handleClear = () => {
    setInternalValue("");
    setDebouncedValue("");
    onChange?.("");
    onClear?.();
  };

  const handleSearch = () => {
    setDebouncedValue(internalValue);
    onSearch?.(internalValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
    if (e.key === 'Escape') {
      handleClear();
    }
  };

  const sizeClasses = {
    sm: 'h-8 text-sm',
    default: 'h-10',
    lg: 'h-12 text-lg'
  };

  return (
    <div className={cn("relative flex items-center", className)}>
      {/* Search Icon */}
      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
        ) : (
          <Search className="h-4 w-4 text-muted-foreground" />
        )}
      </div>

      {/* Input Field */}
      <Input
        type="text"
        placeholder={placeholder}
        value={internalValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        disabled={disabled || isLoading}
        className={cn(
          "pl-10",
          sizeClasses[size],
          (showClearButton && internalValue) && "pr-10",
          showSearchButton && "rounded-l-md rounded-r-none border-r-0"
        )}
      />

      {/* Clear Button */}
      {showClearButton && internalValue && !isLoading && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleClear}
          disabled={disabled}
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted z-10"
        >
          <X className="h-3 w-3" />
        </Button>
      )}

      {/* Search Button */}
      {showSearchButton && (
        <Button
          type="button"
          onClick={handleSearch}
          disabled={disabled || isLoading}
          className={cn(
            "rounded-l-none rounded-r-md border-l-0",
            sizeClasses[size]
          )}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Search className="h-4 w-4" />
          )}
        </Button>
      )}
    </div>
  );
}

// Advanced Search Input with Filters
interface AdvancedSearchInputProps extends SearchInputProps {
  filters?: {
    key: string;
    label: string;
    options: { value: string; label: string }[];
  }[];
  selectedFilters?: Record<string, string>;
  onFiltersChange?: (filters: Record<string, string>) => void;
}

export function AdvancedSearchInput({
  filters = [],
  selectedFilters = {},
  onFiltersChange,
  ...searchProps
}: AdvancedSearchInputProps) {
  const [showFilters, setShowFilters] = useState(false);

  const handleFilterChange = (filterKey: string, value: string) => {
    const newFilters = { ...selectedFilters };
    if (value === 'all' || value === '') {
      delete newFilters[filterKey];
    } else {
      newFilters[filterKey] = value;
    }
    onFiltersChange?.(newFilters);
  };

  const activeFiltersCount = Object.keys(selectedFilters).length;

  return (
    <div className="space-y-2">
      <div className="flex gap-2">
        <SearchInput {...searchProps} className="flex-1" />
        
        {filters.length > 0 && (
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="relative"
          >
            <Search className="h-4 w-4 ml-2" />
            فلاتر
            {activeFiltersCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {activeFiltersCount}
              </span>
            )}
          </Button>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && filters.length > 0 && (
        <div className="p-4 border rounded-lg bg-muted/50 space-y-3">
          <div className="flex justify-between items-center">
            <h4 className="font-medium">فلاتر البحث</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFiltersChange?.({})}
              disabled={activeFiltersCount === 0}
            >
              مسح الكل
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {filters.map((filter) => (
              <div key={filter.key} className="space-y-1">
                <label className="text-sm font-medium">{filter.label}</label>
                <select
                  value={selectedFilters[filter.key] || 'all'}
                  onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                  className="w-full p-2 border rounded-md bg-background"
                >
                  <option value="all">الكل</option>
                  {filter.options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Quick Search with Suggestions
interface QuickSearchProps extends SearchInputProps {
  suggestions?: string[];
  onSuggestionClick?: (suggestion: string) => void;
  maxSuggestions?: number;
}

export function QuickSearch({
  suggestions = [],
  onSuggestionClick,
  maxSuggestions = 5,
  ...searchProps
}: QuickSearchProps) {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);

  useEffect(() => {
    if (searchProps.value && suggestions.length > 0) {
      const filtered = suggestions
        .filter(suggestion => 
          suggestion.toLowerCase().includes(searchProps.value!.toLowerCase())
        )
        .slice(0, maxSuggestions);
      setFilteredSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
    } else {
      setShowSuggestions(false);
    }
  }, [searchProps.value, suggestions, maxSuggestions]);

  const handleSuggestionClick = (suggestion: string) => {
    onSuggestionClick?.(suggestion);
    setShowSuggestions(false);
  };

  return (
    <div className="relative">
      <SearchInput
        {...searchProps}
        onFocus={() => filteredSuggestions.length > 0 && setShowSuggestions(true)}
        onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
      />
      
      {/* Suggestions Dropdown */}
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-background border rounded-md shadow-lg max-h-60 overflow-y-auto">
          {filteredSuggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full text-right px-3 py-2 hover:bg-muted transition-colors"
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
