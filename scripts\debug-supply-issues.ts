import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugSupplyIssues() {
  console.log('🔍 فحص شامل لمشاكل التوريد...\n');

  try {
    // 1. فحص جميع أوامر التوريد
    console.log('1️⃣ فحص أوامر التوريد:');
    const supplyOrders = await prisma.supplyOrder.findMany({
      orderBy: { createdAt: 'desc' }
    });

    console.log(`📦 إجمالي أوامر التوريد: ${supplyOrders.length}`);
    
    supplyOrders.forEach(order => {
      console.log(`\n🏷️  أمر رقم: ${order.id} (${order.supplyOrderId})`);
      console.log(`   📅 التاريخ: ${order.createdAt.toISOString()}`);
      console.log(`   🏪 المخزن: ${order.warehouseId}`);
      console.log(`   � المورد: ${order.supplierId}`);
      console.log(`   � الموظف: ${order.employeeName}`);
      console.log(`   📊 الحالة: ${order.status}`);
      console.log(`   � الملاحظات: ${order.notes || 'بدون ملاحظات'}`);
    });

    // 2. فحص الأجهزة المتاحة
    console.log('\n\n2️⃣ فحص الأجهزة المتاحة:');
    const devices = await prisma.device.findMany({
      orderBy: { dateAdded: 'desc' }
    });

    console.log(`📱 إجمالي الأجهزة: ${devices.length}`);
    
    const devicesByStatus = devices.reduce((acc, device) => {
      acc[device.status] = (acc[device.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('📊 الأجهزة حسب الحالة:');
    Object.entries(devicesByStatus).forEach(([status, count]) => {
      console.log(`   ${status}: ${count}`);
    });

    // عرض أحدث 10 أجهزة
    console.log('\n📱 أحدث 10 أجهزة:');
    devices.slice(0, 10).forEach(device => {
      console.log(`   📱 ${device.id} - ${device.model} - ${device.status} - مخزن: ${device.warehouseId}`);
    });

    // 3. فحص الأجهزة حسب المخازن
    console.log('\n\n3️⃣ فحص الأجهزة حسب المخازن:');
    const warehouses = await prisma.warehouse.findMany();

    for (const warehouse of warehouses) {
      console.log(`🏪 مخزن: ${warehouse.name} (ID: ${warehouse.id})`);
      
      const warehouseDevices = await prisma.device.findMany({
        where: { warehouseId: warehouse.id }
      });
      
      console.log(`   📱 عدد الأجهزة: ${warehouseDevices.length}`);
      
      if (warehouseDevices.length > 0) {
        const devicesByStatus = warehouseDevices.reduce((acc, device) => {
          acc[device.status] = (acc[device.status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        Object.entries(devicesByStatus).forEach(([status, count]) => {
          console.log(`     ${status}: ${count}`);
        });
      }
    }

    // 4. فحص المسودات
    console.log('\n\n4️⃣ فحص مسودات التوريد:');
    const drafts = await prisma.$queryRaw`
      SELECT * FROM "SupplyOrderDraft" 
      ORDER BY "updatedAt" DESC
    `;

    if (Array.isArray(drafts)) {
      console.log(`📝 إجمالي المسودات: ${drafts.length}`);
      
      drafts.forEach((draft: any) => {
        console.log(`\n📝 مسودة: ${draft.draftId}`);
        console.log(`   👤 المستخدم: ${draft.userId}`);
        console.log(`   📅 آخر تحديث: ${draft.updatedAt}`);
        console.log(`   📱 عدد الأجهزة: ${draft.items ? JSON.parse(draft.items).length : 0}`);
      });
    }

    // 5. فحص جدول items القديم (إذا كان موجوداً)
    console.log('\n\n5️⃣ فحص جدول الأجهزة في أوامر التوريد القديمة:');
    try {
      const items = await prisma.$queryRaw`
        SELECT * FROM "SupplyOrderItem" 
        ORDER BY "createdAt" DESC
        LIMIT 10
      `;
      
      if (Array.isArray(items)) {
        console.log(`📋 إجمالي العناصر في الجدول القديم: ${items.length}`);
        items.forEach((item: any) => {
          console.log(`   📱 IMEI: ${item.imei} - أمر: ${item.supplyOrderId}`);
        });
      }
    } catch (error) {
      console.log('ℹ️  جدول SupplyOrderItem غير موجود (هذا طبيعي في النسخة الجديدة)');
    }

    // 6. البحث عن الجهاز المفقود تحديداً
    console.log('\n\n6️⃣ البحث عن الجهاز 111111111111111:');
    const missingDevice = await prisma.device.findUnique({
      where: { id: '111111111111111' }
    });

    if (missingDevice) {
      console.log('✅ تم العثور على الجهاز:');
      console.log(`   📱 الرقم التسلسلي: ${missingDevice.id}`);
      console.log(`   📊 الحالة: ${missingDevice.status}`);
      console.log(`   🏪 المخزن ID: ${missingDevice.warehouseId}`);
      console.log(`   🏢 المورد ID: ${missingDevice.supplierId}`);
      console.log(`   📱 الموديل: ${missingDevice.model}`);
      console.log(`   � التخزين: ${missingDevice.storage}`);
      console.log(`   💰 السعر: ${missingDevice.price}`);
      console.log(`   � الحالة: ${missingDevice.condition}`);
      console.log(`   📅 تاريخ الإضافة: ${missingDevice.dateAdded.toISOString()}`);
    } else {
      console.log('❌ لم يتم العثور على الجهاز في قاعدة البيانات');
      
      // البحث في جميع الأجهزة للتأكد
      console.log('\n🔍 البحث في جميع الأجهزة المحتوية على "111111"...');
      const similarDevices = await prisma.device.findMany({
        where: {
          id: {
            contains: '111111'
          }
        }
      });
      
      if (similarDevices.length > 0) {
        console.log(`📱 تم العثور على ${similarDevices.length} جهاز مشابه:`);
        similarDevices.forEach(device => {
          console.log(`   📱 ${device.id} - ${device.model} - ${device.status}`);
        });
      } else {
        console.log('❌ لا توجد أجهزة مشابهة');
      }
    }

  } catch (error) {
    console.error('❌ خطأ في الفحص:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugSupplyIssues();
