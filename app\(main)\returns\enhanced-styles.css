/* تحسينات مظهر صفحة المرتجعات */

/* ===== التحسينات العامة للصفحة ===== */
.returns-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding: 1rem;
}

.page-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding: 1rem;
}

/* ===== تحسينات بطاقة الرأس ===== */
.header-card {
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6, #ec4899);
  animation: gradient-shift 4s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* ===== تحسينات البطاقات الرئيسية ===== */
.enhanced-card,
.enhanced-returns-card,
.enhanced-stocktake-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.25rem;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  color: #1f2937 !important;
}

/* ألوان مختلفة للبطاقات */
.card-new-stocktake { --card-accent: #3b82f6; --card-accent-end: #1d4ed8; }
.card-inventory-list { --card-accent: #10b981; --card-accent-end: #059669; }
.card-results { --card-accent: #f59e0b; --card-accent-end: #d97706; }

.enhanced-card::before,
.enhanced-returns-card::before,
.enhanced-stocktake-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--card-accent, #3b82f6), var(--card-accent-end, #6366f1));
}

.enhanced-card:hover,
.enhanced-returns-card:hover,
.enhanced-stocktake-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.95);
}

/* إصلاحات التباين والوضوح */
.enhanced-card,
.enhanced-returns-card,
.enhanced-stocktake-card,
.info-section,
.return-section,
.items-section,
.summary-section,
.actions-section {
  color: #1f2937 !important;
}

.enhanced-card h1,
.enhanced-card h2,
.enhanced-card h3,
.enhanced-card h4,
.enhanced-card h5,
.enhanced-card h6,
.enhanced-returns-card h1,
.enhanced-returns-card h2,
.enhanced-returns-card h3,
.enhanced-returns-card h4,
.enhanced-returns-card h5,
.enhanced-returns-card h6,
.enhanced-stocktake-card h1,
.enhanced-stocktake-card h2,
.enhanced-stocktake-card h3,
.enhanced-stocktake-card h4,
.enhanced-stocktake-card h5,
.enhanced-stocktake-card h6 {
  color: #1f2937 !important;
}

.enhanced-card p,
.enhanced-returns-card p,
.enhanced-stocktake-card p,
.info-section p,
.return-section p,
.items-section p,
.summary-section p,
.actions-section p {
  color: #4b5563 !important;
}

.enhanced-card label,
.enhanced-returns-card label,
.enhanced-stocktake-card label,
.info-section label,
.return-section label,
.items-section label,
.summary-section label,
.actions-section label {
  color: #374151 !important;
  font-weight: 600;
}

/* ألوان مختلفة للبطاقات */
.card-primary { --card-accent: #3b82f6; --card-accent-end: #1d4ed8; }
.card-success { --card-accent: #10b981; --card-accent-end: #059669; }
.card-warning { --card-accent: #f59e0b; --card-accent-end: #d97706; }
.card-danger { --card-accent: #ef4444; --card-accent-end: #dc2626; }
.card-info { --card-accent: #06b6d4; --card-accent-end: #0891b2; }
.card-purple { --card-accent: #8b5cf6; --card-accent-end: #7c3aed; }

/* ===== تحسينات الأقسام المعلوماتية ===== */
.info-section {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.info-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #3b82f6, #6366f1);
  border-radius: 0 2px 2px 0;
}

.success-section {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.success-section::before {
  background: linear-gradient(to bottom, #10b981, #059669);
}

.warning-section {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(217, 119, 6, 0.05) 100%);
  border: 1px solid rgba(245, 158, 11, 0.1);
}

.warning-section::before {
  background: linear-gradient(to bottom, #f59e0b, #d97706);
}

/* ===== تحسينات حقول الإدخال ===== */
.enhanced-input {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(5px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  color: #1f2937 !important;
  font-weight: 500 !important;
}

.enhanced-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.98) !important;
  color: #1f2937 !important;
}

.enhanced-input:disabled {
  background: rgba(243, 244, 246, 0.9) !important;
  color: #6b7280 !important;
  opacity: 0.8;
}

.enhanced-input::placeholder {
  color: #6b7280 !important;
  opacity: 1;
  font-weight: 400;
}

/* تحسين القوائم المنسدلة */
select,
.select-trigger {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(203, 213, 225, 0.5) !important;
  border-radius: 0.75rem !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
}

select:focus,
.select-trigger:focus {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  color: #1f2937 !important;
}

select option {
  color: #1f2937 !important;
  background: white !important;
}

/* تحسين منطقة النص */
textarea {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(203, 213, 225, 0.5) !important;
  border-radius: 0.75rem !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
}

textarea:focus {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  color: #1f2937 !important;
}

textarea::placeholder {
  color: #6b7280 !important;
  font-weight: 400;
}

/* ===== تحسينات الأزرار ===== */
.enhanced-button {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  color: #1f2937 !important;
  font-weight: 500 !important;
  text-shadow: none !important;
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.enhanced-button:hover::before {
  left: 100%;
}

.enhanced-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.enhanced-button:active {
  transform: scale(0.98);
}

/* أنواع الأزرار */
.enhanced-button.variant-outline {
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db !important;
}

.enhanced-button.variant-outline:hover {
  background: rgba(255, 255, 255, 1) !important;
  border: 1px solid #9ca3af !important;
}

.enhanced-button.variant-destructive {
  background: #dc2626 !important;
  color: white !important;
  border: none !important;
}

.enhanced-button.variant-destructive:hover {
  background: #b91c1c !important;
}

/* ===== تحسينات الجداول ===== */
.enhanced-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.enhanced-table thead {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
}

.enhanced-table th {
  color: #1f2937 !important;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem;
  text-align: right;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2);
}

.enhanced-table td {
  color: #374151 !important;
  font-size: 0.875rem;
  padding: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  vertical-align: middle;
}

.enhanced-table tr:hover {
  background: rgba(59, 130, 246, 0.05) !important;
}

.enhanced-table tr:hover td {
  color: #1f2937 !important;
}

.row-number {
  font-weight: 600;
  color: #6b7280 !important;
  text-align: center;
  width: 40px;
}

/* ===== تحسينات منطقة التمرير ===== */
.enhanced-scroll-area {
  max-height: 400px;
  overflow-y: auto;
  border-radius: 0.5rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.enhanced-scroll-area::-webkit-scrollbar {
  width: 8px;
}

.enhanced-scroll-area::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.enhanced-scroll-area::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.enhanced-scroll-area::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* ===== تحسينات الشارات ===== */
.enhanced-badge {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  padding: 0.25rem 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  color: inherit !important;
}

.enhanced-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 1) !important;
}

/* ===== تحسينات الأيقونات ===== */
.icon-enhanced {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.icon-enhanced:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* ===== تأثيرات التحميل ===== */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer-loading 1.5s infinite;
  border-radius: 0.5rem;
}

@keyframes shimmer-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ===== تحسينات النوافذ المنبثقة ===== */
.enhanced-dialog {
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.enhanced-dialog-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 1rem 1rem 0 0;
  padding: 1.5rem;
  position: relative;
}

.enhanced-dialog-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6);
  border-radius: 1rem 1rem 0 0;
}

.enhanced-dialog-title {
  color: #1f2937 !important;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.enhanced-dialog-description {
  color: #6b7280 !important;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* تحسينات جداول النوافذ المنبثقة */
.enhanced-modal-table {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.enhanced-modal-table thead {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
}

.enhanced-modal-table th {
  color: #1f2937 !important;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem;
  text-align: right;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2);
}

.enhanced-modal-table td {
  color: #374151 !important;
  font-size: 0.875rem;
  padding: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  vertical-align: middle;
}

.enhanced-modal-table tr:hover {
  background: rgba(59, 130, 246, 0.05) !important;
}

.enhanced-modal-table tr:hover td {
  color: #1f2937 !important;
}

/* تحسينات أزرار النوافذ المنبثقة */
.enhanced-modal-button {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: #1f2937 !important;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.enhanced-modal-button:hover {
  background: rgba(255, 255, 255, 1) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-modal-button.primary {
  background: #3b82f6 !important;
  color: white !important;
  border: none !important;
}

.enhanced-modal-button.primary:hover {
  background: #2563eb !important;
}

.enhanced-modal-button.destructive {
  background: #ef4444 !important;
  color: white !important;
  border: none !important;
}

.enhanced-modal-button.destructive:hover {
  background: #dc2626 !important;
}

/* ===== إصلاحات التباين والوضوح ===== */
.enhanced-card,
.info-section,
.success-section,
.warning-section {
  color: #1f2937 !important;
}

.enhanced-card h1,
.enhanced-card h2,
.enhanced-card h3,
.enhanced-card h4,
.enhanced-card h5,
.enhanced-card h6,
.info-section h1,
.info-section h2,
.info-section h3,
.info-section h4,
.info-section h5,
.info-section h6 {
  color: #1f2937 !important;
}

.enhanced-card p,
.info-section p {
  color: #4b5563 !important;
}

.enhanced-card label,
.info-section label {
  color: #374151 !important;
}

/* ألوان محددة للشارات */
.enhanced-badge.bg-blue-500\/10 {
  color: #1e40af !important;
}

.enhanced-badge.bg-green-500\/10 {
  color: #166534 !important;
}

.enhanced-badge.bg-red-500\/10 {
  color: #dc2626 !important;
}

.enhanced-badge.bg-yellow-500\/10 {
  color: #ca8a04 !important;
}

.enhanced-badge.bg-purple-500\/10 {
  color: #7c3aed !important;
}

.enhanced-badge.bg-gray-500\/10 {
  color: #374151 !important;
}

/* ===== الوضع الليلي (Dark Mode) ===== */

/* متغيرات الألوان للوضع الليلي */
.dark-mode {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
  --accent-primary: #3b82f6;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
}

/* خلفية الصفحة في الوضع الليلي */
.dark-mode .page-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: var(--text-primary);
}

/* البطاقات في الوضع الليلي */
.dark-mode .enhanced-card,
.dark-mode .info-section,
.dark-mode .success-section,
.dark-mode .warning-section {
  background: rgba(30, 41, 59, 0.9) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-card::before,
.dark-mode .info-section::before,
.dark-mode .success-section::before,
.dark-mode .warning-section::before {
  background: linear-gradient(90deg, var(--accent-primary), #6366f1, #8b5cf6);
}

/* النصوص في الوضع الليلي */
.dark-mode .enhanced-card h1,
.dark-mode .enhanced-card h2,
.dark-mode .enhanced-card h3,
.dark-mode .enhanced-card h4,
.dark-mode .enhanced-card h5,
.dark-mode .enhanced-card h6,
.dark-mode .info-section h1,
.dark-mode .info-section h2,
.dark-mode .info-section h3,
.dark-mode .info-section h4,
.dark-mode .info-section h5,
.dark-mode .info-section h6 {
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-card p,
.dark-mode .info-section p,
.dark-mode .enhanced-card label,
.dark-mode .info-section label {
  color: var(--text-secondary) !important;
}

/* حقول الإدخال في الوضع الليلي */
.dark-mode .enhanced-input {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 2px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-input:focus {
  background: rgba(51, 65, 85, 0.95) !important;
  border-color: var(--accent-primary) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
}

.dark-mode .enhanced-input:disabled {
  background: rgba(71, 85, 105, 0.5) !important;
  color: var(--text-muted) !important;
}

.dark-mode .enhanced-input::placeholder {
  color: var(--text-muted) !important;
}

/* الأزرار في الوضع الليلي */
.dark-mode .enhanced-button {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button:hover {
  background: rgba(51, 65, 85, 1) !important;
  border-color: rgba(71, 85, 105, 0.8) !important;
}

.dark-mode .enhanced-button.variant-outline {
  background: rgba(30, 41, 59, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button.variant-outline:hover {
  background: rgba(51, 65, 85, 0.9) !important;
}

/* الشارات في الوضع الليلي */
.dark-mode .enhanced-badge {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-badge.bg-blue-500\/10 {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-green-500\/10 {
  background: rgba(16, 185, 129, 0.2) !important;
  color: #6ee7b7 !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-red-500\/10 {
  background: rgba(239, 68, 68, 0.2) !important;
  color: #fca5a5 !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-yellow-500\/10 {
  background: rgba(245, 158, 11, 0.2) !important;
  color: #fcd34d !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-purple-500\/10 {
  background: rgba(139, 92, 246, 0.2) !important;
  color: #c4b5fd !important;
  border-color: rgba(139, 92, 246, 0.3) !important;
}

.dark-mode .enhanced-badge.bg-gray-500\/10 {
  background: rgba(107, 114, 128, 0.2) !important;
  color: #d1d5db !important;
  border-color: rgba(107, 114, 128, 0.3) !important;
}

/* النوافذ المنبثقة في الوضع الليلي */
.dark-mode .enhanced-dialog {
  background: rgba(30, 41, 59, 0.98) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-dialog-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
  border-bottom: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-dialog-title {
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-dialog-description {
  color: var(--text-secondary) !important;
}

/* جداول النوافذ المنبثقة في الوضع الليلي */
.dark-mode .enhanced-modal-table {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-modal-table thead {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(99, 102, 241, 0.15) 100%);
}

.dark-mode .enhanced-modal-table th {
  color: var(--text-primary) !important;
  border-bottom: 2px solid rgba(71, 85, 105, 0.4) !important;
}

.dark-mode .enhanced-modal-table td {
  color: var(--text-secondary) !important;
  border-bottom: 1px solid rgba(71, 85, 105, 0.2) !important;
}

.dark-mode .enhanced-modal-table tr:hover {
  background: rgba(59, 130, 246, 0.1) !important;
}

.dark-mode .enhanced-modal-table tr:hover td {
  color: var(--text-primary) !important;
}

/* أزرار النوافذ المنبثقة في الوضع الليلي */
.dark-mode .enhanced-modal-button {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-modal-button:hover {
  background: rgba(51, 65, 85, 1) !important;
}

.dark-mode .enhanced-modal-button.primary {
  background: var(--accent-primary) !important;
  color: white !important;
}

.dark-mode .enhanced-modal-button.primary:hover {
  background: #2563eb !important;
}

.dark-mode .enhanced-modal-button.destructive {
  background: var(--accent-danger) !important;
  color: white !important;
}

.dark-mode .enhanced-modal-button.destructive:hover {
  background: #dc2626 !important;
}

/* شريط التمرير في الوضع الليلي */
.dark-mode .enhanced-scroll-area::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* تحسينات إضافية للوضع الليلي */
.dark-mode .bg-gradient-to-r {
  background: linear-gradient(to right, #93c5fd, #ddd6fe) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  color: transparent !important;
}

.dark-mode .icon-enhanced {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.dark-mode .text-muted-foreground {
  color: var(--text-secondary) !important;
}

.dark-mode .text-gray-500,
.dark-mode .text-gray-600,
.dark-mode .text-gray-700 {
  color: var(--text-secondary) !important;
}

.dark-mode .text-gray-800,
.dark-mode .text-gray-900 {
  color: var(--text-primary) !important;
}

.dark-mode .border-gray-300,
.dark-mode .border-gray-200 {
  border-color: rgba(71, 85, 105, 0.5) !important;
}

.dark-mode .bg-white {
  background: rgba(30, 41, 59, 0.9) !important;
}

.dark-mode .bg-gray-50 {
  background: rgba(51, 65, 85, 0.5) !important;
}

.dark-mode .bg-gray-100 {
  background: rgba(51, 65, 85, 0.7) !important;
}

/* تحسين الانتقالات السلسة */
.dark-mode * {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

html {
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
  .page-container {
    padding: 0.5rem;
  }

  .enhanced-card {
    border-radius: 1rem;
  }

  .info-section,
  .success-section,
  .warning-section {
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .enhanced-scroll-area {
    max-height: 300px;
  }

  .enhanced-dialog {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
}

/* ===== تحسينات الطباعة ===== */
@media print {
  .enhanced-card,
  .info-section,
  .success-section,
  .warning-section,
  .enhanced-dialog {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    color: #000 !important;
  }

  .enhanced-card::before,
  .info-section::before,
  .success-section::before,
  .warning-section::before,
  .enhanced-dialog-header::before {
    display: none !important;
  }

  .enhanced-input,
  .enhanced-badge,
  .enhanced-button {
    color: #000 !important;
    background: white !important;
  }
}

/* ===== الوضع الليلي (Dark Mode) ===== */

/* متغيرات الألوان للوضع الليلي */
.dark-mode {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
  --accent-primary: #3b82f6;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
}

/* خلفية الصفحة في الوضع الليلي */
.dark-mode .returns-page,
.dark-mode .page-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: var(--text-primary);
}

/* البطاقات في الوضع الليلي */
.dark-mode .enhanced-returns-card,
.dark-mode .enhanced-card,
.dark-mode .enhanced-stocktake-card,
.dark-mode .header-card,
.dark-mode .enhanced-dialog,
.dark-mode .info-section,
.dark-mode .return-section,
.dark-mode .items-section,
.dark-mode .summary-section,
.dark-mode .actions-section {
  background: rgba(30, 41, 59, 0.9) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-returns-card::before,
.dark-mode .enhanced-card::before,
.dark-mode .enhanced-stocktake-card::before,
.dark-mode .header-card::before,
.dark-mode .enhanced-dialog-header::before,
.dark-mode .info-section::before,
.dark-mode .return-section::before,
.dark-mode .items-section::before,
.dark-mode .summary-section::before,
.dark-mode .actions-section::before {
  background: linear-gradient(90deg, var(--accent-primary), #6366f1, #8b5cf6);
}

/* الحقول والمدخلات في الوضع الليلي */
.dark-mode .enhanced-input {
  background: rgba(51, 65, 85, 0.8) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode .enhanced-input:focus {
  background: rgba(51, 65, 85, 1) !important;
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-input::placeholder {
  color: var(--text-muted) !important;
  font-weight: 400;
}

/* القوائم المنسدلة في الوضع الليلي */
.dark-mode select,
.dark-mode .select-trigger {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode select:focus,
.dark-mode .select-trigger:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode select option {
  background: rgba(30, 41, 59, 0.95) !important;
  color: var(--text-primary) !important;
}

/* منطقة النص في الوضع الليلي */
.dark-mode textarea {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode textarea:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode textarea::placeholder {
  color: var(--text-muted) !important;
  font-weight: 400;
}

/* الأزرار في الوضع الليلي */
.dark-mode .enhanced-button {
  background: rgba(51, 65, 85, 0.8) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button:hover {
  background: rgba(71, 85, 105, 0.9) !important;
  border-color: var(--accent-primary) !important;
}

/* الأزرار البيضاء في الوضع الليلي */
.dark-mode .enhanced-button.bg-white {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.6) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button.bg-white:hover {
  background: rgba(71, 85, 105, 1) !important;
  border-color: var(--accent-primary) !important;
  color: var(--text-primary) !important;
}

/* الجداول في الوضع الليلي */
.dark-mode .enhanced-table {
  background: rgba(30, 41, 59, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-table-header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
}

.dark-mode .enhanced-table-row:hover {
  background: rgba(59, 130, 246, 0.1) !important;
}

.dark-mode .enhanced-table-cell {
  border-color: rgba(71, 85, 105, 0.3) !important;
  color: var(--text-primary) !important;
}

/* الشارات في الوضع الليلي */
.dark-mode .enhanced-badge {
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

/* النصوص في الوضع الليلي */
.dark-mode .enhanced-returns-card h1,
.dark-mode .enhanced-returns-card h2,
.dark-mode .enhanced-returns-card h3,
.dark-mode .enhanced-returns-card h4,
.dark-mode .enhanced-returns-card h5,
.dark-mode .enhanced-returns-card h6,
.dark-mode .enhanced-card h1,
.dark-mode .enhanced-card h2,
.dark-mode .enhanced-card h3,
.dark-mode .enhanced-card h4,
.dark-mode .enhanced-card h5,
.dark-mode .enhanced-card h6,
.dark-mode .enhanced-stocktake-card h1,
.dark-mode .enhanced-stocktake-card h2,
.dark-mode .enhanced-stocktake-card h3,
.dark-mode .enhanced-stocktake-card h4,
.dark-mode .enhanced-stocktake-card h5,
.dark-mode .enhanced-stocktake-card h6,
.dark-mode .info-section h1,
.dark-mode .info-section h2,
.dark-mode .info-section h3,
.dark-mode .info-section h4,
.dark-mode .info-section h5,
.dark-mode .info-section h6 {
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-returns-card p,
.dark-mode .enhanced-returns-card span,
.dark-mode .enhanced-returns-card div,
.dark-mode .enhanced-card p,
.dark-mode .enhanced-card span,
.dark-mode .enhanced-card div,
.dark-mode .enhanced-stocktake-card p,
.dark-mode .enhanced-stocktake-card span,
.dark-mode .enhanced-stocktake-card div,
.dark-mode .info-section p,
.dark-mode .return-section p,
.dark-mode .items-section p,
.dark-mode .summary-section p,
.dark-mode .actions-section p {
  color: var(--text-secondary) !important;
}

.dark-mode .enhanced-returns-card label,
.dark-mode .enhanced-card label,
.dark-mode .enhanced-stocktake-card label,
.dark-mode .info-section label,
.dark-mode .return-section label,
.dark-mode .items-section label,
.dark-mode .summary-section label,
.dark-mode .actions-section label {
  color: var(--text-primary) !important;
  font-weight: 500;
}

/* القوائم المنسدلة في الوضع الليلي */
.dark-mode select,
.dark-mode .select-trigger {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode select:focus,
.dark-mode .select-trigger:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
}

/* منطقة النص في الوضع الليلي */
.dark-mode textarea {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode textarea:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
}

.dark-mode textarea::placeholder {
  color: var(--text-muted) !important;
}
