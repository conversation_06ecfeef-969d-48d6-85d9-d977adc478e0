import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkCurrentStatus() {
  try {
    console.log('=== فحص حالة النظام الحالية ===\n');

    // فحص أوامر التوريد
    const orders = await prisma.supplyOrder.findMany({
      select: { 
        id: true, 
        supplyOrderId: true, 
        status: true, 
        employeeName: true,
        notes: true
      }
    });
    console.log('أوامر التوريد الحالية:');
    orders.forEach(order => {
      console.log(`- ${order.supplyOrderId} (${order.status}) - ${order.employeeName}`);
      console.log(`  ملاحظات: ${order.notes || 'لا توجد'}`);
    });

    // فحص الأجهزة
    const devices = await prisma.device.findMany({
      select: { 
        id: true, 
        model: true, 
        status: true, 
        warehouseId: true,
        storage: true,
        price: true,
        condition: true
      }
    });
    console.log('\nالأجهزة الحالية:');
    devices.forEach(device => {
      console.log(`- ${device.id} - ${device.model} (${device.status})`);
      console.log(`  التخزين: ${device.storage}`);
      console.log(`  السعر: ${device.price}`);
      console.log(`  الحالة: ${device.condition}`);
      console.log(`  معرف المخزن: ${device.warehouseId || 'غير محدد'}`);
    });

    // فحص المخازن
    const warehouses = await prisma.warehouse.findMany({
      select: { id: true, name: true, location: true }
    });
    console.log('\nالمخازن المتاحة:');
    warehouses.forEach(warehouse => {
      console.log(`- ${warehouse.name} (${warehouse.location})`);
    });

    // فحص المستخدمين وأدوارهم
    const users = await prisma.user.findMany({
      select: { id: true, username: true, role: true }
    });
    console.log('\nالمستخدمين وأدوارهم:');
    users.forEach(user => {
      console.log(`- ${user.username} (${user.role})`);
    });

  } catch (error) {
    console.error('خطأ في فحص حالة النظام:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentStatus();
