import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';

// مجلد المرفقات
const ATTACHMENTS_DIR = path.join(process.cwd(), 'public', 'attachments');

// التأكد من وجود مجلد المرفقات
async function ensureAttachmentsDir() {
  if (!existsSync(ATTACHMENTS_DIR)) {
    await mkdir(ATTACHMENTS_DIR, { recursive: true });
  }
}

// التحقق من نوع الملف المسموح
function isAllowedFileType(fileName: string): boolean {
  const allowedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.gif', '.txt'];
  const fileExtension = path.extname(fileName).toLowerCase();
  return allowedExtensions.includes(fileExtension);
}

// التحقق من حجم الملف (10MB كحد أقصى)
function isValidFileSize(fileSize: number): boolean {
  const maxSize = 10 * 1024 * 1024; // 10MB
  return fileSize <= maxSize;
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    await ensureAttachmentsDir();

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'لم يتم العثور على ملف' }, { status: 400 });
    }

    // التحقق من نوع الملف
    if (!isAllowedFileType(file.name)) {
      return NextResponse.json({
        error: 'نوع الملف غير مسموح. الأنواع المسموحة: PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG, GIF, TXT'
      }, { status: 400 });
    }

    // التحقق من حجم الملف
    if (!isValidFileSize(file.size)) {
      return NextResponse.json({
        error: 'حجم الملف كبير جداً. الحد الأقصى 10MB'
      }, { status: 400 });
    }

    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(file.name);
    const fileName = `${timestamp}_${randomString}${fileExtension}`;

    // مسار الملف
    const filePath = path.join(ATTACHMENTS_DIR, fileName);

    // تحويل الملف إلى Buffer وحفظه
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    await writeFile(filePath, buffer);

    // إنشاء audit log
    await executeInTransaction(async (tx) => {
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPLOAD',
        details: `Uploaded file: ${file.name} (${fileName})`,
        tableName: 'attachment',
        recordId: fileName
      });
    });

    // إرجاع معلومات الملف
    return NextResponse.json({
      success: true,
      fileName: fileName,
      originalName: file.name,
      size: file.size,
      type: file.type,
      url: `/attachments/${fileName}`
    });

  } catch (error) {
    console.error('خطأ في رفع الملف:', error);
    return NextResponse.json({ error: 'فشل في رفع الملف' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('file');

    if (!fileName) {
      return NextResponse.json({ error: 'اسم الملف مطلوب' }, { status: 400 });
    }

    // التحقق من أن اسم الملف آمن (لا يحتوي على مسارات خطيرة)
    if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      return NextResponse.json({ error: 'اسم ملف غير صالح' }, { status: 400 });
    }

    const filePath = path.join(ATTACHMENTS_DIR, fileName);

    if (!existsSync(filePath)) {
      return NextResponse.json({ error: 'الملف غير موجود' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      url: `/attachments/${fileName}`
    });

  } catch (error) {
    console.error('خطأ في جلب الملف:', error);
    return NextResponse.json({ error: 'فشل في جلب الملف' }, { status: 500 });
  }
}
