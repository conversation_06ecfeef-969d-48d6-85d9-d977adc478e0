import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function findDevice111111111111111() {
  try {
    console.log('🔍 البحث عن الجهاز 111111111111111 في قاعدة البيانات...\n');

    // 1. البحث في جدول الأجهزة
    console.log('1. البحث في جدول Device:');
    const deviceInDeviceTable = await prisma.device.findMany({
      where: {
        OR: [
          { id: '111111111111111' },
          { id: { contains: '111111111111111' } }
        ]
      }
    });

    if (deviceInDeviceTable.length > 0) {
      console.log(`   ✅ وُجد في جدول Device: ${deviceInDeviceTable.length} جهاز`);
      deviceInDeviceTable.forEach(device => {
        console.log(`   - ID: ${device.id}`);
        console.log(`   - الموديل: ${device.model}`);
        console.log(`   - الحالة: ${device.status}`);
        console.log(`   - المخزن: ${device.warehouseId}`);
        console.log(`   - المورد: ${device.supplierId}`);
      });
    } else {
      console.log('   ❌ غير موجود في جدول Device');
    }

    // 2. البحث في أوامر التوريد
    console.log('\n2. البحث في جدول SupplyOrder:');
    const supplyOrders = await prisma.supplyOrder.findMany();
    
    console.log(`   عدد أوامر التوريد: ${supplyOrders.length}`);
    supplyOrders.forEach(order => {
      console.log(`   - الأمر: ${order.supplyOrderId} (ID: ${order.id})`);
      console.log(`   - الحالة: ${order.status}`);
      console.log(`   - المورد: ${order.supplierId}`);
      console.log(`   - المخزن: ${order.warehouseId}`);
      console.log(`   - الموظف: ${order.employeeName}`);
    });

    // 3. البحث في جدول supply_order_items (إذا كان موجوداً)
    console.log('\n3. البحث في جدول supply_order_items:');
    try {
      const supplyOrderItems = await prisma.$queryRaw`
        SELECT * FROM supply_order_items 
        WHERE device_id = '111111111111111' OR imei = '111111111111111'
      `;
      console.log(`   عدد العناصر: ${Array.isArray(supplyOrderItems) ? supplyOrderItems.length : 0}`);
      if (Array.isArray(supplyOrderItems) && supplyOrderItems.length > 0) {
        console.log('   العناصر الموجودة:', supplyOrderItems);
      }
    } catch (error) {
      console.log('   ❌ خطأ في الوصول لجدول supply_order_items:', error.message);
    }

    // 4. البحث في جميع البيانات JSON
    console.log('\n4. البحث في البيانات JSON:');
    
    // البحث في المبيعات
    const salesWithDevice = await prisma.sale.findMany({
      where: {
        OR: [
          { items: { path: '$[*].deviceId', equals: '111111111111111' } },
          { items: { path: '$[*].imei', equals: '111111111111111' } }
        ]
      }
    });
    console.log(`   المبيعات المرتبطة: ${salesWithDevice.length}`);

    // البحث في المرتجعات
    const returnsWithDevice = await prisma.return.findMany({
      where: {
        OR: [
          { items: { path: '$[*].deviceId', equals: '111111111111111' } },
          { items: { path: '$[*].imei', equals: '111111111111111' } }
        ]
      }
    });
    console.log(`   المرتجعات المرتبطة: ${returnsWithDevice.length}`);

    // 5. فحص جميع الجداول للرقم
    console.log('\n5. البحث في جميع الجداول:');
    
    const allTables = [
      'AuditLog', 'Client', 'DeliveryOrder', 'Device', 'DeviceModel', 
      'MaintenanceOrder', 'MaintenanceReceiptOrder', 'Manufacturer', 
      'Return', 'Sale', 'Supplier', 'SupplyOrder', 'Warehouse'
    ];

    for (const tableName of allTables) {
      try {
        const result = await prisma.$queryRaw`
          SELECT * FROM ${prisma.$queryRawUnsafe(`"${tableName}"`)} 
          WHERE data::text LIKE '%111111111111111%' 
          OR items::text LIKE '%111111111111111%'
          OR id::text = '111111111111111'
        `;
        
        if (Array.isArray(result) && result.length > 0) {
          console.log(`   ✅ وُجد في جدول ${tableName}: ${result.length} سجل`);
          console.log('      البيانات:', result);
        }
      } catch (error) {
        // تجاهل الأخطاء للجداول التي لا تحتوي على الأعمدة المطلوبة
      }
    }

    // 6. فحص localStorage simulation (في النظام الحالي)
    console.log('\n6. فحص cache النظام:');
    console.log('   (يجب فحص هذا من الواجهة الأمامية)');

    console.log('\n=== خلاصة التحليل ===');
    if (deviceInDeviceTable.length > 0) {
      console.log('✅ الجهاز موجود في قاعدة البيانات');
    } else {
      console.log('❌ الجهاز غير موجود في جدول الأجهزة الرئيسي');
      console.log('💡 قد يكون محفوظ في:');
      console.log('   - Cache الواجهة الأمامية');
      console.log('   - localStorage/sessionStorage');
      console.log('   - حالة مؤقتة في النظام');
      console.log('   - جدول منفصل غير متزامن');
    }

  } catch (error) {
    console.error('❌ خطأ في البحث:', error);
  } finally {
    await prisma.$disconnect();
  }
}

findDevice111111111111111();
