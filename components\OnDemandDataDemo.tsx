"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, Filter, RefreshCw, BarChart3 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  fetchDevices, 
  fetchSales, 
  fetchClients, 
  dataFetcher 
} from '@/lib/data-fetcher';
import { globalCache, staticDataCache, dynamicDataCache } from '@/lib/cache-manager';
import { Device, Sale, Contact, ApiQueryParams, PaginatedResponse } from '@/lib/types';

/**
 * مكون توضيحي لنظام جلب البيانات عند الطلب
 */
export default function OnDemandDataDemo() {
  // --- STATE MANAGEMENT ---
  const [activeTab, setActiveTab] = useState<'devices' | 'sales' | 'clients'>('devices');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  // Data states
  const [devicesData, setDevicesData] = useState<PaginatedResponse<Device> | null>(null);
  const [salesData, setSalesData] = useState<PaginatedResponse<Sale> | null>(null);
  const [clientsData, setClientsData] = useState<PaginatedResponse<Contact> | null>(null);
  
  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Cache stats
  const [cacheStats, setCacheStats] = useState<any>(null);

  // --- DATA FETCHING ---
  
  const fetchData = async (forceRefresh = false) => {
    setIsLoading(true);
    
    try {
      const queryParams: ApiQueryParams = {
        pagination: {
          page: currentPage,
          limit: pageSize
        },
        sort: {
          field: sortField,
          direction: sortDirection
        },
        search: searchTerm ? {
          query: searchTerm
        } : undefined
      };

      const options = {
        forceRefresh,
        cache: !forceRefresh
      };

      switch (activeTab) {
        case 'devices':
          const devices = await fetchDevices(queryParams, options);
          setDevicesData(devices);
          break;
          
        case 'sales':
          const sales = await fetchSales(queryParams, options);
          setSalesData(sales);
          break;
          
        case 'clients':
          const clients = await fetchClients(queryParams, options);
          setClientsData(clients);
          break;
      }
      
      // Update cache stats
      updateCacheStats();
      
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const refreshData = async () => {
    setIsRefreshing(true);
    await fetchData(true);
  };

  const updateCacheStats = () => {
    setCacheStats({
      global: globalCache.getStats(),
      static: staticDataCache.getStats(),
      dynamic: dynamicDataCache.getStats()
    });
  };

  // --- EFFECTS ---
  
  useEffect(() => {
    fetchData();
  }, [activeTab, currentPage, pageSize, sortField, sortDirection]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm) {
        setCurrentPage(1);
        fetchData();
      } else if (searchTerm === '') {
        fetchData();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  useEffect(() => {
    updateCacheStats();
    const interval = setInterval(updateCacheStats, 5000);
    return () => clearInterval(interval);
  }, []);

  // --- RENDER HELPERS ---
  
  const getCurrentData = () => {
    switch (activeTab) {
      case 'devices': return devicesData;
      case 'sales': return salesData;
      case 'clients': return clientsData;
      default: return null;
    }
  };

  const renderDataTable = () => {
    const data = getCurrentData();
    if (!data) return null;

    return (
      <div className="space-y-4">
        {/* Pagination Info */}
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            عرض {((data.pagination.page - 1) * data.pagination.limit) + 1} إلى{' '}
            {Math.min(data.pagination.page * data.pagination.limit, data.pagination.total)} من{' '}
            {data.pagination.total} عنصر
          </span>
          <span>
            صفحة {data.pagination.page} من {data.pagination.totalPages}
          </span>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                {activeTab === 'devices' && (
                  <>
                    <th className="px-4 py-2 text-right">المعرف</th>
                    <th className="px-4 py-2 text-right">الموديل</th>
                    <th className="px-4 py-2 text-right">الحالة</th>
                    <th className="px-4 py-2 text-right">السعر</th>
                  </>
                )}
                {activeTab === 'sales' && (
                  <>
                    <th className="px-4 py-2 text-right">الرقم</th>
                    <th className="px-4 py-2 text-right">العميل</th>
                    <th className="px-4 py-2 text-right">التاريخ</th>
                    <th className="px-4 py-2 text-right">المجموع</th>
                  </>
                )}
                {activeTab === 'clients' && (
                  <>
                    <th className="px-4 py-2 text-right">الرقم</th>
                    <th className="px-4 py-2 text-right">الاسم</th>
                    <th className="px-4 py-2 text-right">الهاتف</th>
                    <th className="px-4 py-2 text-right">البريد</th>
                  </>
                )}
              </tr>
            </thead>
            <tbody>
              {data.data.map((item: any, index) => (
                <tr key={item.id || index} className="border-t hover:bg-gray-50">
                  {activeTab === 'devices' && (
                    <>
                      <td className="px-4 py-2">{item.id}</td>
                      <td className="px-4 py-2">{item.model}</td>
                      <td className="px-4 py-2">
                        <Badge variant="outline">{item.status}</Badge>
                      </td>
                      <td className="px-4 py-2">{item.price} ريال</td>
                    </>
                  )}
                  {activeTab === 'sales' && (
                    <>
                      <td className="px-4 py-2">{item.id}</td>
                      <td className="px-4 py-2">{item.clientName}</td>
                      <td className="px-4 py-2">{item.date}</td>
                      <td className="px-4 py-2">{item.total} ريال</td>
                    </>
                  )}
                  {activeTab === 'clients' && (
                    <>
                      <td className="px-4 py-2">{item.id}</td>
                      <td className="px-4 py-2">{item.name}</td>
                      <td className="px-4 py-2">{item.phone}</td>
                      <td className="px-4 py-2">{item.email}</td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={!data.pagination.hasPrev || isLoading}
            >
              السابق
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={!data.pagination.hasNext || isLoading}
            >
              التالي
            </Button>
          </div>
          
          <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5 عناصر</SelectItem>
              <SelectItem value="10">10 عناصر</SelectItem>
              <SelectItem value="20">20 عنصر</SelectItem>
              <SelectItem value="50">50 عنصر</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">نظام جلب البيانات عند الطلب</h1>
        <Button onClick={refreshData} disabled={isRefreshing} variant="outline">
          {isRefreshing ? <Loader2 className="w-4 h-4 animate-spin ml-2" /> : <RefreshCw className="w-4 h-4 ml-2" />}
          تحديث
        </Button>
      </div>

      {/* Cache Stats */}
      {cacheStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              إحصائيات التخزين المؤقت
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{cacheStats.global.totalEntries}</div>
                <div className="text-sm text-gray-600">التخزين العام</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{cacheStats.static.totalEntries}</div>
                <div className="text-sm text-gray-600">البيانات الثابتة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{cacheStats.dynamic.totalEntries}</div>
                <div className="text-sm text-gray-600">البيانات الديناميكية</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle>أدوات التحكم</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Tab Selection */}
            <Select value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="devices">الأجهزة</SelectItem>
                <SelectItem value="sales">المبيعات</SelectItem>
                <SelectItem value="clients">العملاء</SelectItem>
              </SelectContent>
            </Select>

            {/* Search */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="بحث..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>

            {/* Sort Field */}
            <Select value={sortField} onValueChange={setSortField}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="id">الرقم</SelectItem>
                <SelectItem value="createdAt">تاريخ الإنشاء</SelectItem>
                {activeTab === 'devices' && <SelectItem value="model">الموديل</SelectItem>}
                {activeTab === 'devices' && <SelectItem value="price">السعر</SelectItem>}
                {activeTab === 'sales' && <SelectItem value="clientName">العميل</SelectItem>}
                {activeTab === 'sales' && <SelectItem value="total">المجموع</SelectItem>}
                {activeTab === 'clients' && <SelectItem value="name">الاسم</SelectItem>}
              </SelectContent>
            </Select>

            {/* Sort Direction */}
            <Select value={sortDirection} onValueChange={(value: any) => setSortDirection(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">تنازلي</SelectItem>
                <SelectItem value="asc">تصاعدي</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Data Display */}
      <Card>
        <CardHeader>
          <CardTitle>
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="w-5 h-5 animate-spin" />
                جاري التحميل...
              </div>
            ) : (
              `بيانات ${activeTab === 'devices' ? 'الأجهزة' : activeTab === 'sales' ? 'المبيعات' : 'العملاء'}`
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderDataTable()}
        </CardContent>
      </Card>
    </div>
  );
}
