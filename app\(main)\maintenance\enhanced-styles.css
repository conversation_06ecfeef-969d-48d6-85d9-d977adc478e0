/* ===== تحسينات صفحة الصيانة المتقدمة ===== */

/* الخلفية الرئيسية */
.maintenance-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding: 1rem;
}

/* ===== رأس الصفحة المحسن ===== */
.header-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.25rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.header-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f59e0b, #d97706, #92400e);
}

/* ===== تحسينات البطاقات الرئيسية ===== */
.enhanced-maintenance-card,
.enhanced-stocktake-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.25rem;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  color: #1f2937 !important;
}

.enhanced-maintenance-card::before,
.enhanced-stocktake-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--card-accent, #f59e0b), var(--card-accent-end, #d97706));
}

.enhanced-maintenance-card:hover,
.enhanced-stocktake-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.95);
}

/* ألوان مختلفة للبطاقات */
.card-new-stocktake { --card-accent: #f59e0b; --card-accent-end: #d97706; }
.card-inventory-list { --card-accent: #10b981; --card-accent-end: #059669; }
.card-results { --card-accent: #8b5cf6; --card-accent-end: #7c3aed; }

/* ===== أقسام البطاقات المختلفة ===== */
.overview-section {
  --card-accent: #f59e0b;
  --card-accent-end: #d97706;
}

.receive-section {
  --card-accent: #10b981;
  --card-accent-end: #059669;
}

.deliver-section {
  --card-accent: #3b82f6;
  --card-accent-end: #1d4ed8;
}

.browse-section {
  --card-accent: #8b5cf6;
  --card-accent-end: #7c3aed;
}

.orders-section {
  --card-accent: #06b6d4;
  --card-accent-end: #0891b2;
}

.history-section {
  --card-accent: #ef4444;
  --card-accent-end: #dc2626;
}

/* إصلاحات التباين والوضوح */
.enhanced-maintenance-card,
.enhanced-stocktake-card,
.overview-section,
.receive-section,
.deliver-section,
.browse-section,
.orders-section,
.history-section {
  color: #1f2937 !important;
}

.enhanced-maintenance-card h1,
.enhanced-maintenance-card h2,
.enhanced-maintenance-card h3,
.enhanced-maintenance-card h4,
.enhanced-maintenance-card h5,
.enhanced-maintenance-card h6,
.enhanced-stocktake-card h1,
.enhanced-stocktake-card h2,
.enhanced-stocktake-card h3,
.enhanced-stocktake-card h4,
.enhanced-stocktake-card h5,
.enhanced-stocktake-card h6 {
  color: #1f2937 !important;
}

.enhanced-maintenance-card p,
.enhanced-stocktake-card p,
.overview-section p,
.receive-section p,
.deliver-section p,
.browse-section p,
.orders-section p,
.history-section p {
  color: #4b5563 !important;
}

.enhanced-maintenance-card label,
.enhanced-stocktake-card label,
.overview-section label,
.receive-section label,
.deliver-section label,
.browse-section label,
.orders-section label,
.history-section label {
  color: #374151 !important;
  font-weight: 600;
}

/* ===== تحسينات الحقول والمدخلات ===== */
.enhanced-input {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.5);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  color: #1f2937 !important;
  font-weight: 500;
}

.enhanced-input:focus {
  background: rgba(255, 255, 255, 1);
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
  color: #1f2937 !important;
}

.enhanced-input:hover {
  border-color: #94a3b8;
  background: rgba(255, 255, 255, 0.95);
  color: #1f2937 !important;
}

.enhanced-input::placeholder {
  color: #6b7280 !important;
  font-weight: 400;
}

/* تحسين القوائم المنسدلة */
select,
.select-trigger {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(203, 213, 225, 0.5) !important;
  border-radius: 0.75rem !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
}

select:focus,
.select-trigger:focus {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  color: #1f2937 !important;
}

select option {
  color: #1f2937 !important;
  background: white !important;
}

/* تحسين منطقة النص */
textarea {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(203, 213, 225, 0.5) !important;
  border-radius: 0.75rem !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
}

textarea:focus {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  color: #1f2937 !important;
}

textarea::placeholder {
  color: #6b7280 !important;
  font-weight: 400;
}

/* ===== تحسينات الأزرار ===== */
.enhanced-button {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.enhanced-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.enhanced-button:active {
  transform: translateY(0);
}

/* ===== تحسينات الجداول ===== */
.enhanced-table {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.enhanced-table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-weight: 600;
}

.enhanced-table-row:hover {
  background: rgba(59, 130, 246, 0.05);
}

.enhanced-table-cell {
  border-color: rgba(226, 232, 240, 0.5);
  color: #374151;
}

/* ===== تحسينات الشارات ===== */
.enhanced-badge {
  backdrop-filter: blur(5px);
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.enhanced-badge:hover {
  transform: scale(1.05);
}

/* ===== تحسينات التبويبات ===== */
.enhanced-tabs {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.enhanced-tab-trigger {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  font-weight: 500;
}

.enhanced-tab-trigger:hover {
  background: rgba(59, 130, 246, 0.1);
}

.enhanced-tab-trigger[data-state="active"] {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* ===== الرسوم المتحركة ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

/* ===== تحسينات الأيقونات ===== */
.enhanced-icon {
  transition: all 0.3s ease;
}

.enhanced-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

/* ===== تحسينات منطقة التمرير ===== */
.enhanced-scroll-area {
  max-height: 400px;
  overflow-y: auto;
  border-radius: 0.75rem;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.enhanced-scroll-area::-webkit-scrollbar {
  width: 8px;
}

.enhanced-scroll-area::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 4px;
}

.enhanced-scroll-area::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
}

.enhanced-scroll-area::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.8);
}

/* ===== الوضع الليلي (Dark Mode) ===== */

/* متغيرات الألوان للوضع الليلي */
.dark-mode {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
  --accent-primary: #f59e0b;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
}

/* خلفية الصفحة في الوضع الليلي */
.dark-mode .maintenance-page {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: var(--text-primary);
}

/* البطاقات في الوضع الليلي */
.dark-mode .enhanced-maintenance-card,
.dark-mode .enhanced-stocktake-card,
.dark-mode .header-card,
.dark-mode .overview-section,
.dark-mode .receive-section,
.dark-mode .deliver-section,
.dark-mode .browse-section,
.dark-mode .orders-section,
.dark-mode .history-section {
  background: rgba(30, 41, 59, 0.9) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-maintenance-card::before,
.dark-mode .enhanced-stocktake-card::before,
.dark-mode .header-card::before,
.dark-mode .overview-section::before,
.dark-mode .receive-section::before,
.dark-mode .deliver-section::before,
.dark-mode .browse-section::before,
.dark-mode .orders-section::before,
.dark-mode .history-section::before {
  background: linear-gradient(90deg, var(--accent-primary), #d97706, #92400e);
}

/* الحقول والمدخلات في الوضع الليلي */
.dark-mode .enhanced-input {
  background: rgba(51, 65, 85, 0.8) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode .enhanced-input:focus {
  background: rgba(51, 65, 85, 1) !important;
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-input::placeholder {
  color: var(--text-muted) !important;
  font-weight: 400;
}

/* القوائم المنسدلة في الوضع الليلي */
.dark-mode select,
.dark-mode .select-trigger {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode select:focus,
.dark-mode .select-trigger:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode select option {
  background: rgba(30, 41, 59, 0.95) !important;
  color: var(--text-primary) !important;
}

/* منطقة النص في الوضع الليلي */
.dark-mode textarea {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode textarea:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode textarea::placeholder {
  color: var(--text-muted) !important;
  font-weight: 400;
}

/* الأزرار في الوضع الليلي */
.dark-mode .enhanced-button {
  background: rgba(51, 65, 85, 0.8) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button:hover {
  background: rgba(71, 85, 105, 0.9) !important;
  border-color: var(--accent-primary) !important;
}

/* الأزرار البيضاء في الوضع الليلي */
.dark-mode .enhanced-button.bg-white {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.6) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button.bg-white:hover {
  background: rgba(71, 85, 105, 1) !important;
  border-color: var(--accent-primary) !important;
  color: var(--text-primary) !important;
}

/* الجداول في الوضع الليلي */
.dark-mode .enhanced-table {
  background: rgba(30, 41, 59, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-table-header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
}

.dark-mode .enhanced-table-row:hover {
  background: rgba(245, 158, 11, 0.1) !important;
}

.dark-mode .enhanced-table-cell {
  border-color: rgba(71, 85, 105, 0.3) !important;
  color: var(--text-primary) !important;
}

/* الشارات في الوضع الليلي */
.dark-mode .enhanced-badge {
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

/* النصوص في الوضع الليلي */
.dark-mode .enhanced-maintenance-card h1,
.dark-mode .enhanced-maintenance-card h2,
.dark-mode .enhanced-maintenance-card h3,
.dark-mode .enhanced-maintenance-card h4,
.dark-mode .enhanced-maintenance-card h5,
.dark-mode .enhanced-maintenance-card h6,
.dark-mode .enhanced-stocktake-card h1,
.dark-mode .enhanced-stocktake-card h2,
.dark-mode .enhanced-stocktake-card h3,
.dark-mode .enhanced-stocktake-card h4,
.dark-mode .enhanced-stocktake-card h5,
.dark-mode .enhanced-stocktake-card h6 {
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-maintenance-card p,
.dark-mode .enhanced-maintenance-card span,
.dark-mode .enhanced-maintenance-card div,
.dark-mode .enhanced-stocktake-card p,
.dark-mode .enhanced-stocktake-card span,
.dark-mode .enhanced-stocktake-card div,
.dark-mode .overview-section p,
.dark-mode .receive-section p,
.dark-mode .deliver-section p,
.dark-mode .browse-section p,
.dark-mode .orders-section p,
.dark-mode .history-section p {
  color: var(--text-secondary) !important;
}

.dark-mode .enhanced-maintenance-card label,
.dark-mode .enhanced-stocktake-card label,
.dark-mode .overview-section label,
.dark-mode .receive-section label,
.dark-mode .deliver-section label,
.dark-mode .browse-section label,
.dark-mode .orders-section label,
.dark-mode .history-section label {
  color: var(--text-primary) !important;
  font-weight: 500;
}

/* التبويبات في الوضع الليلي */
.dark-mode .enhanced-tabs {
  background: rgba(30, 41, 59, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-tab-trigger {
  color: var(--text-secondary) !important;
}

.dark-mode .enhanced-tab-trigger:hover {
  background: rgba(245, 158, 11, 0.1) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-tab-trigger[data-state="active"] {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3) !important;
}

/* منطقة التمرير في الوضع الليلي */
.dark-mode .enhanced-scroll-area {
  border-color: rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.5) !important;
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.5) !important;
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.8) !important;
}
