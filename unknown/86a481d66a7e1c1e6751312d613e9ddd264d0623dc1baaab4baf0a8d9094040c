import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// دالة توليد رقم SO فريد
async function generateUniqueId(tx: any, table: string, prefix: string): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    attempts++;
    
    // الحصول على أعلى رقم موجود
    const lastRecord = await tx[table].findFirst({
      where: {
        soNumber: {
          startsWith: prefix
        }
      },
      orderBy: {
        soNumber: 'desc'
      },
      select: {
        soNumber: true
      }
    });

    let nextNumber = 1;
    if (lastRecord?.soNumber) {
      // استخراج الرقم من آخر سجل
      const lastNumber = parseInt(lastRecord.soNumber.replace(prefix, ''));
      if (!isNaN(lastNumber)) {
        nextNumber = lastNumber + 1;
      }
    }

    const newId = `${prefix}${nextNumber}`;

    // التحقق من عدم وجود الرقم
    const existing = await tx[table].findFirst({
      where: { soNumber: newId }
    });

    if (!existing) {
      return newId;
    }
  }

  throw new Error(`فشل في توليد رقم فريد بعد ${maxAttempts} محاولات`);
}

export async function POST(request: NextRequest) {
  try {
    const soNumber = await prisma.$transaction(async (tx) => {
      return await generateUniqueId(tx, 'sale', 'SO-');
    });

    return NextResponse.json({ soNumber });
  } catch (error) {
    console.error('خطأ في توليد رقم الأمر:', error);
    return NextResponse.json(
      { error: 'فشل في توليد رقم الأمر' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
