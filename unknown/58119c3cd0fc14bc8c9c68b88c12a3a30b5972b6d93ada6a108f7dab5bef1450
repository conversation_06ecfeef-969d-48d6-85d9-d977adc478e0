"use client";

import React from 'react';
import { Loader2, RefreshCw, Spinner } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'default' | 'lg' | 'xl';
  variant?: 'default' | 'dots' | 'pulse' | 'bounce' | 'refresh';
  className?: string;
  text?: string;
  textPosition?: 'bottom' | 'right' | 'left';
  color?: 'default' | 'primary' | 'secondary' | 'muted';
}

export function LoadingSpinner({
  size = 'default',
  variant = 'default',
  className = '',
  text,
  textPosition = 'bottom',
  color = 'default'
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    default: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  const colorClasses = {
    default: 'text-foreground',
    primary: 'text-primary',
    secondary: 'text-secondary',
    muted: 'text-muted-foreground'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    default: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  const renderSpinner = () => {
    const baseClasses = cn(sizeClasses[size], colorClasses[color]);

    switch (variant) {
      case 'refresh':
        return <RefreshCw className={cn(baseClasses, 'animate-spin')} />;
      
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  'rounded-full bg-current animate-pulse',
                  size === 'sm' ? 'h-1 w-1' : 
                  size === 'default' ? 'h-2 w-2' :
                  size === 'lg' ? 'h-3 w-3' : 'h-4 w-4',
                  colorClasses[color]
                )}
                style={{
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: '1s'
                }}
              />
            ))}
          </div>
        );
      
      case 'pulse':
        return (
          <div className={cn(baseClasses, 'rounded-full bg-current animate-pulse')} />
        );
      
      case 'bounce':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  'rounded-full bg-current animate-bounce',
                  size === 'sm' ? 'h-2 w-2' : 
                  size === 'default' ? 'h-3 w-3' :
                  size === 'lg' ? 'h-4 w-4' : 'h-6 w-6',
                  colorClasses[color]
                )}
                style={{
                  animationDelay: `${i * 0.1}s`
                }}
              />
            ))}
          </div>
        );
      
      default:
        return <Loader2 className={cn(baseClasses, 'animate-spin')} />;
    }
  };

  const content = (
    <>
      {renderSpinner()}
      {text && (
        <span className={cn(textSizeClasses[size], colorClasses[color])}>
          {text}
        </span>
      )}
    </>
  );

  const containerClasses = cn(
    'flex items-center',
    textPosition === 'bottom' && 'flex-col space-y-2',
    textPosition === 'right' && 'flex-row space-x-2',
    textPosition === 'left' && 'flex-row-reverse space-x-reverse space-x-2',
    className
  );

  return <div className={containerClasses}>{content}</div>;
}

// Full page loading overlay
interface LoadingOverlayProps {
  isVisible: boolean;
  text?: string;
  variant?: LoadingSpinnerProps['variant'];
  backdrop?: boolean;
  className?: string;
}

export function LoadingOverlay({
  isVisible,
  text = "جاري التحميل...",
  variant = 'default',
  backdrop = true,
  className = ''
}: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div className={cn(
      'fixed inset-0 z-50 flex items-center justify-center',
      backdrop && 'bg-background/80 backdrop-blur-sm',
      className
    )}>
      <div className="flex flex-col items-center space-y-4 p-6 bg-background border rounded-lg shadow-lg">
        <LoadingSpinner size="lg" variant={variant} />
        <p className="text-muted-foreground">{text}</p>
      </div>
    </div>
  );
}

// Inline loading state for components
interface InlineLoadingProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  variant?: LoadingSpinnerProps['variant'];
  size?: LoadingSpinnerProps['size'];
  className?: string;
}

export function InlineLoading({
  isLoading,
  children,
  loadingText = "جاري التحميل...",
  variant = 'default',
  size = 'default',
  className = ''
}: InlineLoadingProps) {
  if (isLoading) {
    return (
      <div className={cn('flex items-center justify-center p-4', className)}>
        <LoadingSpinner 
          size={size} 
          variant={variant} 
          text={loadingText}
          textPosition="right"
        />
      </div>
    );
  }

  return <>{children}</>;
}

// Button with loading state
interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  loadingText?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children: React.ReactNode;
}

export function LoadingButton({
  isLoading = false,
  loadingText,
  variant = 'default',
  size = 'default',
  children,
  disabled,
  className = '',
  ...props
}: LoadingButtonProps) {
  const baseClasses = cn(
    'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50',
    
    // Variants
    variant === 'default' && 'bg-primary text-primary-foreground hover:bg-primary/90',
    variant === 'destructive' && 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
    variant === 'outline' && 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    variant === 'secondary' && 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    variant === 'ghost' && 'hover:bg-accent hover:text-accent-foreground',
    variant === 'link' && 'text-primary underline-offset-4 hover:underline',
    
    // Sizes
    size === 'default' && 'h-10 px-4 py-2',
    size === 'sm' && 'h-9 rounded-md px-3',
    size === 'lg' && 'h-11 rounded-md px-8',
    size === 'icon' && 'h-10 w-10',
    
    className
  );

  return (
    <button
      className={baseClasses}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading && (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      )}
      {isLoading && loadingText ? loadingText : children}
    </button>
  );
}

// Skeleton loader for content
interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export function Skeleton({
  className = '',
  variant = 'rectangular',
  width,
  height,
  lines = 1
}: SkeletonProps) {
  const baseClasses = 'animate-pulse bg-muted rounded';
  
  const variantClasses = {
    text: 'h-4',
    circular: 'rounded-full',
    rectangular: 'rounded'
  };

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, i) => (
          <div
            key={i}
            className={cn(baseClasses, variantClasses[variant], className)}
            style={{
              ...style,
              width: i === lines - 1 ? '75%' : '100%'
            }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(baseClasses, variantClasses[variant], className)}
      style={style}
    />
  );
}
