"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { 
  Filter, 
  X, 
  Calendar as CalendarIcon, 
  ChevronDown, 
  ChevronUp,
  RotateCcw,
  Search
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

export interface FilterConfig {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'text' | 'number' | 'date' | 'daterange' | 'boolean' | 'range';
  options?: FilterOption[];
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
  defaultValue?: any;
}

export interface FilterValues {
  [key: string]: any;
}

interface AdvancedFiltersProps {
  filters: FilterConfig[];
  values: FilterValues;
  onChange: (values: FilterValues) => void;
  onReset?: () => void;
  className?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  showActiveCount?: boolean;
  title?: string;
  description?: string;
}

export function AdvancedFilters({
  filters,
  values,
  onChange,
  onReset,
  className = '',
  collapsible = true,
  defaultExpanded = false,
  showActiveCount = true,
  title = "فلاتر متقدمة",
  description = "استخدم الفلاتر لتضييق نطاق البحث"
}: AdvancedFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [localValues, setLocalValues] = useState<FilterValues>(values || {});

  // Update local values when external values change
  useEffect(() => {
    setLocalValues(values || {});
  }, [values]);

  // Count active filters
  const activeFiltersCount = Object.keys(values || {}).filter(key => {
    const value = values?.[key];
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'string') return value.trim() !== '';
    if (typeof value === 'number') return value !== 0;
    if (typeof value === 'boolean') return value;
    return value != null;
  }).length;

  const handleValueChange = (key: string, value: any) => {
    const newValues = { ...(localValues || {}), [key]: value };
    setLocalValues(newValues);
    onChange(newValues);
  };

  const handleReset = () => {
    const resetValues: FilterValues = {};
    filters.forEach(filter => {
      if (filter.defaultValue !== undefined) {
        resetValues[filter.key] = filter.defaultValue;
      }
    });
    setLocalValues(resetValues);
    onChange(resetValues);
    onReset?.();
  };

  const renderFilter = (filter: FilterConfig) => {
    const value = localValues?.[filter.key];

    switch (filter.type) {
      case 'select':
        return (
          <div key={filter.key} className="space-y-2">
            <Label htmlFor={filter.key}>{filter.label}</Label>
            <Select
              value={value || ''}
              onValueChange={(newValue) => handleValueChange(filter.key, newValue)}
            >
              <SelectTrigger>
                <SelectValue placeholder={filter.placeholder || `اختر ${filter.label}`} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">الكل</SelectItem>
                {filter.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex justify-between items-center w-full">
                      <span>{option.label}</span>
                      {option.count !== undefined && (
                        <Badge variant="secondary" className="ml-2">
                          {option.count}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'multiselect':
        const selectedValues = Array.isArray(value) ? value : [];
        return (
          <div key={filter.key} className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {filter.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${filter.key}-${option.value}`}
                    checked={selectedValues.includes(option.value)}
                    onCheckedChange={(checked) => {
                      const newValues = checked
                        ? [...selectedValues, option.value]
                        : selectedValues.filter(v => v !== option.value);
                      handleValueChange(filter.key, newValues);
                    }}
                  />
                  <Label
                    htmlFor={`${filter.key}-${option.value}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    <div className="flex justify-between items-center">
                      <span>{option.label}</span>
                      {option.count !== undefined && (
                        <Badge variant="outline" className="ml-2">
                          {option.count}
                        </Badge>
                      )}
                    </div>
                  </Label>
                </div>
              ))}
            </div>
          </div>
        );

      case 'text':
        return (
          <div key={filter.key} className="space-y-2">
            <Label htmlFor={filter.key}>{filter.label}</Label>
            <Input
              id={filter.key}
              type="text"
              placeholder={filter.placeholder || `أدخل ${filter.label}`}
              value={value || ''}
              onChange={(e) => handleValueChange(filter.key, e.target.value)}
            />
          </div>
        );

      case 'number':
        return (
          <div key={filter.key} className="space-y-2">
            <Label htmlFor={filter.key}>{filter.label}</Label>
            <Input
              id={filter.key}
              type="number"
              placeholder={filter.placeholder || `أدخل ${filter.label}`}
              value={value || ''}
              min={filter.min}
              max={filter.max}
              step={filter.step}
              onChange={(e) => handleValueChange(filter.key, Number(e.target.value) || 0)}
            />
          </div>
        );

      case 'range':
        const rangeValue = Array.isArray(value) ? value : [filter.min || 0, filter.max || 100];
        return (
          <div key={filter.key} className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="px-2">
              <Slider
                value={rangeValue}
                onValueChange={(newValue) => handleValueChange(filter.key, newValue)}
                min={filter.min || 0}
                max={filter.max || 100}
                step={filter.step || 1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>{rangeValue[0]}</span>
                <span>{rangeValue[1]}</span>
              </div>
            </div>
          </div>
        );

      case 'date':
        return (
          <div key={filter.key} className="space-y-2">
            <Label>{filter.label}</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !value && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {value ? format(new Date(value), "PPP", { locale: ar }) : filter.placeholder || "اختر التاريخ"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={value ? new Date(value) : undefined}
                  onSelect={(date) => handleValueChange(filter.key, date?.toISOString())}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        );

      case 'daterange':
        const dateRange = value || {};
        return (
          <div key={filter.key} className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="grid grid-cols-2 gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "justify-start text-left font-normal",
                      !dateRange.from && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? format(new Date(dateRange.from), "PP", { locale: ar }) : "من"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateRange.from ? new Date(dateRange.from) : undefined}
                    onSelect={(date) => handleValueChange(filter.key, { ...dateRange, from: date?.toISOString() })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "justify-start text-left font-normal",
                      !dateRange.to && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.to ? format(new Date(dateRange.to), "PP", { locale: ar }) : "إلى"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateRange.to ? new Date(dateRange.to) : undefined}
                    onSelect={(date) => handleValueChange(filter.key, { ...dateRange, to: date?.toISOString() })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        );

      case 'boolean':
        return (
          <div key={filter.key} className="flex items-center space-x-2">
            <Checkbox
              id={filter.key}
              checked={Boolean(value)}
              onCheckedChange={(checked) => handleValueChange(filter.key, checked)}
            />
            <Label htmlFor={filter.key} className="cursor-pointer">
              {filter.label}
            </Label>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            <div>
              <CardTitle className="text-lg">{title}</CardTitle>
              {description && <CardDescription className="text-sm">{description}</CardDescription>}
            </div>
            {showActiveCount && activeFiltersCount > 0 && (
              <Badge variant="secondary">
                {activeFiltersCount} فلتر نشط
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Button variant="ghost" size="sm" onClick={handleReset}>
                <RotateCcw className="h-4 w-4 ml-1" />
                إعادة تعيين
              </Button>
            )}
            
            {collapsible && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      {(!collapsible || isExpanded) && (
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filters.map(renderFilter)}
          </div>
          
          {/* Active Filters Display */}
          {activeFiltersCount > 0 && (
            <>
              <Separator className="my-4" />
              <div className="space-y-2">
                <Label className="text-sm font-medium">الفلاتر النشطة:</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(values || {}).map(([key, value]) => {
                    if (!value || (Array.isArray(value) && value.length === 0)) return null;

                    const filter = filters.find(f => f.key === key);
                    if (!filter) return null;
                    
                    const displayValue = Array.isArray(value) 
                      ? `${value.length} عنصر`
                      : typeof value === 'object' && value.from && value.to
                      ? `${format(new Date(value.from), "PP", { locale: ar })} - ${format(new Date(value.to), "PP", { locale: ar })}`
                      : String(value);
                    
                    return (
                      <Badge key={key} variant="secondary" className="gap-1">
                        {filter.label}: {displayValue}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 hover:bg-transparent"
                          onClick={() => handleValueChange(key, filter.type === 'multiselect' ? [] : '')}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    );
                  })}
                </div>
              </div>
            </>
          )}
        </CardContent>
      )}
    </Card>
  );
}
