import { jsPDF } from "jspdf";
import "jspdf-autotable";

// تهيئة jsPDF مع دعم اللغة العربية
export function createArabicPDF() {
  // إنشاء مستند PDF
  const doc = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
  });

  // استخدام الخط الافتراضي بدلاً من محاولة استيراد خط خارجي
  // هذا سيعمل للنصوص الإنجليزية، وسنضيف دعم للعربية بطريقة أخرى
  // تفعيل اتجاه من اليمين إلى اليسار
  doc.setR2L(true);
  
  return doc;
}

// تصدير بيانات الجدول إلى PDF
export function exportTableToPDF(
  tableData: any[], 
  headers: string[], 
  fileName: string, 
  title: string = "تقرير"
) {
  const doc = createArabicPDF();
  
  // إضافة العنوان
  doc.setFontSize(18);
  doc.text(title, doc.internal.pageSize.width / 2, 20, { align: "center" });
  
  // إعداد بيانات الجدول
  const tableHeaders = headers;
  const tableRows = tableData.map(row => {
    // تأكد من أن البيانات نصية
    return Object.values(row).map(cell => String(cell));
  });

  // إضافة الجدول مع تفعيل الاتجاه من اليمين إلى اليسار
  (doc as any).autoTable({
    head: [tableHeaders],
    body: tableRows,
    startY: 30,
    styles: {
      halign: "right", // محاذاة النص إلى اليمين
      rtl: true, // من اليمين إلى اليسار
    },
    headStyles: {
      fillColor: [66, 66, 66],
      textColor: 255,
      fontStyle: "bold",
    },
    alternateRowStyles: {
      fillColor: [245, 245, 245],
    },
  });
  
  // حفظ الملف
  doc.save(`${fileName}.pdf`);
}

// تصدير بيانات إلى CSV مع دعم اللغة العربية
export function exportToCSV(data: any[], headers: string[], fileName: string) {
  // إضافة BOM (Byte Order Mark) لدعم الأحرف العربية في Excel
  const BOM = "\uFEFF";
  
  // إنشاء سطر الرؤوس
  const headerRow = headers.join(",");
  
  // تحويل البيانات إلى أسطر CSV
  const rows = data.map(row => 
    Object.values(row)
      .map(value => `"${String(value).replace(/"/g, '""')}"`)
      .join(",")
  );
  
  // دمج الرؤوس والبيانات
  let csvContent = BOM + headerRow + "\n" + rows.join("\n");
  
  // إنشاء ملف للتنزيل
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" });
  const url = URL.createObjectURL(blob);
  
  // إنشاء رابط تنزيل
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", `${fileName}.csv`);
  link.style.display = "none";
  document.body.appendChild(link);
  link.click();
  
  // تنظيف
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

// تجهيز صفحة للطباعة مع دعم اللغة العربية
export function printReport(contentId: string, title: string = "تقرير") {
  const content = document.getElementById(contentId);
  if (!content) return;
  
  const printWindow = window.open("", "_blank");
  if (!printWindow) return;
  
  printWindow.document.write(`
    <!DOCTYPE html>
    <html dir="rtl">
      <head>
        <meta charset="utf-8" />
        <title>${title}</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Amiri&display=swap');
          
          body {
            font-family: 'Amiri', Arial, sans-serif;
            direction: rtl;
            padding: 20px;
          }
          
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          
          table, th, td {
            border: 1px solid #ddd;
          }
          
          th, td {
            padding: 12px;
            text-align: right;
          }
          
          th {
            background-color: #f2f2f2;
          }
          
          @media print {
            body {
              print-color-adjust: exact;
              -webkit-print-color-adjust: exact;
            }
          }
        </style>
      </head>
      <body>
        <h1 style="text-align: center;">${title}</h1>
        ${content.innerHTML}
        <script>
          window.onload = function() { 
            setTimeout(function() {
              window.print();
              window.close();
            }, 500);
          };
        </script>
      </body>
    </html>
  `);
  
  printWindow.document.close();
}
