#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkMaterialsTables() {
  try {
    console.log('🔍 فحص جداول إدارة المواد...');

    // 1. فحص جدول الشركات المصنعة
    console.log('\n📋 فحص جدول الشركات المصنعة (Manufacturer)...');
    
    try {
      const manufacturers = await prisma.manufacturer.findMany({
        orderBy: { id: 'asc' }
      });
      
      console.log(`✅ تم العثور على ${manufacturers.length} شركة مصنعة في قاعدة البيانات`);
      
      if (manufacturers.length > 0) {
        console.log('\n📋 الشركات المصنعة:');
        manufacturers.forEach(manufacturer => {
          console.log(`- ID: ${manufacturer.id}, الاسم: "${manufacturer.name}", تاريخ الإنشاء: ${manufacturer.createdAt.toISOString().split('T')[0]}`);
        });
      }
    } catch (error) {
      console.error('❌ خطأ في الوصول إلى جدول الشركات المصنعة:', error);
    }

    // 2. فحص جدول موديلات الأجهزة
    console.log('\n📱 فحص جدول موديلات الأجهزة (DeviceModel)...');
    
    try {
      const deviceModels = await prisma.deviceModel.findMany({
        include: {
          manufacturer: true
        },
        orderBy: { id: 'asc' }
      });
      
      console.log(`✅ تم العثور على ${deviceModels.length} موديل في قاعدة البيانات`);
      
      if (deviceModels.length > 0) {
        console.log('\n📱 موديلات الأجهزة:');
        deviceModels.forEach(model => {
          console.log(`- ID: ${model.id}, الاسم: "${model.name}", الشركة: "${model.manufacturer.name}", الفئة: "${model.category}"`);
        });
      }
    } catch (error) {
      console.error('❌ خطأ في الوصول إلى جدول موديلات الأجهزة:', error);
    }

    // 3. فحص العلاقات بين الجداول
    console.log('\n🔗 فحص العلاقات بين الجداول...');
    
    try {
      const manufacturersWithModels = await prisma.manufacturer.findMany({
        include: {
          deviceModels: true
        }
      });

      console.log('\n📊 إحصائيات الموديلات لكل شركة:');
      manufacturersWithModels.forEach(manufacturer => {
        console.log(`- ${manufacturer.name}: ${manufacturer.deviceModels.length} موديل`);
      });

      // فحص الشركات بدون موديلات
      const manufacturersWithoutModels = manufacturersWithModels.filter(m => m.deviceModels.length === 0);
      if (manufacturersWithoutModels.length > 0) {
        console.log('\n⚠️ شركات بدون موديلات:');
        manufacturersWithoutModels.forEach(m => {
          console.log(`- ${m.name} (ID: ${m.id})`);
        });
      }

    } catch (error) {
      console.error('❌ خطأ في فحص العلاقات:', error);
    }

    // 4. فحص تكامل البيانات
    console.log('\n🔍 فحص تكامل البيانات...');
    
    try {
      // فحص الموديلات المكررة
      const duplicateModels = await prisma.deviceModel.groupBy({
        by: ['name', 'manufacturerId'],
        _count: {
          id: true
        },
        having: {
          id: {
            _count: {
              gt: 1
            }
          }
        }
      });

      if (duplicateModels.length > 0) {
        console.log('⚠️ موديلات مكررة:');
        duplicateModels.forEach(duplicate => {
          console.log(`- "${duplicate.name}" للشركة ID ${duplicate.manufacturerId}: ${duplicate._count.id} نسخة`);
        });
      } else {
        console.log('✅ لا توجد موديلات مكررة');
      }

      // فحص الشركات المكررة
      const duplicateManufacturers = await prisma.manufacturer.groupBy({
        by: ['name'],
        _count: {
          id: true
        },
        having: {
          id: {
            _count: {
              gt: 1
            }
          }
        }
      });

      if (duplicateManufacturers.length > 0) {
        console.log('⚠️ شركات مكررة:');
        duplicateManufacturers.forEach(duplicate => {
          console.log(`- "${duplicate.name}": ${duplicate._count.id} نسخة`);
        });
      } else {
        console.log('✅ لا توجد شركات مكررة');
      }

    } catch (error) {
      console.error('❌ خطأ في فحص تكامل البيانات:', error);
    }

    // 5. اختبار إنشاء بيانات جديدة
    console.log('\n🧪 اختبار إنشاء بيانات جديدة...');
    
    try {
      const testManufacturerName = `شركة اختبار قاعدة البيانات ${Date.now()}`;
      
      // إنشاء شركة اختبار
      const testManufacturer = await prisma.manufacturer.create({
        data: {
          name: testManufacturerName
        }
      });
      
      console.log(`✅ تم إنشاء شركة اختبار: ${testManufacturer.name} (ID: ${testManufacturer.id})`);
      
      // إنشاء موديل اختبار
      const testModel = await prisma.deviceModel.create({
        data: {
          name: `موديل اختبار ${Date.now()}`,
          manufacturerId: testManufacturer.id,
          category: 'هاتف ذكي'
        }
      });
      
      console.log(`✅ تم إنشاء موديل اختبار: ${testModel.name} (ID: ${testModel.id})`);
      
      // حذف البيانات الاختبارية
      await prisma.deviceModel.delete({
        where: { id: testModel.id }
      });
      
      await prisma.manufacturer.delete({
        where: { id: testManufacturer.id }
      });
      
      console.log('✅ تم حذف البيانات الاختبارية بنجاح');
      
    } catch (error) {
      console.error('❌ خطأ في اختبار إنشاء البيانات:', error);
    }

    // 6. فحص الفهارس والقيود
    console.log('\n📊 فحص الفهارس والقيود...');
    
    try {
      // فحص القيود الفريدة
      const uniqueConstraintTest = await prisma.manufacturer.findFirst({
        where: { name: 'Apple' }
      });
      
      if (uniqueConstraintTest) {
        console.log('✅ القيود الفريدة تعمل بشكل صحيح');
      }
      
      // فحص العلاقات الخارجية
      const foreignKeyTest = await prisma.deviceModel.findFirst({
        include: { manufacturer: true }
      });
      
      if (foreignKeyTest && foreignKeyTest.manufacturer) {
        console.log('✅ العلاقات الخارجية تعمل بشكل صحيح');
      }
      
    } catch (error) {
      console.error('❌ خطأ في فحص الفهارس والقيود:', error);
    }

    console.log('\n✅ تم الانتهاء من فحص جداول إدارة المواد بنجاح!');
    console.log('\n📋 ملخص:');
    console.log('- جدول الشركات المصنعة (Manufacturer): ✅ موجود ويعمل');
    console.log('- جدول موديلات الأجهزة (DeviceModel): ✅ موجود ويعمل');
    console.log('- العلاقات بين الجداول: ✅ تعمل بشكل صحيح');
    console.log('- تكامل البيانات: ✅ سليم');

  } catch (error) {
    console.error('❌ خطأ عام في فحص جداول المواد:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMaterialsTables();
