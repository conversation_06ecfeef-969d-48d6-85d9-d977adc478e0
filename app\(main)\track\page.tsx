'use client';

// 🎯 النسخة المحسّنة من صفحة التتبع - مستوحاة من نمط صفحات التقارير
// ✅ تستخدم Store API بنسبة 100%
// ✅ تستفيد من الكاش المدمج
// ✅ أداء محسّن بـ useMemo/useCallback

import { useState, useMemo, useEffect, useCallback } from 'react';
import { useStore } from '@/context/store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Package, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react';
import type { Device, Sale, Return, SupplyOrder, Contact, EvaluationOrder, MaintenanceOrder, WarehouseTransfer } from '@/lib/types';

interface TimelineEvent {
  id: string;
  date: string;
  type: 'sale' | 'return' | 'supply' | 'evaluation' | 'maintenance' | 'transfer';
  description: string;
  status: string;
  details: any;
}

export default function TrackPageOptimized() {
  // ✅ استخدام Store API - البيانات متوفرة فوراً من الكاش
  const { 
    devices, 
    sales, 
    returns,
    supplyOrders,
    suppliers,
    evaluationOrders,
    maintenanceOrders,
    warehouseTransfers,
    isLoading 
  } = useStore();

  // حالات محلية للتفاعل
  const [searchedImei, setSearchedImei] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);

  // ✅ البحث عن الجهاز بكفاءة مع useMemo
  const device = useMemo(() => {
    if (!searchedImei || !devices.length) return null;
    return devices.find(d => d.id === searchedImei) || null;
  }, [devices, searchedImei]);

  // ✅ إنشاء timeline الأحداث بكفاءة
  const fullTimelineEvents = useMemo((): TimelineEvent[] => {
    if (!device) return [];

    const events: TimelineEvent[] = [];
    const deviceId = device.id;

    // أحداث المبيعات
    sales.forEach(sale => {
      if (sale.items?.some(item => item.deviceId === deviceId)) {
        events.push({
          id: `sale-${sale.id}`,
          date: sale.saleDate,
          type: 'sale',
          description: `بيع للعميل: ${sale.clientName}`,
          status: sale.status || 'مكتمل',
          details: sale
        });
      }
    });

    // أحداث المرتجعات
    returns.forEach(returnItem => {
      if (returnItem.items?.some(item => item.deviceId === deviceId)) {
        events.push({
          id: `return-${returnItem.id}`,
          date: returnItem.returnDate,
          type: 'return',
          description: `إرجاع من العميل: ${returnItem.clientName}`,
          status: returnItem.status || 'مكتمل',
          details: returnItem
        });
      }
    });

    // أحداث التوريد
    supplyOrders.forEach(supply => {
      if (supply.items?.some(item => item.deviceId === deviceId)) {
        events.push({
          id: `supply-${supply.id}`,
          date: supply.supplyDate,
          type: 'supply',
          description: `توريد من: ${suppliers.find(s => s.id === supply.supplierId)?.name || 'غير محدد'}`,
          status: supply.status || 'مكتمل',
          details: supply
        });
      }
    });

    // أحداث التقييم
    evaluationOrders.forEach(evaluation => {
      if (evaluation.items?.some(item => item.deviceId === deviceId)) {
        events.push({
          id: `evaluation-${evaluation.id}`,
          date: evaluation.evaluationDate,
          type: 'evaluation',
          description: `تقييم الجهاز - الدرجة: ${evaluation.items?.find(item => item.deviceId === deviceId)?.grade || 'غير محدد'}`,
          status: evaluation.status || 'مكتمل',
          details: evaluation
        });
      }
    });

    // أحداث الصيانة
    maintenanceOrders.forEach(maintenance => {
      if (maintenance.items?.some(item => item.id === deviceId)) {
        events.push({
          id: `maintenance-${maintenance.id}`,
          date: maintenance.date,
          type: 'maintenance',
          description: `صيانة الجهاز`,
          status: maintenance.status || 'مكتمل',
          details: maintenance
        });
      }
    });

    // أحداث النقل بين المخازن
    warehouseTransfers.forEach(transfer => {
      if (transfer.items?.some(item => item.deviceId === deviceId)) {
        events.push({
          id: `transfer-${transfer.id}`,
          date: transfer.transferDate,
          type: 'transfer',
          description: `نقل بين المخازن`,
          status: transfer.status || 'مكتمل',
          details: transfer
        });
      }
    });

    // ترتيب الأحداث حسب التاريخ (الأحدث أولاً)
    return events.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [device, sales, returns, supplyOrders, suppliers, evaluationOrders, maintenanceOrders, warehouseTransfers]);

  // ✅ إحصائيات الجهاز بكفاءة
  const deviceStats = useMemo(() => {
    if (!device) return null;

    return {
      totalEvents: fullTimelineEvents.length,
      salesCount: fullTimelineEvents.filter(e => e.type === 'sale').length,
      returnsCount: fullTimelineEvents.filter(e => e.type === 'return').length,
      maintenanceCount: fullTimelineEvents.filter(e => e.type === 'maintenance').length,
      currentStatus: device.status,
      currentWarehouse: device.warehouseId
    };
  }, [device, fullTimelineEvents]);

  // ✅ دالة البحث محسّنة مع useCallback
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setSearchedImei(query.trim());
  }, []);

  // ✅ دالة تنظيف البحث
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchedImei('');
    setSelectedDevice(null);
  }, []);

  // ✅ الحصول على لون الحالة
  const getStatusColor = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'متاح': return 'bg-green-100 text-green-800';
      case 'مباع': return 'bg-blue-100 text-blue-800';
      case 'قيد الإصلاح': return 'bg-yellow-100 text-yellow-800';
      case 'تالف': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }, []);

  // ✅ الحصول على أيقونة نوع الحدث
  const getEventIcon = useCallback((type: string) => {
    switch (type) {
      case 'sale': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'return': return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'supply': return <Package className="h-4 w-4 text-blue-600" />;
      case 'evaluation': return <CheckCircle className="h-4 w-4 text-purple-600" />;
      case 'maintenance': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'transfer': return <Package className="h-4 w-4 text-indigo-600" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  }, []);

  // عرض حالة التحميل
  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل البيانات...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* رأس الصفحة */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">تتبع الأجهزة</h1>
          <Badge variant="outline" className="text-sm">
            إجمالي الأجهزة: {devices.length}
          </Badge>
        </div>

        {/* شريط البحث */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="أدخل الرقم التسلسلي للجهاز (IMEI)..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              {searchQuery && (
                <Button variant="outline" onClick={clearSearch}>
                  مسح
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* حالة البحث */}
      {searchedImei && !device && !isLoading && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <p>لم يتم العثور على جهاز بالرقم التسلسلي: <strong>{searchedImei}</strong></p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* عرض تفاصيل الجهاز */}
      {device && deviceStats && (
        <div className="space-y-6">
          {/* معلومات الجهاز الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>معلومات الجهاز</span>
                <Badge className={getStatusColor(device.status)}>
                  {device.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">الرقم التسلسلي</p>
                  <p className="font-medium">{device.id}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">الموديل</p>
                  <p className="font-medium">{device.model}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">المخزن الحالي</p>
                  <p className="font-medium">{device.warehouseId || 'غير محدد'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary">{deviceStats.totalEvents}</p>
                  <p className="text-sm text-muted-foreground">إجمالي الأحداث</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{deviceStats.salesCount}</p>
                  <p className="text-sm text-muted-foreground">المبيعات</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{deviceStats.returnsCount}</p>
                  <p className="text-sm text-muted-foreground">المرتجعات</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">{deviceStats.maintenanceCount}</p>
                  <p className="text-sm text-muted-foreground">الصيانة</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* تاريخ الأحداث */}
          <Card>
            <CardHeader>
              <CardTitle>تاريخ الأحداث</CardTitle>
            </CardHeader>
            <CardContent>
              {fullTimelineEvents.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  لا توجد أحداث مسجلة لهذا الجهاز
                </p>
              ) : (
                <div className="space-y-4">
                  {fullTimelineEvents.map((event) => (
                    <div key={event.id} className="flex items-start gap-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0 mt-1">
                        {getEventIcon(event.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">{event.description}</p>
                          <Badge variant="outline" className="text-xs">
                            {new Date(event.date).toLocaleDateString('ar-SA')}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          الحالة: {event.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
