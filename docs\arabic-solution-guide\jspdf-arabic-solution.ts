// الطريقة الثالثة: jsPDF مع إعدادات عربية محسنة
// ملف: lib/export-utils/jspdf-arabic.ts

import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

// 1. إعداد الخطوط العربية لـ jsPDF
export function setupArabicFont(doc: jsPDF) {
  // تعيين اتجاه النص من اليمين لليسار
  doc.setR2L(true);
  
  // تعيين اللغة
  doc.setLanguage('ar');
  
  // إعدادات الخط الافتراضية
  doc.setFont('helvetica');
  doc.setFontSize(12);
}

// 2. وظيفة لكتابة النص العربي بشكل صحيح
export function writeArabicText(
  doc: jsPDF, 
  text: string, 
  x: number, 
  y: number, 
  options: any = {}
) {
  const {
    fontSize = 12,
    fontStyle = 'normal',
    align = 'right',
    maxWidth = 180
  } = options;
  
  doc.setFontSize(fontSize);
  doc.setFont('helvetica', fontStyle);
  
  // تقسيم النص الطويل إلى أسطر
  const lines = doc.splitTextToSize(text, maxWidth);
  
  if (Array.isArray(lines)) {
    lines.forEach((line: string, index: number) => {
      doc.text(line, x, y + (index * (fontSize * 0.5)), { align });
    });
    return y + (lines.length * (fontSize * 0.5));
  } else {
    doc.text(lines, x, y, { align });
    return y + (fontSize * 0.5);
  }
}

// 3. إنشاء رأس وتذييل عربي
export function addArabicHeaderFooter(doc: jsPDF, title: string) {
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  
  // رأس الصفحة
  const addHeader = () => {
    // خط فاصل علوي
    doc.setLineWidth(0.5);
    doc.setDrawColor(52, 73, 94);
    doc.line(15, 25, pageWidth - 15, 25);
    
    // العنوان
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text(title, pageWidth / 2, 20, { align: 'center' });
    
    // التاريخ
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const currentDate = new Date().toLocaleDateString('ar-EG');
    doc.text(`التاريخ: ${currentDate}`, pageWidth - 15, 35, { align: 'right' });
  };
  
  // تذييل الصفحة
  const addFooter = () => {
    // خط فاصل سفلي
    doc.setLineWidth(0.5);
    doc.setDrawColor(52, 73, 94);
    doc.line(15, pageHeight - 25, pageWidth - 15, pageHeight - 25);
    
    // رقم الصفحة
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const pageNumber = doc.getCurrentPageInfo().pageNumber;
    doc.text(`صفحة ${pageNumber}`, pageWidth / 2, pageHeight - 15, { align: 'center' });
    
    // معلومات إضافية
    doc.text('تم الإنشاء بواسطة النظام', 15, pageHeight - 15, { align: 'left' });
  };
  
  return { addHeader, addFooter };
}

// 4. إنشاء جدول عربي محسن
export function createArabicTable(
  doc: jsPDF,
  headers: string[],
  data: any[][],
  startY: number = 60
) {
  autoTable(doc, {
    startY: startY,
    head: [headers],
    body: data,
    
    // إعدادات عامة
    theme: 'grid',
    tableWidth: 'auto',
    margin: { top: 20, right: 15, bottom: 20, left: 15 },
    
    // إعدادات الخطوط
    styles: {
      font: 'helvetica',
      fontSize: 10,
      cellPadding: 5,
      halign: 'right', // محاذاة النص لليمين
      valign: 'middle',
      textColor: [33, 37, 41],
      lineColor: [222, 226, 230],
      lineWidth: 0.1
    },
    
    // إعدادات رأس الجدول
    headStyles: {
      fillColor: [248, 249, 250],
      textColor: [33, 37, 41],
      fontStyle: 'bold',
      halign: 'center'
    },
    
    // إعدادات صفوف البيانات
    bodyStyles: {
      fillColor: [255, 255, 255]
    },
    
    // صفوف متناوبة
    alternateRowStyles: {
      fillColor: [248, 249, 250]
    },
    
    // معالجة النصوص الطويلة
    columnStyles: {
      0: { cellWidth: 20 },  // عمود الرقم
      1: { cellWidth: 'auto' }, // عمود الاسم
      2: { cellWidth: 25 },  // عمود الكمية
      3: { cellWidth: 30 }   // عمود السعر
    },
    
    // إعدادات إضافية للعربية
    didParseCell: (data) => {
      // تأكد من أن النص محاذي لليمين
      data.cell.styles.halign = 'right';
      
      // معالجة خاصة لرأس الجدول
      if (data.section === 'head') {
        data.cell.styles.halign = 'center';
        data.cell.styles.fontStyle = 'bold';
      }
    }
  });
  
  // إرجاع الموضع Y الجديد
  return (doc as any).lastAutoTable.finalY + 10;
}

// 5. وظيفة شاملة لإنشاء PDF عربي
export function createArabicPDF(
  data: any,
  fileName: string,
  title: string
): void {
  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });
  
  // إعداد الخطوط العربية
  setupArabicFont(doc);
  
  // إضافة رأس وتذييل
  const { addHeader, addFooter } = addArabicHeaderFooter(doc, title);
  addHeader();
  
  let currentY = 50;
  
  // إضافة معلومات أساسية
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('المعلومات الأساسية', doc.internal.pageSize.width - 15, currentY, { align: 'right' });
  currentY += 10;
  
  // رسم مربع المعلومات
  doc.setDrawColor(200, 200, 200);
  doc.setLineWidth(0.5);
  doc.rect(15, currentY, doc.internal.pageSize.width - 30, 40);
  
  // إضافة المعلومات
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  
  currentY = writeArabicText(doc, `الاسم: ${data.name || '-'}`, doc.internal.pageSize.width - 20, currentY + 8);
  currentY = writeArabicText(doc, `التاريخ: ${data.date || '-'}`, doc.internal.pageSize.width - 20, currentY + 5);
  currentY = writeArabicText(doc, `الوصف: ${data.description || '-'}`, doc.internal.pageSize.width - 20, currentY + 5);
  
  currentY += 20;
  
  // إضافة جدول البيانات
  if (data.items && data.items.length > 0) {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('تفاصيل الأصناف', doc.internal.pageSize.width - 15, currentY, { align: 'right' });
    currentY += 10;
    
    const headers = ['الرقم', 'الاسم', 'الكمية', 'السعر'];
    const tableData = data.items.map((item: any, index: number) => [
      (index + 1).toString(),
      item.name || '-',
      item.quantity || '-',
      item.price || '-'
    ]);
    
    currentY = createArabicTable(doc, headers, tableData, currentY);
  }
  
  // إضافة التذييل
  addFooter();
  
  // حفظ الملف
  doc.save(`${fileName}.pdf`);
}

// مثال للاستخدام
export function exportSupplyOrderWithJsPDF(orderData: any) {
  const data = {
    name: orderData.supplierName,
    date: orderData.date,
    description: orderData.notes,
    items: orderData.items.map((item: any) => ({
      name: `${item.manufacturer} ${item.model}`,
      quantity: '1',
      price: item.price || '-'
    }))
  };
  
  createArabicPDF(
    data,
    `supply_order_${orderData.id}`,
    `أمر التوريد رقم ${orderData.id}`
  );
}

// المميزات:
// ✅ ينتج ملف PDF قابل للبحث والنسخ
// ✅ حجم ملف صغير
// ✅ سهولة التعديل والصيانة
// ✅ دعم جيد للجداول والتنسيق

// العيوب:
// ❌ قد تظهر بعض الأحرف العربية بشكل غريب
// ❌ دعم محدود للخطوط العربية المعقدة
// ❌ صعوبة في التحكم الدقيق في التنسيق
