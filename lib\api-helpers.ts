import { NextRequest } from 'next/server';
import { ApiQueryParams, PaginationParams, SortParams, FilterParams, SearchParams } from '@/lib/types';

/**
 * دوال مساعدة لمعالجة معاملات API
 */

/**
 * استخراج معاملات الترقيم من URL
 */
export function extractPaginationParams(request: NextRequest): PaginationParams {
  const { searchParams } = new URL(request.url);
  
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '20');
  const offset = parseInt(searchParams.get('offset') || '0');
  const cursor = searchParams.get('cursor') || undefined;

  return {
    page: Math.max(1, page),
    limit: Math.min(100, Math.max(1, limit)), // حد أقصى 100 عنصر
    offset: Math.max(0, offset),
    cursor
  };
}

/**
 * استخراج معاملات الفرز من URL
 */
export function extractSortParams(request: NextRequest): SortParams | undefined {
  const { searchParams } = new URL(request.url);
  
  const field = searchParams.get('sortBy');
  const direction = searchParams.get('sortOrder') as 'asc' | 'desc';

  if (!field) return undefined;

  return {
    field,
    direction: direction === 'desc' ? 'desc' : 'asc'
  };
}

/**
 * استخراج معاملات التصفية من URL
 */
export function extractFilterParams(request: NextRequest, allowedFilters: string[] = []): FilterParams {
  const { searchParams } = new URL(request.url);
  const filters: FilterParams = {};

  // إذا لم يتم تحديد فلاتر مسموحة، استخدم جميع المعاملات
  if (allowedFilters.length === 0) {
    searchParams.forEach((value, key) => {
      // تجاهل معاملات النظام
      if (!['page', 'limit', 'offset', 'cursor', 'sortBy', 'sortOrder', 'search', 'searchFields'].includes(key)) {
        filters[key] = parseFilterValue(value);
      }
    });
  } else {
    // استخدم الفلاتر المسموحة فقط
    allowedFilters.forEach(filter => {
      const value = searchParams.get(filter);
      if (value !== null) {
        filters[filter] = parseFilterValue(value);
      }
    });
  }

  return filters;
}

/**
 * استخراج معاملات البحث من URL
 */
export function extractSearchParams(request: NextRequest): SearchParams | undefined {
  const { searchParams } = new URL(request.url);
  
  const query = searchParams.get('search');
  const fieldsParam = searchParams.get('searchFields');
  
  if (!query) return undefined;

  const fields = fieldsParam ? fieldsParam.split(',').map(f => f.trim()) : undefined;

  return { query, fields };
}

/**
 * استخراج جميع معاملات API من الطلب
 */
export function extractApiQueryParams(
  request: NextRequest, 
  allowedFilters: string[] = []
): ApiQueryParams {
  return {
    pagination: extractPaginationParams(request),
    sort: extractSortParams(request),
    filters: extractFilterParams(request, allowedFilters),
    search: extractSearchParams(request)
  };
}

/**
 * تحويل معاملات الترقيم إلى معاملات Prisma
 */
export function paginationToPrisma(pagination: PaginationParams) {
  const { page, limit, offset } = pagination;
  
  // إذا تم تحديد offset مباشرة
  if (offset !== undefined && offset > 0) {
    return {
      skip: offset,
      take: limit
    };
  }

  // إذا تم تحديد page
  if (page !== undefined && page > 1) {
    return {
      skip: (page - 1) * (limit || 20),
      take: limit
    };
  }

  // الافتراضي
  return {
    skip: 0,
    take: limit || 20
  };
}

/**
 * تحويل معاملات الفرز إلى معاملات Prisma
 */
export function sortToPrisma(sort: SortParams | undefined, allowedFields: string[] = []) {
  if (!sort || !sort.field) return undefined;

  // التحقق من أن الحقل مسموح
  if (allowedFields.length > 0 && !allowedFields.includes(sort.field)) {
    return undefined;
  }

  return {
    [sort.field]: sort.direction
  };
}

/**
 * تحويل معاملات البحث إلى معاملات Prisma
 */
export function searchToPrisma(search: SearchParams | undefined, defaultFields: string[] = []) {
  if (!search || !search.query) return undefined;

  const fields = search.fields || defaultFields;
  if (fields.length === 0) return undefined;

  // إنشاء شروط البحث لكل حقل
  const searchConditions = fields.map(field => ({
    [field]: {
      contains: search.query,
      mode: 'insensitive' as const
    }
  }));

  return {
    OR: searchConditions
  };
}

/**
 * تحويل معاملات التصفية إلى معاملات Prisma
 */
export function filtersToPrisma(filters: FilterParams, fieldMappings: Record<string, string> = {}) {
  const prismaFilters: any = {};

  Object.entries(filters).forEach(([key, value]) => {
    if (value === undefined || value === null || value === '') return;

    // استخدام تعيين الحقل إذا كان متاحاً
    const field = fieldMappings[key] || key;

    // معالجة أنواع مختلفة من القيم
    if (typeof value === 'string') {
      // إذا كانت القيمة تحتوي على فاصلة، فهي قائمة
      if (value.includes(',')) {
        const values = value.split(',').map(v => v.trim());
        prismaFilters[field] = { in: values };
      } else {
        // بحث نصي
        prismaFilters[field] = {
          contains: value,
          mode: 'insensitive'
        };
      }
    } else if (typeof value === 'number') {
      prismaFilters[field] = value;
    } else if (typeof value === 'boolean') {
      prismaFilters[field] = value;
    } else {
      prismaFilters[field] = value;
    }
  });

  return prismaFilters;
}

/**
 * إنشاء استجابة مرقمة
 */
export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  pagination: PaginationParams,
  queryParams?: ApiQueryParams
) {
  const { page = 1, limit = 20 } = pagination;
  const totalPages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    },
    meta: {
      filters: queryParams?.filters,
      search: queryParams?.search,
      sort: queryParams?.sort
    }
  };
}

/**
 * تحليل قيمة الفلتر من النص
 */
function parseFilterValue(value: string): any {
  // محاولة تحويل إلى رقم
  if (!isNaN(Number(value))) {
    return Number(value);
  }

  // محاولة تحويل إلى boolean
  if (value.toLowerCase() === 'true') return true;
  if (value.toLowerCase() === 'false') return false;

  // محاولة تحليل JSON
  try {
    return JSON.parse(value);
  } catch {
    // إرجاع كنص
    return value;
  }
}

/**
 * التحقق من صحة معاملات الترقيم
 */
export function validatePaginationParams(pagination: PaginationParams): string[] {
  const errors: string[] = [];

  if (pagination.page !== undefined && pagination.page < 1) {
    errors.push('رقم الصفحة يجب أن يكون أكبر من 0');
  }

  if (pagination.limit !== undefined && (pagination.limit < 1 || pagination.limit > 100)) {
    errors.push('حد العناصر يجب أن يكون بين 1 و 100');
  }

  if (pagination.offset !== undefined && pagination.offset < 0) {
    errors.push('الإزاحة يجب أن تكون أكبر من أو تساوي 0');
  }

  return errors;
}

/**
 * التحقق من صحة معاملات الفرز
 */
export function validateSortParams(sort: SortParams, allowedFields: string[]): string[] {
  const errors: string[] = [];

  if (!allowedFields.includes(sort.field)) {
    errors.push(`حقل الفرز '${sort.field}' غير مسموح`);
  }

  if (!['asc', 'desc'].includes(sort.direction)) {
    errors.push('اتجاه الفرز يجب أن يكون asc أو desc');
  }

  return errors;
}
