#!/usr/bin/env tsx

// Script لاختبار endpoint توليد رقم أمر التوريد
async function testGenerateSupplyNumber() {
  console.log('🧪 اختبار endpoint توليد رقم أمر التوريد...');

  const baseUrl = 'http://localhost:9005';
  const token = Buffer.from('user:admin:admin').toString('base64');
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  try {
    // اختبار endpoint توليد رقم أمر التوريد
    console.log('\n🔢 اختبار توليد رقم أمر التوريد...');
    
    const response = await fetch(`${baseUrl}/api/supply/generate-supply-number`, {
      method: 'POST',
      headers
    });

    console.log(`📊 حالة الاستجابة: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ تم توليد رقم أمر التوريد: ${data.supplyNumber}`);
      
      // اختبار توليد رقم آخر للتأكد من التسلسل
      const response2 = await fetch(`${baseUrl}/api/supply/generate-supply-number`, {
        method: 'POST',
        headers
      });

      if (response2.ok) {
        const data2 = await response2.json();
        console.log(`✅ تم توليد رقم أمر التوريد الثاني: ${data2.supplyNumber}`);
      }
    } else {
      const error = await response.text();
      console.log(`❌ خطأ: ${error}`);
    }

    // اختبار endpoint GET للتأكد من الأوامر الموجودة
    console.log('\n📋 فحص الأوامر الموجودة...');
    
    const getResponse = await fetch(`${baseUrl}/api/supply`, {
      method: 'GET',
      headers
    });

    if (getResponse.ok) {
      const ordersData = await getResponse.json();
      console.log(`📦 عدد أوامر التوريد الموجودة: ${ordersData.data?.length || ordersData.length || 0}`);
      
      if (ordersData.data && ordersData.data.length > 0) {
        console.log('\n📋 آخر أوامر التوريد:');
        ordersData.data.slice(0, 3).forEach((order: any) => {
          console.log(`- ${order.supplyOrderId} (ID: ${order.id})`);
        });
      }
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  }
}

testGenerateSupplyNumber();
