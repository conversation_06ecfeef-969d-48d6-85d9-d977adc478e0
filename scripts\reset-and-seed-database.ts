import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 بدء إعادة تهيئة قاعدة البيانات...')

  try {
    // حذف جميع البيانات الموجودة (بترتيب معكوس لتجنب أخطاء المراجع)
    console.log('🗑️  حذف البيانات الموجودة...')
    
    await prisma.deviceMovement.deleteMany()
    await prisma.auditLog.deleteMany()
    await prisma.internalMessage.deleteMany()
    await prisma.employeeRequest.deleteMany()
    await prisma.maintenanceLog.deleteMany()
    
    // حذف عناصر الأوامر
    await prisma.stocktakeItem.deleteMany()
    await prisma.warehouseTransferItem.deleteMany()
    await prisma.deliveryOrderItem.deleteMany()
    await prisma.maintenanceReceiptOrderItem.deleteMany()
    await prisma.maintenanceOrderItem.deleteMany()
    await prisma.evaluationOrderItem.deleteMany()
    await prisma.returnItem.deleteMany()
    await prisma.saleItem.deleteMany()
    await prisma.supplyOrderItem.deleteMany()
    
    // حذف الأوامر
    await prisma.stocktakeOperation.deleteMany()
    await prisma.warehouseTransfer.deleteMany()
    await prisma.deliveryOrder.deleteMany()
    await prisma.maintenanceReceiptOrder.deleteMany()
    await prisma.maintenanceOrder.deleteMany()
    await prisma.evaluationOrder.deleteMany()
    await prisma.return.deleteMany()
    await prisma.sale.deleteMany()
    await prisma.supplyOrder.deleteMany()
    
    // حذف البيانات الأساسية
    await prisma.warehouseStock.deleteMany()
    await prisma.device.deleteMany()
    await prisma.deviceModel.deleteMany()
    await prisma.manufacturer.deleteMany()
    await prisma.warehouse.deleteMany()
    await prisma.supplier.deleteMany()
    await prisma.client.deleteMany()
    
    // إعادة ضبط التسلسلات
    await prisma.$executeRaw`ALTER SEQUENCE IF EXISTS "Manufacturer_id_seq" RESTART WITH 1;`
    await prisma.$executeRaw`ALTER SEQUENCE IF EXISTS "DeviceModel_id_seq" RESTART WITH 1;`
    await prisma.$executeRaw`ALTER SEQUENCE IF EXISTS "Warehouse_id_seq" RESTART WITH 1;`
    await prisma.$executeRaw`ALTER SEQUENCE IF EXISTS "Supplier_id_seq" RESTART WITH 1;`
    await prisma.$executeRaw`ALTER SEQUENCE IF EXISTS "Client_id_seq" RESTART WITH 1;`
    
    console.log('✅ تم حذف البيانات الموجودة بنجاح')

    // إنشاء بيانات أساسية جديدة
    console.log('📊 إنشاء البيانات الأساسية...')

    // إنشاء الشركات المصنعة
    const manufacturers = await Promise.all([
      prisma.manufacturer.create({
        data: {
          name: 'Samsung',
          nameEn: 'Samsung',
          code: 'SAM',
          status: 'Active'
        }
      }),
      prisma.manufacturer.create({
        data: {
          name: 'Apple',
          nameEn: 'Apple',
          code: 'APL',
          status: 'Active'
        }
      }),
      prisma.manufacturer.create({
        data: {
          name: 'Xiaomi',
          nameEn: 'Xiaomi',
          code: 'XIA',
          status: 'Active'
        }
      }),
      prisma.manufacturer.create({
        data: {
          name: 'Huawei',
          nameEn: 'Huawei',
          code: 'HUA',
          status: 'Active'
        }
      })
    ])

    console.log(`✅ تم إنشاء ${manufacturers.length} شركة مصنعة`)

    // إنشاء موديلات الأجهزة
    const deviceModels = await Promise.all([
      // Samsung Models
      prisma.deviceModel.create({
        data: {
          name: 'Galaxy S24 Ultra',
          nameEn: 'Galaxy S24 Ultra',
          manufacturerId: manufacturers[0].id,
          category: 'هاتف ذكي',
          categoryEn: 'Smartphone',
          specifications: {
            screen: '6.8 inch',
            storage: ['256GB', '512GB', '1TB'],
            ram: ['12GB', '16GB'],
            camera: '200MP'
          }
        }
      }),
      prisma.deviceModel.create({
        data: {
          name: 'Galaxy A54',
          nameEn: 'Galaxy A54',
          manufacturerId: manufacturers[0].id,
          category: 'هاتف ذكي',
          categoryEn: 'Smartphone'
        }
      }),
      // Apple Models
      prisma.deviceModel.create({
        data: {
          name: 'iPhone 15 Pro Max',
          nameEn: 'iPhone 15 Pro Max',
          manufacturerId: manufacturers[1].id,
          category: 'هاتف ذكي',
          categoryEn: 'Smartphone',
          specifications: {
            screen: '6.7 inch',
            storage: ['256GB', '512GB', '1TB'],
            camera: '48MP'
          }
        }
      }),
      prisma.deviceModel.create({
        data: {
          name: 'iPhone 15',
          nameEn: 'iPhone 15',
          manufacturerId: manufacturers[1].id,
          category: 'هاتف ذكي',
          categoryEn: 'Smartphone'
        }
      })
    ])

    console.log(`✅ تم إنشاء ${deviceModels.length} موديل جهاز`)

    // إنشاء المخازن
    const warehouses = await Promise.all([
      prisma.warehouse.create({
        data: {
          name: 'المخزن الرئيسي',
          nameEn: 'Main Warehouse',
          code: 'MAIN-01',
          type: 'main',
          location: 'الرياض',
          address: 'شارع الملك فهد، الرياض',
          phone: '+966501234567',
          capacity: 10000,
          status: 'Active',
          description: 'المخزن الرئيسي للشركة'
        }
      }),
      prisma.warehouse.create({
        data: {
          name: 'مخزن جدة',
          nameEn: 'Jeddah Warehouse',
          code: 'JED-01',
          type: 'branch',
          location: 'جدة',
          address: 'شارع التحلية، جدة',
          phone: '+966502234567',
          capacity: 5000,
          status: 'Active',
          description: 'مخزن فرع جدة'
        }
      }),
      prisma.warehouse.create({
        data: {
          name: 'مخزن الصيانة',
          nameEn: 'Maintenance Warehouse',
          code: 'MAINT-01',
          type: 'maintenance',
          location: 'الرياض',
          address: 'المنطقة الصناعية، الرياض',
          phone: '+966503234567',
          capacity: 2000,
          status: 'Active',
          description: 'مخزن خاص بأجهزة الصيانة'
        }
      })
    ])

    console.log(`✅ تم إنشاء ${warehouses.length} مخزن`)

    // إنشاء الموردين
    const suppliers = await Promise.all([
      prisma.supplier.create({
        data: {
          name: 'مورد التقنية المتقدمة',
          nameEn: 'Advanced Tech Supplier',
          code: 'ATS-001',
          phone: '+966504234567',
          email: '<EMAIL>',
          address: 'الرياض، المملكة العربية السعودية',
          contactPerson: 'أحمد محمد',
          taxNumber: '*********',
          status: 'Active'
        }
      }),
      prisma.supplier.create({
        data: {
          name: 'شركة الإلكترونيات الذكية',
          nameEn: 'Smart Electronics Co.',
          code: 'SEC-001',
          phone: '+966505234567',
          email: '<EMAIL>',
          address: 'جدة، المملكة العربية السعودية',
          contactPerson: 'سارة أحمد',
          taxNumber: '*********',
          status: 'Active'
        }
      })
    ])

    console.log(`✅ تم إنشاء ${suppliers.length} مورد`)

    // إنشاء العملاء
    const clients = await Promise.all([
      prisma.client.create({
        data: {
          name: 'محمد عبدالله',
          nameEn: 'Mohammed Abdullah',
          code: 'CLI-001',
          phone: '+966506234567',
          email: '<EMAIL>',
          address: 'الرياض',
          idNumber: '*********0',
          type: 'individual',
          status: 'Active'
        }
      }),
      prisma.client.create({
        data: {
          name: 'شركة التقنية الحديثة',
          nameEn: 'Modern Tech Company',
          code: 'CLI-002',
          phone: '+966507234567',
          email: '<EMAIL>',
          address: 'جدة',
          taxNumber: '*********',
          type: 'company',
          status: 'Active'
        }
      })
    ])

    console.log(`✅ تم إنشاء ${clients.length} عميل`)

    // إنشاء بعض الأجهزة التجريبية
    const devices = await Promise.all([
      prisma.device.create({
        data: {
          id: '354234567890123', // IMEI example
          manufacturerId: manufacturers[0].id,
          deviceModelId: deviceModels[0].id,
          warehouseId: warehouses[0].id,
          supplierId: suppliers[0].id,
          status: 'في المخزن',
          condition: 'جديد',
          grade: 'A+',
          storage: '256GB',
          color: 'أسود',
          price: 4500.00,
          costPrice: 4000.00,
          warrantyPeriod: 12
        }
      }),
      prisma.device.create({
        data: {
          id: '354234567890124',
          manufacturerId: manufacturers[1].id,
          deviceModelId: deviceModels[2].id,
          warehouseId: warehouses[0].id,
          supplierId: suppliers[1].id,
          status: 'في المخزن',
          condition: 'جديد',
          grade: 'A+',
          storage: '256GB',
          color: 'أزرق',
          price: 5500.00,
          costPrice: 5000.00,
          warrantyPeriod: 12
        }
      })
    ])

    console.log(`✅ تم إنشاء ${devices.length} جهاز تجريبي`)

    // تحديث مخزون المخازن
    for (const device of devices) {
      await prisma.warehouseStock.upsert({
        where: {
          warehouseId_manufacturerId_deviceModelId_condition: {
            warehouseId: device.warehouseId,
            manufacturerId: device.manufacturerId,
            deviceModelId: device.deviceModelId,
            condition: device.condition
          }
        },
        update: {
          totalQuantity: { increment: 1 },
          availableQuantity: { increment: 1 }
        },
        create: {
          warehouseId: device.warehouseId,
          manufacturerId: device.manufacturerId,
          deviceModelId: device.deviceModelId,
          condition: device.condition,
          totalQuantity: 1,
          availableQuantity: 1
        }
      })
    }

    console.log('✅ تم تحديث مخزون المخازن')

    // تحديث إعدادات النظام
    await prisma.systemSetting.upsert({
      where: { id: 1 },
      update: {
        companyNameAr: 'نظام إدارة الأجهزة المحسّن',
        companyNameEn: 'Enhanced Device Management System',
        updatedAt: new Date()
      },
      create: {
        id: 1,
        companyNameAr: 'نظام إدارة الأجهزة المحسّن',
        companyNameEn: 'Enhanced Device Management System'
      }
    })

    console.log('✅ تم تحديث إعدادات النظام')
    console.log('🎉 تم إكمال إعادة تهيئة قاعدة البيانات بنجاح!')

  } catch (error) {
    console.error('❌ خطأ في إعادة تهيئة قاعدة البيانات:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
