'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  fetchDevices, 
  fetchSales, 
  fetchSupplyOrders, 
  fetchClients, 
  fetchSuppliers,
  fetchEmployeeRequests 
} from '@/lib/data-fetcher';
import { globalCache, staticDataCache, dynamicDataCache } from '@/lib/cache-manager';
import { Clock, Database, Zap, TrendingUp, CheckCircle, AlertCircle } from 'lucide-react';

interface PerformanceTest {
  name: string;
  description: string;
  testFunction: () => Promise<{ duration: number; success: boolean; data?: any; error?: string }>;
}

interface TestResult {
  name: string;
  duration: number;
  success: boolean;
  error?: string;
  timestamp: Date;
}

export default function PerformanceTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [cacheStats, setCacheStats] = useState<any>({});

  // Performance tests configuration
  const performanceTests: PerformanceTest[] = [
    {
      name: 'جلب الأجهزة (صفحة واحدة)',
      description: 'اختبار جلب 10 أجهزة مع الترقيم',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchDevices({ pagination: { page: 1, limit: 10 } });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'جلب الأجهزة (صفحات متعددة)',
      description: 'اختبار جلب 100 جهاز مع الترقيم',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchDevices({ pagination: { page: 1, limit: 100 } });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'البحث في الأجهزة',
      description: 'اختبار البحث في الأجهزة بكلمة مفتاحية',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchDevices({ 
          search: { query: 'iPhone' },
          pagination: { page: 1, limit: 20 }
        });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'تصفية الأجهزة',
      description: 'اختبار تصفية الأجهزة حسب الحالة',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchDevices({ 
          filters: { status: 'متاح للبيع' },
          pagination: { page: 1, limit: 20 }
        });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'ترتيب الأجهزة',
      description: 'اختبار ترتيب الأجهزة حسب التاريخ',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchDevices({ 
          sort: { field: 'createdAt', direction: 'desc' },
          pagination: { page: 1, limit: 20 }
        });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'جلب المبيعات',
      description: 'اختبار جلب المبيعات مع الترقيم',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchSales({ pagination: { page: 1, limit: 20 } });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'جلب أوامر التوريد',
      description: 'اختبار جلب أوامر التوريد مع الترقيم',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchSupplyOrders({ pagination: { page: 1, limit: 20 } });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'جلب العملاء',
      description: 'اختبار جلب العملاء مع الترقيم',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchClients({ pagination: { page: 1, limit: 20 } });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'جلب الموردين',
      description: 'اختبار جلب الموردين مع الترقيم',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchSuppliers({ pagination: { page: 1, limit: 20 } });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'جلب طلبات الموظفين',
      description: 'اختبار جلب طلبات الموظفين مع الترقيم',
      testFunction: async () => {
        const start = performance.now();
        const result = await fetchEmployeeRequests({ pagination: { page: 1, limit: 20 } });
        const duration = performance.now() - start;
        return { duration, success: true, data: result };
      }
    },
    {
      name: 'اختبار التخزين المؤقت',
      description: 'اختبار فعالية التخزين المؤقت',
      testFunction: async () => {
        // First call (should be slow)
        const start1 = performance.now();
        await fetchDevices({ pagination: { page: 1, limit: 10 } });
        const duration1 = performance.now() - start1;

        // Second call (should be fast due to caching)
        const start2 = performance.now();
        await fetchDevices({ pagination: { page: 1, limit: 10 } });
        const duration2 = performance.now() - start2;

        const improvement = ((duration1 - duration2) / duration1) * 100;
        return { 
          duration: duration2, 
          success: improvement > 50, // Cache should improve performance by at least 50%
          data: { firstCall: duration1, secondCall: duration2, improvement }
        };
      }
    }
  ];

  // Update cache statistics
  const updateCacheStats = useCallback(() => {
    setCacheStats({
      global: globalCache.getStats(),
      static: staticDataCache.getStats(),
      dynamic: dynamicDataCache.getStats(),
    });
  }, []);

  // Run all performance tests
  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setProgress(0);

    for (let i = 0; i < performanceTests.length; i++) {
      const test = performanceTests[i];
      setCurrentTest(test.name);
      
      try {
        const result = await test.testFunction();
        setTestResults(prev => [...prev, {
          name: test.name,
          duration: result.duration,
          success: result.success,
          error: result.error,
          timestamp: new Date()
        }]);
      } catch (error) {
        setTestResults(prev => [...prev, {
          name: test.name,
          duration: 0,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date()
        }]);
      }

      setProgress(((i + 1) / performanceTests.length) * 100);
      updateCacheStats();
    }

    setIsRunning(false);
    setCurrentTest('');
  };

  // Clear all caches
  const clearAllCaches = () => {
    globalCache.clear();
    staticDataCache.clear();
    dynamicDataCache.clear();
    updateCacheStats();
  };

  useEffect(() => {
    updateCacheStats();
    const interval = setInterval(updateCacheStats, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [updateCacheStats]);

  const averageResponseTime = testResults.length > 0 
    ? testResults.reduce((sum, result) => sum + result.duration, 0) / testResults.length 
    : 0;

  const successRate = testResults.length > 0 
    ? (testResults.filter(result => result.success).length / testResults.length) * 100 
    : 0;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">اختبار الأداء</h1>
          <p className="text-muted-foreground">
            اختبار أداء نظام جلب البيانات عند الطلب
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={clearAllCaches} variant="outline">
            مسح التخزين المؤقت
          </Button>
          <Button onClick={runAllTests} disabled={isRunning}>
            {isRunning ? 'جاري التشغيل...' : 'تشغيل جميع الاختبارات'}
          </Button>
        </div>
      </div>

      {/* Progress */}
      {isRunning && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>الاختبار الحالي: {currentTest}</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Statistics */}
      {testResults.length > 0 && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الاختبارات</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{testResults.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">متوسط وقت الاستجابة</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{averageResponseTime.toFixed(2)}ms</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">معدل النجاح</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{successRate.toFixed(1)}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الاختبارات الناجحة</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {testResults.filter(r => r.success).length}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="results" className="space-y-4">
        <TabsList>
          <TabsTrigger value="results">نتائج الاختبارات</TabsTrigger>
          <TabsTrigger value="cache">إحصائيات التخزين المؤقت</TabsTrigger>
        </TabsList>

        <TabsContent value="results" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>نتائج اختبارات الأداء</CardTitle>
            </CardHeader>
            <CardContent>
              {testResults.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  لم يتم تشغيل أي اختبارات بعد
                </p>
              ) : (
                <div className="space-y-2">
                  {testResults.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {result.success ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                        <div>
                          <div className="font-medium">{result.name}</div>
                          {result.error && (
                            <div className="text-sm text-red-500">{result.error}</div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={result.success ? 'default' : 'destructive'}>
                          {result.duration.toFixed(2)}ms
                        </Badge>
                        <Badge variant={result.success ? 'default' : 'secondary'}>
                          {result.success ? 'نجح' : 'فشل'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            {Object.entries(cacheStats).map(([cacheType, stats]: [string, any]) => (
              <Card key={cacheType}>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {cacheType === 'global' ? 'التخزين العام' : 
                     cacheType === 'static' ? 'التخزين الثابت' : 'التخزين الديناميكي'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>العناصر المخزنة:</span>
                    <span className="font-mono">{stats?.size || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>النجاحات:</span>
                    <span className="font-mono text-green-600">{stats?.hits || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الإخفاقات:</span>
                    <span className="font-mono text-red-600">{stats?.misses || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>معدل النجاح:</span>
                    <span className="font-mono">
                      {stats?.hits && stats?.misses 
                        ? ((stats.hits / (stats.hits + stats.misses)) * 100).toFixed(1) + '%'
                        : '0%'
                      }
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
