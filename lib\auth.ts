import { NextRequest } from 'next/server';
import { prisma } from './prisma';
import { checkRequiredEnvVars } from './env-check';

export interface AuthUser {
  id: number;  // تغيير من string إلى number
  username: string;
  role: string;
  name?: string; // إضافة الاسم
}

export interface AuthResult {
  success: boolean;
  user?: AuthUser;
  error?: string;
}

/**
 * نظام تفويض مبسط بدون JWT (للتطوير والاختبار)
 * في الإنتاج، يُنصح باستخدام JWT أو نظام تفويض أكثر تقدماً
 */
export async function verifyAuth(request: NextRequest): Promise<AuthResult> {
  try {
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'Missing or invalid authorization header'
      };
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // نظام تفويض مبسط - في الإنتاج استخدم JWT
    // التوكن المتوقع: "user:username:role" مُرمز بـ base64
    try {
      const decoded = atob(token); // فك ترميز base64
      const parts = decoded.split(':');

      if (parts.length !== 3 || parts[0] !== 'user') {
        throw new Error('Invalid token format');
      }

      const [, username, role] = parts;

      if (!username || !role) {
        throw new Error('Missing user data');
      }

      // البحث عن المستخدم في قاعدة البيانات
      let user = await prisma.user.findFirst({
        where: {
          OR: [
            { username: username },
            { email: username }
          ]
        }
      });

      // إذا لم يوجد المستخدم، أنشئ مستخدمًا مؤقتًا للـ admin
      let userId = user?.id;
      if (!user && username === 'admin') {
        // البحث عن المستخدم الإداري بالاسم أو email أو الصلاحية
        user = await prisma.user.findFirst({
          where: {
            OR: [
              { email: '<EMAIL>' },
              { role: 'admin', status: 'Active' },
              { username: 'admin' },
              { name: { contains: 'مدير' } } // البحث عن أي مستخدم يحتوي على كلمة "مدير"
            ]
          }
        });

        if (user) {
          // إذا وُجد مستخدم إداري، استخدمه
          userId = user.id;
        } else {
          // إنشاء مستخدم إداري جديد فقط إذا لم يوجد أي مستخدم إداري
          const adminUser = await prisma.user.upsert({
            where: { email: '<EMAIL>' },
            update: { role: 'admin', username: 'admin' },
            create: {
              username: 'admin',
              email: '<EMAIL>',
              name: 'System Administrator',
              role: 'admin',
              status: 'Active'
            }
          });
          user = adminUser;
          userId = adminUser.id;
        }
      }

      if (!userId || !user) {
        throw new Error('User not found');
      }

      return {
        success: true,
        user: {
          id: userId,
          username: user.username || username,
          role: user.role || role,
          name: user.name // إضافة الاسم للاستخدام في التحقق من الصلاحيات
        }
      };
    } catch (decodeError) {
      throw new Error('Invalid token');
    }
  } catch (error) {
    console.error('Auth verification error:', error);
    return {
      success: false,
      error: 'Invalid or expired token'
    };
  }
}

/**
 * التحقق من صلاحيات المستخدم
 */
export function hasPermission(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'admin': 3,
    'manager': 2,
    'user': 1,
    'guest': 0
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
}

/**
 * Middleware للتحقق من التفويض
 */
export async function requireAuth(
  request: NextRequest, 
  requiredRole: string = 'user'
): Promise<AuthResult> {
  const authResult = await verifyAuth(request);
  
  if (!authResult.success || !authResult.user) {
    return authResult;
  }

  if (!hasPermission(authResult.user.role, requiredRole)) {
    return {
      success: false,
      error: 'Insufficient permissions'
    };
  }

  return authResult;
}

/**
 * استخراج معلومات المستخدم من الطلب (للاستخدام في audit logs)
 */
export async function getUserFromRequest(request: NextRequest): Promise<AuthUser | null> {
  const authResult = await verifyAuth(request);
  return authResult.success ? authResult.user! : null;
}

/**
 * إنشاء توكن مبسط للاختبار (بدون JWT)
 * في الإنتاج، استخدم JWT مع secret key قوي
 */
export function createSimpleToken(username: string, role: string = 'user'): string {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData); // ترميز base64
}

/**
 * أمثلة على التوكنات للاختبار:
 *
 * Admin token: createSimpleToken('admin', 'admin')
 * Manager token: createSimpleToken('manager', 'manager')
 * User token: createSimpleToken('user', 'user')
 *
 * استخدام في headers:
 * Authorization: Bearer dXNlcjphZG1pbjphZG1pbg==
 */
