# دليل نظام الطباعة والتصدير الموحد

## 📋 نظرة عامة

هذا النظام يوفر حلول طباعة وتصدير موحدة يمكن استخدامها في جميع أقسام التطبيق بدون تكرار الكود.

## 🎯 المميزات

- ✅ **نظام موحد**: استخدم نفس الكود في جميع الأقسام
- ✅ **قوالب جاهزة**: قوالب مُعدة مسبقاً للأقسام المختلفة
- ✅ **دعم العربية**: خطوط عربية واضحة وتنسيق RTL
- ✅ **مرونة عالية**: قابل للتخصيص حسب احتياجات كل قسم
- ✅ **سهولة الاستخدام**: مكونات جاهزة للاستخدام المباشر

## 🚀 الاستخدام السريع

### 1. الاستخدام الأساسي

```tsx
import { PrintExportButtons } from '@/components/ui/print-export-buttons';

function MyComponent() {
  const data = {
    title: 'تقرير المبيعات',
    sections: [
      {
        title: 'المعلومات الأساسية',
        type: 'info',
        data: {
          'رقم الفاتورة': 'INV-001',
          'التاريخ': '2024-01-15',
          'العميل': 'أحمد محمد'
        }
      }
    ]
  };

  return (
    <PrintExportButtons 
      data={data} 
      options={{ fileName: 'sales_report' }}
    />
  );
}
```

### 2. استخدام القوالب الجاهزة

```tsx
import { createSalesTemplate } from '@/lib/print-templates';
import { PrintExportButtons } from '@/components/ui/print-export-buttons';

function SalesReport() {
  const salesData = createSalesTemplate({
    customerInfo: {
      'اسم العميل': 'أحمد محمد',
      'رقم الهاتف': '0501234567',
      'العنوان': 'الرياض، السعودية'
    },
    invoiceInfo: {
      'رقم الفاتورة': 'INV-001',
      'التاريخ': '2024-01-15',
      'المبلغ الإجمالي': '1,500 ريال'
    },
    items: [
      ['iPhone 14', '1', '3000', '0', '3000'],
      ['غطاء حماية', '1', '50', '10', '45']
    ]
  });

  return (
    <PrintExportButtons 
      data={salesData}
      options={{ fileName: 'invoice_001' }}
    />
  );
}
```

## 📚 القوالب المتاحة

### 1. قالب التوريد

```tsx
import { createSupplyTemplate } from '@/lib/print-templates';

const supplyData = createSupplyTemplate({
  supplierInfo: {
    'اسم المورد': 'شركة التقنية المتقدمة',
    'رقم الهاتف': '0112345678',
    'البريد الإلكتروني': '<EMAIL>'
  },
  orderInfo: {
    'رقم الطلب': 'PO-001',
    'تاريخ الطلب': '2024-01-10',
    'تاريخ التسليم المتوقع': '2024-01-20'
  },
  items: [
    ['iPhone 14 Pro', '10', '4000', '40000', 'اللون الأزرق'],
    ['iPad Air', '5', '2500', '12500', 'ذاكرة 256GB']
  ],
  timeline: [
    {
      title: 'تم إنشاء الطلب',
      description: 'تم إنشاء طلب التوريد بنجاح',
      date: '2024-01-10',
      user: 'أحمد محمد'
    }
  ]
});
```

### 2. قالب المبيعات

```tsx
import { createSalesTemplate } from '@/lib/print-templates';

const salesData = createSalesTemplate({
  customerInfo: {
    'اسم العميل': 'سارة أحمد',
    'رقم الهوية': '1234567890',
    'رقم الهاتف': '0501234567'
  },
  invoiceInfo: {
    'رقم الفاتورة': 'INV-001',
    'التاريخ': '2024-01-15',
    'طريقة الدفع': 'نقداً'
  },
  items: [
    ['iPhone 14', '1', '3000', '0', '3000'],
    ['AirPods Pro', '1', '800', '50', '750']
  ],
  paymentInfo: {
    'المبلغ الفرعي': '3800 ريال',
    'الخصم': '50 ريال',
    'الضريبة': '562.5 ريال',
    'المبلغ الإجمالي': '4312.5 ريال'
  }
});
```

### 3. قالب المخزون

```tsx
import { createInventoryTemplate } from '@/lib/print-templates';

const inventoryData = createInventoryTemplate({
  warehouseInfo: {
    'اسم المخزن': 'المخزن الرئيسي',
    'الموقع': 'الرياض',
    'المسؤول': 'علي حسن'
  },
  summary: {
    'إجمالي الأصناف': '150',
    'القيمة الإجمالية': '500,000 ريال',
    'الأصناف المنخفضة': '5'
  },
  items: [
    ['iPhone 14', '25', '10', 'متاح', '2024-01-15'],
    ['iPad Air', '5', '10', 'منخفض', '2024-01-14']
  ]
});
```

## 🛠️ الاستخدام المتقدم

### 1. إنشاء قالب مخصص

```tsx
import { createCustomTemplate } from '@/lib/print-templates';

const customData = createCustomTemplate(
  'تقرير مخصص',
  [
    {
      title: 'معلومات أساسية',
      type: 'info',
      data: { 'المشروع': 'نظام إدارة المخزون' }
    },
    {
      title: 'البيانات',
      type: 'table',
      data: [['العنصر 1', 'القيمة 1'], ['العنصر 2', 'القيمة 2']],
      columns: ['العنصر', 'القيمة']
    },
    {
      title: 'الأحداث',
      type: 'timeline',
      data: [
        {
          title: 'بداية المشروع',
          description: 'تم بدء العمل في المشروع',
          date: '2024-01-01',
          user: 'فريق التطوير'
        }
      ]
    }
  ],
  'تقرير شامل للمشروع'
);
```

### 2. استخدام Hook مباشرة

```tsx
import { usePrintExport } from '@/hooks/usePrintExport';

function MyComponent() {
  const { printData, exportToPDF, isLoading } = usePrintExport();

  const handlePrint = () => {
    printData({
      title: 'تقريري المخصص',
      sections: [
        {
          title: 'البيانات',
          type: 'info',
          data: { 'المعلومة': 'القيمة' }
        }
      ]
    });
  };

  return (
    <button onClick={handlePrint} disabled={isLoading}>
      {isLoading ? 'جاري الطباعة...' : 'طباعة'}
    </button>
  );
}
```

### 3. مكونات مبسطة

```tsx
import { QuickPrint, TablePrint, TimelinePrint } from '@/components/ui/print-export-buttons';

// طباعة سريعة للمعلومات
<QuickPrint 
  title="معلومات العميل"
  data={{ 'الاسم': 'أحمد', 'الهاتف': '123456' }}
  fileName="customer_info"
/>

// طباعة جدول
<TablePrint
  title="قائمة المنتجات"
  data={products}
  columns={['الاسم', 'السعر', 'الكمية']}
  fileName="products_list"
/>

// طباعة تايم لاين
<TimelinePrint
  title="سجل الأنشطة"
  events={activities}
  fileName="activities_log"
/>
```

## 🎨 التخصيص

### 1. تخصيص الألوان والتنسيق

```tsx
const customOptions = {
  fileName: 'my_report',
  title: 'تقريري المخصص',
  template: 'invoice', // أو 'report' أو 'list'
  pageSize: 'A4',
  orientation: 'portrait'
};

<PrintExportButtons 
  data={myData}
  options={customOptions}
  variant="compact"
  showLabels={false}
/>
```

### 2. إضافة أقسام مخصصة

```tsx
const dataWithCustomSection = {
  title: 'تقرير شامل',
  sections: [
    {
      title: 'HTML مخصص',
      type: 'custom',
      data: `
        <div style="background: #f0f0f0; padding: 10px; border-radius: 5px;">
          <h3>قسم مخصص</h3>
          <p>يمكنك إضافة أي HTML هنا</p>
        </div>
      `
    }
  ]
};
```

## 📱 أمثلة للأقسام المختلفة

### قسم التوريد

```tsx
// في صفحة طلبات التوريد
import { createSupplyTemplate } from '@/lib/print-templates';
import { PrintExportButtons } from '@/components/ui/print-export-buttons';

function SupplyOrderPage({ order }) {
  const printData = createSupplyTemplate({
    supplierInfo: order.supplier,
    orderInfo: order.details,
    items: order.items,
    timeline: order.history
  });

  return (
    <div>
      {/* محتوى الصفحة */}
      <PrintExportButtons 
        data={printData}
        options={{ fileName: `supply_order_${order.id}` }}
      />
    </div>
  );
}
```

### قسم المبيعات

```tsx
// في صفحة الفواتير
import { createSalesTemplate } from '@/lib/print-templates';

function InvoicePage({ invoice }) {
  const printData = createSalesTemplate({
    customerInfo: invoice.customer,
    invoiceInfo: invoice.details,
    items: invoice.items,
    paymentInfo: invoice.payment
  });

  return (
    <PrintExportButtons 
      data={printData}
      options={{ fileName: `invoice_${invoice.number}` }}
    />
  );
}
```

### قسم المخزون

```tsx
// في صفحة تقارير المخزون
import { createInventoryTemplate } from '@/lib/print-templates';

function InventoryReportPage({ warehouse, items }) {
  const printData = createInventoryTemplate({
    warehouseInfo: warehouse,
    items: items,
    summary: calculateSummary(items)
  });

  return (
    <PrintExportButtons 
      data={printData}
      options={{ fileName: `inventory_${warehouse.id}` }}
    />
  );
}
```

## 🔧 نصائح للاستخدام

1. **استخدم القوالب الجاهزة** عندما أمكن لتوفير الوقت
2. **خصص أسماء الملفات** لتسهيل التنظيم
3. **اختبر الطباعة** على أحجام ورق مختلفة
4. **استخدم المكونات المبسطة** للحالات البسيطة
5. **أضف معلومات التوقيت** للتقارير المهمة

## 🐛 استكشاف الأخطاء

### مشكلة: النصوص العربية لا تظهر بشكل صحيح
**الحل**: تأكد من استخدام `createArabicPDFWithCanvas` للتصدير

### مشكلة: الطباعة لا تعمل
**الحل**: تحقق من إعدادات المتصفح وتأكد من عدم حظر النوافذ المنبثقة

### مشكلة: البيانات لا تظهر
**الحل**: تأكد من تمرير البيانات بالتنسيق الصحيح للقالب

## 📞 الدعم

للمساعدة أو الاستفسارات، راجع الأمثلة في مجلد `/examples` أو اتصل بفريق التطوير.
