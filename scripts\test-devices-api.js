const { default: fetch } = require('node-fetch');

async function testDevicesAPI() {
  try {
    console.log('Testing Devices API...\n');

    // 1. Test GET - fetch all devices
    console.log('1. Testing GET /api/devices-simple');
    const getResponse = await fetch('http://localhost:9005/api/devices-simple');
    
    console.log('GET Response status:', getResponse.status);
    
    if (getResponse.ok) {
      const result = await getResponse.json();
      if (result.data) {
        // Paginated response
        console.log('Devices found:', result.data.length);
        console.log('Total devices:', result.pagination.total);
        result.data.slice(0, 3).forEach(d => {
          console.log(`- ${d.id} (${d.model}) - ${d.status} - مخزن: ${d.warehouseId || 'غير محدد'}`);
        });
        if (result.data.length > 3) {
          console.log(`... and ${result.data.length - 3} more`);
        }
      } else {
        // Legacy response (array)
        console.log('Devices found:', result.length);
        result.slice(0, 3).forEach(d => {
          console.log(`- ${d.id} (${d.model}) - ${d.status} - مخزن: ${d.warehouseId || 'غير محدد'}`);
        });
      }
    } else {
      const errorText = await getResponse.text();
      console.log('GET Error response:', errorText);
    }

    // 2. Test search functionality
    console.log('\n2. Testing search functionality');
    const searchResponse = await fetch('http://localhost:9005/api/devices-simple?search=iPhone');
    
    if (searchResponse.ok) {
      const searchResult = await searchResponse.json();
      const searchDevices = searchResult.data || searchResult;
      console.log(`Search found ${searchDevices.length} devices containing "iPhone"`);
    } else {
      console.log('Search failed');
    }

    // 3. Test pagination
    console.log('\n3. Testing pagination');
    const paginationResponse = await fetch('http://localhost:9005/api/devices-simple?page=1&limit=5');
    
    if (paginationResponse.ok) {
      const paginationResult = await paginationResponse.json();
      if (paginationResult.pagination) {
        console.log(`Pagination works: Page ${paginationResult.pagination.page}, Total: ${paginationResult.pagination.total}`);
      } else {
        console.log('Pagination format not detected');
      }
    } else {
      console.log('Pagination test failed');
    }

    console.log('\n✅ Devices API test completed!');
    
  } catch (error) {
    console.error('❌ Error testing devices API:', error);
  }
}

testDevicesAPI();
