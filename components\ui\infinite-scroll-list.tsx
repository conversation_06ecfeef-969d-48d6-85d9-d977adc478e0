"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { RefreshCw, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';

// Infinite scroll list component
interface InfiniteScrollListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  loadMore: () => Promise<void>;
  hasMore: boolean;
  isLoading: boolean;
  className?: string;
  threshold?: number;
  emptyMessage?: string;
  loadingMessage?: string;
  showScrollToTop?: boolean;
  onRefresh?: () => Promise<void>;
  refreshing?: boolean;
}

export function InfiniteScrollList<T extends { id: string | number }>({
  items,
  renderItem,
  loadMore,
  hasMore,
  isLoading,
  className = '',
  threshold = 100,
  emptyMessage = "لا توجد عناصر للعرض",
  loadingMessage = "جاري تحميل المزيد...",
  showScrollToTop = true,
  onRefresh,
  refreshing = false
}: InfiniteScrollListProps<T>) {
  const [isMounted, setIsMounted] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const observerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    if (!isMounted || !observerRef.current || !hasMore || isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(observerRef.current);

    return () => observer.disconnect();
  }, [isMounted, hasMore, isLoading, loadMore]);

  // Scroll to top button visibility
  useEffect(() => {
    if (!showScrollToTop || !containerRef.current) return;

    const handleScroll = () => {
      if (containerRef.current) {
        setShowScrollButton(containerRef.current.scrollTop > 300);
      }
    };

    const container = containerRef.current;
    container.addEventListener('scroll', handleScroll);

    return () => container.removeEventListener('scroll', handleScroll);
  }, [showScrollToTop]);

  const scrollToTop = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    if (onRefresh && !refreshing) {
      await onRefresh();
    }
  }, [onRefresh, refreshing]);

  if (items.length === 0 && !isLoading) {
    return (
      <div className={cn('flex flex-col items-center justify-center p-8 space-y-4', className)}>
        <p className="text-muted-foreground text-lg">{emptyMessage}</p>
        {onRefresh && (
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? (
              <LoadingSpinner size="sm" className="ml-2" />
            ) : (
              <RefreshCw className="h-4 w-4 ml-2" />
            )}
            تحديث
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Refresh Button */}
      {onRefresh && (
        <div className="flex justify-end mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <LoadingSpinner size="sm" className="ml-2" />
            ) : (
              <RefreshCw className="h-4 w-4 ml-2" />
            )}
            تحديث
          </Button>
        </div>
      )}

      {/* Items Container */}
      <div
        ref={containerRef}
        className={cn('space-y-2 max-h-[600px] overflow-y-auto', className)}
      >
        {items.map((item, index) => (
          <div key={item.id} className="transition-all duration-200 hover:bg-muted/50 rounded-lg">
            {renderItem(item, index)}
          </div>
        ))}
        
        {/* Loading Trigger */}
        {hasMore && (
          <div ref={observerRef} className="flex items-center justify-center p-4">
            {isLoading ? (
              <LoadingSpinner text={loadingMessage} textPosition="right" />
            ) : (
              <div className="h-4" /> // Invisible trigger area
            )}
          </div>
        )}

        {/* End Message */}
        {!hasMore && items.length > 0 && (
          <div className="flex items-center justify-center p-4 text-muted-foreground">
            <p className="text-sm">تم عرض جميع العناصر</p>
          </div>
        )}
      </div>

      {/* Scroll to Top Button */}
      {showScrollToTop && showScrollButton && (
        <Button
          variant="outline"
          size="icon"
          onClick={scrollToTop}
          className="fixed bottom-4 left-4 z-50 shadow-lg"
        >
          <ChevronUp className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}

// Virtual infinite scroll for large datasets
interface VirtualInfiniteScrollProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  loadMore: () => Promise<void>;
  hasMore: boolean;
  isLoading: boolean;
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
  className?: string;
  emptyMessage?: string;
}

export function VirtualInfiniteScroll<T extends { id: string | number }>({
  items,
  renderItem,
  loadMore,
  hasMore,
  isLoading,
  itemHeight,
  containerHeight,
  overscan = 5,
  className = '',
  emptyMessage = "لا توجد عناصر للعرض"
}: VirtualInfiniteScrollProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const visibleItemsCount = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(items.length, startIndex + visibleItemsCount + overscan * 2);

  const visibleItems = items.slice(startIndex, endIndex);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    setScrollTop(scrollTop);

    // Load more when near bottom
    const scrollHeight = e.currentTarget.scrollHeight;
    const clientHeight = e.currentTarget.clientHeight;
    
    if (scrollHeight - scrollTop - clientHeight < itemHeight * 3 && hasMore && !isLoading) {
      loadMore();
    }
  }, [hasMore, isLoading, loadMore, itemHeight]);

  if (items.length === 0 && !isLoading) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={cn('overflow-auto', className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div
              key={item.id}
              style={{ height: itemHeight }}
              className="flex items-center"
            >
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
        
        {isLoading && (
          <div className="flex items-center justify-center p-4">
            <LoadingSpinner text="جاري تحميل المزيد..." textPosition="right" />
          </div>
        )}
      </div>
    </div>
  );
}

// Grid infinite scroll
interface InfiniteScrollGridProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  loadMore: () => Promise<void>;
  hasMore: boolean;
  isLoading: boolean;
  columns?: number;
  gap?: number;
  className?: string;
  emptyMessage?: string;
}

export function InfiniteScrollGrid<T extends { id: string | number }>({
  items,
  renderItem,
  loadMore,
  hasMore,
  isLoading,
  columns = 3,
  gap = 4,
  className = '',
  emptyMessage = "لا توجد عناصر للعرض"
}: InfiniteScrollGridProps<T>) {
  const observerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!observerRef.current || !hasMore || isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(observerRef.current);

    return () => observer.disconnect();
  }, [hasMore, isLoading, loadMore]);

  if (items.length === 0 && !isLoading) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={className}>
      <div
        className="grid"
        style={{
          gridTemplateColumns: `repeat(${columns}, 1fr)`,
          gap: `${gap * 0.25}rem`
        }}
      >
        {items.map((item, index) => (
          <div key={item.id} className="transition-all duration-200 hover:scale-105">
            {renderItem(item, index)}
          </div>
        ))}
      </div>
      
      {hasMore && (
        <div ref={observerRef} className="flex items-center justify-center p-4 mt-4">
          {isLoading ? (
            <LoadingSpinner text="جاري تحميل المزيد..." textPosition="right" />
          ) : (
            <div className="h-4" />
          )}
        </div>
      )}
    </div>
  );
}
