const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSupplyNumberGeneration() {
  try {
    console.log('Testing supply number generation...');
    
    // Test 1: Check if we can connect to database
    console.log('1. Testing database connection...');
    const count = await prisma.supply_orders.count();
    console.log(`Found ${count} supply orders in database`);

    // Test 2: Check existing supply orders
    console.log('2. Checking existing supply orders...');
    const orders = await prisma.supply_orders.findMany({
      select: { id: true, orderNumber: true },
      orderBy: { id: 'desc' },
      take: 5
    });
    console.log('Recent orders:', orders);
    
    // Test 3: Test the generateUniqueId function
    console.log('3. Testing generateUniqueId function...');

    // Manual implementation for testing
    const allOrders = await prisma.supply_orders.findMany({
      select: { orderNumber: true },
      orderBy: { id: 'desc' }
    });

    let maxNumber = 0;
    allOrders.forEach(order => {
      const match = order.orderNumber.match(/^SUP-(\d+)$/);
      if (match) {
        const num = parseInt(match[1]);
        if (!isNaN(num) && num > maxNumber) {
          maxNumber = num;
        }
      }
    });

    const newId = `SUP-${maxNumber + 1}`;
    console.log('Generated ID:', newId);
    
    console.log('All tests completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSupplyNumberGeneration();
