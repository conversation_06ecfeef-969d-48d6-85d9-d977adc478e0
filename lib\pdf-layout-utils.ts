import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { SystemSettings } from '@/lib/types';

export interface PDFGeneratorOptions {
  title: string;
  tableHeaders: string[];
  tableData: any[][];
  orientation?: 'portrait' | 'landscape';
  customSettings?: SystemSettings['reportLayout'];
}

export function generateCustomPDF(
  settings: SystemSettings,
  options: PDFGeneratorOptions
): jsPDF {
  const {
    title,
    tableHeaders,
    tableData,
    orientation = 'portrait',
    customSettings
  } = options;

  // استخدام الإعدادات المخصصة أو الافتراضية
  const layout = customSettings || settings.reportLayout;
  
  if (!layout) {
    throw new Error('لا توجد إعدادات تخطيط متاحة');
  }

  // إنشاء PDF جديد
  const doc = new jsPDF({
    orientation: layout.page.orientation,
    unit: 'pt',
    format: 'a4'
  });

  // تمكين RTL
  doc.setR2L(true);

  let currentY = layout.page.marginTop;

  // إضافة الشعار
  if (settings.logoUrl && layout.logo) {
    try {
      const logoImg = new Image();
      logoImg.src = settings.logoUrl;
      
      let logoX = layout.page.marginLeft;
      if (layout.logo.position === 'center') {
        logoX = (doc.internal.pageSize.width - layout.logo.size) / 2;
      } else if (layout.logo.position === 'right') {
        logoX = doc.internal.pageSize.width - layout.page.marginRight - layout.logo.size;
      }

      doc.addImage(
        logoImg,
        'PNG',
        logoX,
        currentY + layout.logo.marginTop,
        layout.logo.size,
        layout.logo.size
      );
      
      currentY += layout.logo.marginTop + layout.logo.size + layout.logo.marginBottom;
    } catch (error) {
      console.warn('تعذر إضافة الشعار:', error);
    }
  }

  // إضافة العنوان
  if (layout.title) {
    doc.setFontSize(layout.title.fontSize);
    doc.setFont('helvetica', layout.title.fontWeight === 'bold' ? 'bold' : 'normal');
    
    const titleWidth = doc.getTextWidth(title);
    let titleX = layout.page.marginLeft;
    
    if (layout.title.position === 'center') {
      titleX = (doc.internal.pageSize.width - titleWidth) / 2;
    } else if (layout.title.position === 'right') {
      titleX = doc.internal.pageSize.width - layout.page.marginRight - titleWidth;
    }

    currentY += layout.title.marginTop;
    doc.text(title, titleX, currentY);
    currentY += layout.title.marginBottom;
  }

  // إضافة معلومات الشركة
  if (layout.companyInfo) {
    doc.setFontSize(layout.companyInfo.fontSize);
    doc.setFont('helvetica', layout.companyInfo.fontWeight === 'bold' ? 'bold' : 'normal');
    
    currentY += layout.companyInfo.marginTop;
    
    const companyLines: string[] = [];
    
    if (layout.companyInfo.showArabic) {
      companyLines.push(settings.companyNameAr);
      companyLines.push(settings.addressAr);
      companyLines.push(`${settings.phone} | ${settings.email}`);
    }
    
    if (layout.companyInfo.showEnglish) {
      companyLines.push(settings.companyNameEn);
      companyLines.push(settings.addressEn);
      if (!layout.companyInfo.showArabic) {
        companyLines.push(`${settings.phone} | ${settings.email}`);
      }
    }

    companyLines.forEach((line, index) => {
      if (line) {
        const lineWidth = doc.getTextWidth(line);
        let lineX = layout.page.marginLeft;
        
        if (layout.companyInfo.position === 'center') {
          lineX = (doc.internal.pageSize.width - lineWidth) / 2;
        } else if (layout.companyInfo.position === 'right') {
          lineX = doc.internal.pageSize.width - layout.page.marginRight - lineWidth;
        }

        doc.text(line, lineX, currentY + (index * (layout.companyInfo.fontSize + 2)));
      }
    });
    
    currentY += (companyLines.length * (layout.companyInfo.fontSize + 2)) + layout.companyInfo.marginBottom;
  }

  // إضافة مسافة قبل الجدول
  currentY += 20;

  // إضافة الجدول
  if (layout.table && tableHeaders.length > 0 && tableData.length > 0) {
    autoTable(doc, {
      head: [tableHeaders],
      body: tableData,
      startY: currentY,
      theme: 'grid',
      styles: {
        fontSize: layout.table.fontSize,
        cellPadding: layout.table.cellPadding,
        halign: 'right',
        font: 'helvetica'
      },
      headStyles: {
        fillColor: layout.table.headerBackgroundColor,
        textColor: layout.table.headerTextColor,
        halign: 'center',
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: layout.table.rowAlternateColor
      },
      tableLineColor: layout.table.borderColor,
      tableLineWidth: 0.5,
      didDrawPage: (data) => {
        // إضافة التذييل في كل صفحة
        if (layout.footer) {
          const pageNumber = doc.internal.pages.length - 1;
          const pageCount = doc.internal.pages.length - 1;
          
          doc.setFontSize(layout.footer.fontSize);
          doc.setFont('helvetica', 'normal');
          
          const footerY = doc.internal.pageSize.height - layout.page.marginBottom - 10;
          
          const footerElements: string[] = [];
          
          if (layout.footer.showDate) {
            footerElements.push(`تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}`);
          }
          
          if (layout.footer.showPageNumbers) {
            footerElements.push(`صفحة ${pageNumber} من ${pageCount}`);
          }
          
          if (settings.footerTextAr) {
            footerElements.push(settings.footerTextAr);
          }

          const footerText = footerElements.join(' | ');
          const footerWidth = doc.getTextWidth(footerText);
          
          let footerX = layout.page.marginLeft;
          if (layout.footer.position === 'center') {
            footerX = (doc.internal.pageSize.width - footerWidth) / 2;
          } else if (layout.footer.position === 'right') {
            footerX = doc.internal.pageSize.width - layout.page.marginRight - footerWidth;
          }

          doc.text(footerText, footerX, footerY);
        }
      }
    });
  }

  return doc;
}

// دالة مساعدة لتطبيق التخطيط على أي PDF موجود
export function applyLayoutToPDF(
  doc: jsPDF,
  settings: SystemSettings,
  title: string,
  customLayout?: SystemSettings['reportLayout']
): void {
  const layout = customLayout || settings.reportLayout;
  if (!layout) return;

  // إضافة الرأس والتذييل حسب التخطيط المحدد
  // ... يمكن إضافة المزيد من الوظائف هنا حسب الحاجة
}

// دالة لتصدير التخطيط كـ JSON
export function exportLayoutAsJSON(layout: SystemSettings['reportLayout']): string {
  return JSON.stringify(layout, null, 2);
}

// دالة لاستيراد التخطيط من JSON
export function importLayoutFromJSON(jsonString: string): SystemSettings['reportLayout'] | null {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('خطأ في استيراد التخطيط:', error);
    return null;
  }
}
