#!/usr/bin/env tsx

// Script لاختبار API أوامر التوريد
async function testSupplyOrderAPI() {
  console.log('🧪 اختبار API أوامر التوريد...');

  const baseUrl = 'http://localhost:9005';
  const token = Buffer.from('user:admin:admin').toString('base64');
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  try {
    // 1. اختبار جلب أوامر التوريد
    console.log('\n1️⃣ اختبار جلب أوامر التوريد...');
    
    const supplyResponse = await fetch(`${baseUrl}/api/supply`, {
      method: 'GET',
      headers
    });

    console.log(`📊 حالة الاستجابة: ${supplyResponse.status}`);
    
    if (supplyResponse.ok) {
      const supplyData = await supplyResponse.json();
      console.log(`✅ تم جلب ${supplyData.data?.length || supplyData.length || 0} أمر توريد`);
      
      if (supplyData.data && supplyData.data.length > 0) {
        console.log('\n📋 أول أمر توريد:');
        const firstOrder = supplyData.data[0];
        console.log(`- ID: ${firstOrder.id}`);
        console.log(`- رقم الأمر: ${firstOrder.supplyOrderId}`);
        console.log(`- التاريخ: ${firstOrder.supplyDate}`);
        console.log(`- المورد: ${firstOrder.supplierId}`);
        console.log(`- المخزن: ${firstOrder.warehouseId}`);
        console.log(`- عدد العناصر: ${firstOrder.items?.length || 0}`);
      }
    } else {
      const error = await supplyResponse.text();
      console.log(`❌ خطأ: ${error}`);
    }

    // 2. اختبار جلب الأجهزة
    console.log('\n2️⃣ اختبار جلب الأجهزة...');
    
    const devicesResponse = await fetch(`${baseUrl}/api/devices-simple`, {
      method: 'GET',
      headers
    });

    console.log(`📊 حالة الاستجابة: ${devicesResponse.status}`);
    
    if (devicesResponse.ok) {
      const devicesData = await devicesResponse.json();
      console.log(`✅ تم جلب ${devicesData.data?.length || devicesData.length || 0} جهاز`);
      
      if (devicesData.data && devicesData.data.length > 0) {
        console.log('\n📱 أول جهاز:');
        const firstDevice = devicesData.data[0];
        console.log(`- IMEI: ${firstDevice.id}`);
        console.log(`- الموديل: ${firstDevice.model}`);
        console.log(`- الحالة: ${firstDevice.status}`);
        console.log(`- المخزن: ${firstDevice.warehouseId}`);
        console.log(`- السعر: ${firstDevice.price}`);
      }
    } else {
      const error = await devicesResponse.text();
      console.log(`❌ خطأ: ${error}`);
    }

    // 3. اختبار إنشاء أمر توريد جديد
    console.log('\n3️⃣ اختبار إنشاء أمر توريد جديد...');
    
    const newSupplyOrder = {
      supplyOrderId: `SUP-API-TEST-${Date.now()}`,
      supplierId: 1,
      supplyDate: new Date().toISOString(),
      warehouseId: 1,
      employeeName: 'مستخدم اختبار API',
      notes: 'أمر توريد اختبار API',
      items: [
        {
          imei: `API-TEST-${Date.now()}`,
          model: 'iPhone 14 Pro',
          manufacturer: 'Apple',
          condition: 'جديد'
        }
      ]
    };

    const createSupplyResponse = await fetch(`${baseUrl}/api/supply`, {
      method: 'POST',
      headers,
      body: JSON.stringify(newSupplyOrder)
    });

    console.log(`📊 حالة الاستجابة: ${createSupplyResponse.status}`);
    
    if (createSupplyResponse.ok) {
      const createdOrder = await createSupplyResponse.json();
      console.log(`✅ تم إنشاء أمر توريد: ${createdOrder.supplyOrderId} (ID: ${createdOrder.id})`);
      
      // التحقق من إنشاء الجهاز
      const deviceCheckResponse = await fetch(`${baseUrl}/api/devices-simple`, {
        method: 'GET',
        headers
      });
      
      if (deviceCheckResponse.ok) {
        const updatedDevicesData = await deviceCheckResponse.json();
        const newDevice = updatedDevicesData.data?.find((d: any) => d.id === newSupplyOrder.items[0].imei);
        
        if (newDevice) {
          console.log(`✅ تم إنشاء الجهاز في المخزون: ${newDevice.id}`);
          console.log(`📍 في المخزن: ${newDevice.warehouseId}`);
          console.log(`📊 بحالة: ${newDevice.status}`);
        } else {
          console.log(`❌ لم يتم إنشاء الجهاز في المخزون`);
        }
      }
      
      // 4. اختبار حذف أمر التوريد
      console.log('\n4️⃣ اختبار حذف أمر التوريد...');
      
      const deleteSupplyResponse = await fetch(`${baseUrl}/api/supply/${createdOrder.id}`, {
        method: 'DELETE',
        headers
      });

      console.log(`📊 حالة الاستجابة: ${deleteSupplyResponse.status}`);
      
      if (deleteSupplyResponse.ok) {
        const deleteResult = await deleteSupplyResponse.json();
        console.log(`✅ تم حذف أمر التوريد بنجاح`);
        console.log(`📱 تم حذف ${deleteResult.deletedDevices || 0} جهاز`);
      } else {
        const error = await deleteSupplyResponse.text();
        console.log(`❌ خطأ في حذف أمر التوريد: ${error}`);
      }
      
    } else {
      const error = await createSupplyResponse.text();
      console.log(`❌ خطأ في إنشاء أمر التوريد: ${error}`);
    }

    // 5. اختبار البحث في الأجهزة حسب المخزن
    console.log('\n5️⃣ اختبار البحث في الأجهزة حسب المخزن...');
    
    const devicesInWarehouse1Response = await fetch(`${baseUrl}/api/devices-simple?filters[warehouseId]=1`, {
      method: 'GET',
      headers
    });

    if (devicesInWarehouse1Response.ok) {
      const warehouseDevicesData = await devicesInWarehouse1Response.json();
      console.log(`✅ أجهزة في المخزن 1: ${warehouseDevicesData.data?.length || warehouseDevicesData.length || 0}`);
    } else {
      console.log(`❌ خطأ في البحث في أجهزة المخزن: ${await devicesInWarehouse1Response.text()}`);
    }

    // 6. اختبار البحث في الأجهزة حسب الحالة
    console.log('\n6️⃣ اختبار البحث في الأجهزة حسب الحالة...');
    
    const availableDevicesResponse = await fetch(`${baseUrl}/api/devices-simple?filters[status]=متاح للبيع`, {
      method: 'GET',
      headers
    });

    if (availableDevicesResponse.ok) {
      const availableDevicesData = await availableDevicesResponse.json();
      console.log(`✅ أجهزة متاحة للبيع: ${availableDevicesData.data?.length || availableDevicesData.length || 0}`);
    } else {
      console.log(`❌ خطأ في البحث في الأجهزة المتاحة: ${await availableDevicesResponse.text()}`);
    }

    console.log('\n✅ تم الانتهاء من اختبار API أوامر التوريد!');
    
    // تقرير النتائج
    console.log('\n📋 ملخص النتائج:');
    console.log('═'.repeat(50));
    console.log('🔍 API أوامر التوريد:');
    console.log('  - جلب الأوامر: متاح');
    console.log('  - إنشاء أمر جديد: متاح');
    console.log('  - حذف أمر: متاح');
    console.log('');
    console.log('📱 API الأجهزة:');
    console.log('  - جلب الأجهزة: متاح');
    console.log('  - البحث حسب المخزن: متاح');
    console.log('  - البحث حسب الحالة: متاح');
    console.log('');
    console.log('🔗 التكامل:');
    console.log('  - إنشاء الأجهزة عند التوريد: يحتاج فحص');
    console.log('  - حذف الأجهزة عند حذف الأمر: يحتاج فحص');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  }
}

testSupplyOrderAPI();
