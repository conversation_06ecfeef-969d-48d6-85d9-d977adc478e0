import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { connectionId, databaseName } = await request.json();

    if (!connectionId || !databaseName) {
      return NextResponse.json(
        { error: 'Connection ID and database name are required' },
        { status: 400 }
      );
    }

    // التحقق من صحة اسم قاعدة البيانات (منع SQL injection)
    if (!/^[a-zA-Z0-9_]+$/.test(databaseName)) {
      return NextResponse.json(
        { error: 'Invalid database name' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // الحصول على معلومات الاتصال
      const connection = await tx.databaseConnection.findUnique({
        where: { id: connectionId }
      });

      if (!connection) {
        throw new Error('Connection not found');
      }

      const oldDatabase = connection.database;

      // تحديث اتصال قاعدة البيانات للاتصال الجديد
      const updatedConnection = await tx.databaseConnection.update({
        where: { id: connectionId },
        data: {
          database: databaseName,
          updatedAt: new Date()
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Switched database connection from '${oldDatabase}' to '${databaseName}' for connection: ${connection.name}`,
        tableName: 'databaseConnection',
        recordId: connectionId.toString()
      });

      return {
        message: 'Database switched successfully',
        connection: connection.name,
        oldDatabase,
        newDatabase: databaseName
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Switch database error:', error);

    if (error instanceof Error && error.message === 'Connection not found') {
      return NextResponse.json({ error: 'Connection not found' }, { status: 404 });
    }

    return NextResponse.json(
      { error: 'Failed to switch database' },
      { status: 500 }
    );
  }
}

// GET للحصول على قائمة قواعد البيانات المتاحة لاتصال معين
export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get('connectionId');

    if (!connectionId) {
      return NextResponse.json(
        { error: 'Connection ID is required' },
        { status: 400 }
      );
    }

    // الحصول على قواعد البيانات المتاحة لهذا الاتصال
    const databases = await prisma.database.findMany({
      where: { connectionId: parseInt(connectionId) },
      select: {
        id: true,
        name: true,
        owner: true,
        template: true,
        encoding: true,
        createdAt: true
      },
      orderBy: { name: 'asc' }
    });

    return NextResponse.json({
      success: true,
      databases
    });
  } catch (error) {
    console.error('Failed to fetch databases:', error);
    return NextResponse.json(
      { error: 'Failed to fetch databases' },
      { status: 500 }
    );
  }
}
