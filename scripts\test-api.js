const { default: fetch } = require('node-fetch');

async function testAPI() {
  try {
    // Create auth token
    const token = Buffer.from('user:admin:admin').toString('base64');
    
    console.log('Testing API with token:', token);
    
    const response = await fetch('http://localhost:9005/api/devices?page=1&limit=10', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());
    
    if (response.ok) {
      const data = await response.json();
      console.log('API Response:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAPI();
