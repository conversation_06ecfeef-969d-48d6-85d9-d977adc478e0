'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from '@/components/ui/tabs';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Users, Building, Plus, Search } from 'lucide-react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';

interface Contact {
  id: number;
  name: string;
  phone: string;
  email: string;
}

export default function ClientsPage() {
  const {
    clients,
    suppliers,
    isLoading,
    loadEssentialData,
    addContact,
    reloadClients,
    reloadSuppliers
  } = useStore();

  const { toast } = useToast();

  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'clients' | 'suppliers'>('clients');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // تحميل البيانات الأولية
  useEffect(() => {
    loadEssentialData();
  }, []); // إزالة dependency لمنع التحميل المتكرر

  // تصفية العملاء
  const filteredClients = clients.filter(contact => 
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (contact.phone && contact.phone.includes(searchTerm)) ||
    (contact.email && contact.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // تصفية الموردين
  const filteredSuppliers = suppliers.filter(contact => 
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (contact.phone && contact.phone.includes(searchTerm)) ||
    (contact.email && contact.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // فتح نافذة الإضافة/التحرير
  const openDialog = (contact?: Contact) => {
    if (contact) {
      setEditingContact(contact);
      setFormData({
        name: contact.name,
        phone: contact.phone,
        email: contact.email
      });
    } else {
      setEditingContact(null);
      setFormData({ name: '', phone: '', email: '' });
    }
    setIsDialogOpen(true);
  };

  // إغلاق النافذة
  const closeDialog = () => {
    setIsDialogOpen(false);
    setEditingContact(null);
    setFormData({ name: '', phone: '', email: '' });
  };

  // حفظ البيانات
  const saveContact = async () => {
    // التحقق من الحقول المطلوبة - الاسم فقط مطلوب
    if (!formData.name.trim()) {
      toast({
        title: 'خطأ في البيانات',
        description: 'اسم العميل/المورد مطلوب',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const contactType = activeTab === 'clients' ? 'client' : 'supplier';

      if (editingContact) {
        // تحديث - سنضيف هذه الوظيفة لاحقاً
        toast({
          title: 'تنبيه',
          description: 'وظيفة التحديث ستضاف قريباً',
          variant: 'default',
        });
      } else {
        // إضافة جديد
        const result = await addContact(contactType, formData);
        if (result.success) {
          // إعادة تحميل البيانات
          if (contactType === 'client') {
            await reloadClients();
          } else {
            await reloadSuppliers();
          }
          toast({
            title: 'تم بنجاح',
            description: `تم إضافة ${contactType === 'client' ? 'العميل' : 'المورد'} بنجاح`,
            variant: 'default',
          });
          closeDialog();
        } else {
          toast({
            title: 'خطأ',
            description: result.error || 'فشل في إضافة البيانات. يرجى المحاولة مرة أخرى',
            variant: 'destructive',
          });
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء الحفظ. يرجى المحاولة مرة أخرى',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // حذف جهة اتصال
  const deleteContact = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه الجهة؟')) {
      // سنضيف وظيفة الحذف لاحقاً
      toast({
        title: 'تنبيه',
        description: 'وظيفة الحذف ستضاف قريباً',
        variant: 'default',
      });
    }
  };

  // عرض الجدول
  const renderTable = (data: Contact[]) => (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الاسم</TableHead>
            <TableHead>رقم الهاتف</TableHead>
            <TableHead>البريد الإلكتروني</TableHead>
            <TableHead>الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                لا توجد بيانات للعرض
              </TableCell>
            </TableRow>
          ) : (
            data.map((contact) => (
              <TableRow key={contact.id}>
                <TableCell className="font-medium">{contact.name}</TableCell>
                <TableCell>{contact.phone}</TableCell>
                <TableCell>{contact.email}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openDialog(contact)}
                    >
                      تحرير
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => deleteContact(contact.id)}
                    >
                      حذف
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="py-8">
            <LoadingSpinner />
            <p className="text-center mt-4 text-muted-foreground">جار تحميل البيانات...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">العملاء والموردين</h1>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'clients' | 'suppliers')}
        className="space-y-4"
      >
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="clients" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              العملاء ({filteredClients.length})
            </TabsTrigger>
            <TabsTrigger value="suppliers" className="flex items-center gap-2">
              <Building className="w-4 h-4" />
              الموردين ({filteredSuppliers.length})
            </TabsTrigger>
          </TabsList>

          <Button onClick={() => openDialog()} className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            إضافة {activeTab === 'clients' ? 'عميل' : 'مورد'} جديد
          </Button>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="البحث في الأسماء أو الهواتف أو الإيميلات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <TabsContent value="clients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                قائمة العملاء
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderTable(filteredClients)}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="suppliers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="w-5 h-5" />
                قائمة الموردين
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderTable(filteredSuppliers)}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* نافذة الإضافة/التحرير */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingContact ? 'تحرير' : 'إضافة'} {activeTab === 'clients' ? 'عميل' : 'مورد'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">الاسم *</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="أدخل اسم الشركة أو الشخص"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">رقم الهاتف (اختياري)</label>
              <Input
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="05xxxxxxxx"
              />
            </div>

            <div>
              <label className="text-sm font-medium">البريد الإلكتروني (اختياري)</label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeDialog}>
              إلغاء
            </Button>
            <Button onClick={saveContact} disabled={isSubmitting}>
              {isSubmitting ? 'جار الحفظ...' : (editingContact ? 'تحديث' : 'إضافة')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}