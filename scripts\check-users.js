const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('Checking existing users...');
    
    const users = await prisma.user.findMany();
    console.log('Found users:', users);
    
    const devices = await prisma.device.findMany();
    console.log('Found devices:', devices.length);
    
  } catch (error) {
    console.error('Error checking users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
