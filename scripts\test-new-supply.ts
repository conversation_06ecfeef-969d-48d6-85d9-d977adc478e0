import { PrismaClient } from '@prisma/client';
import { generateUniqueId } from '../lib/transaction-utils';

const prisma = new PrismaClient();

async function testSupplyGeneration() {
  try {
    console.log('اختبار توليد رقم أمر التوريد...');

    // اختبار توليد رقم جديد
    const newSupplyNumber = await generateUniqueId('supplyOrder', 'SUP-');
    console.log(`رقم أمر التوريد الجديد: ${newSupplyNumber}`);

    // اختبار إنشاء أمر تجريبي
    const testOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: newSupplyNumber,
        supplierId: 1,
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'موظف تجريبي',
        notes: 'أمر تجريبي للاختبار'
      }
    });

    console.log(`تم إنشاء أمر تجريبي: ${testOrder.supplyOrderId}`);

    // اختبار توليد رقم آخر
    const nextSupplyNumber = await generateUniqueId('supplyOrder', 'SUP-');
    console.log(`الرقم التالي: ${nextSupplyNumber}`);

    // حذف الأمر التجريبي
    await prisma.supplyOrder.delete({
      where: { id: testOrder.id }
    });
    console.log('تم حذف الأمر التجريبي');

    // عرض جميع الأوامر الحالية
    const allOrders = await prisma.supplyOrder.findMany({
      select: { supplyOrderId: true }
    });
    console.log('جميع أوامر التوريد الحالية:');
    allOrders.forEach(order => console.log(`- ${order.supplyOrderId}`));

  } catch (error) {
    console.error('خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSupplyGeneration();
