import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    console.log('🗑️ محاولة حذف أمر التوريد');
    console.log('المعرف المستلم:', params.id);
    console.log('نوع المعرف:', typeof params.id);
    console.log('طول المعرف:', params.id.length);

    // تحسين التحقق من صحة المعرف
    const id = parseInt(params.id, 10);
    
    console.log('المعرف بعد تحويله لرقم:', id);
    console.log('نوع المعرف المحول:', typeof id);
    console.log('هل المعرف صحيح؟', !isNaN(id) && id > 0);
    
    if (isNaN(id) || id <= 0) {
      console.error('❌ معرف أمر التوريد غير صحيح:', params.id);
      return NextResponse.json({ 
        error: 'معرف أمر التوريد غير صحيح',
        receivedId: params.id,
        parsedId: id
      }, { status: 400 });
    }

    console.log('✅ معرف أمر التوريد صحيح:', id);

    const result = await executeInTransaction(async (tx) => {
      console.log('🔍 البحث عن أمر التوريد في قاعدة البيانات...');
      
      const existingOrder = await tx.supplyOrder.findUnique({
        where: { id },
        include: { items: true },
      });

      console.log('📋 نتيجة البحث:', existingOrder ? 'تم العثور على الأمر' : 'لم يتم العثور على الأمر');
      
      if (existingOrder) {
        console.log('تفاصيل الأمر الموجود:', {
          id: existingOrder.id,
          supplyOrderId: existingOrder.supplyOrderId,
          itemsCount: existingOrder.items?.length || 0
        });
      }

      if (!existingOrder) {
        console.error('❌ أمر التوريد غير موجود في قاعدة البيانات');
        throw new Error('Supply order not found');
      }

      const items = existingOrder.items || [];
      const deviceIMEIs = items.map((item) => item.imei).filter(Boolean);

      if (deviceIMEIs.length > 0) {
        // Check for related operations (sales, maintenance, etc.)
        const salesCount = await tx.sale.count({
          where: {
            items: { some: { deviceId: { in: deviceIMEIs } } },
            createdAt: { gte: existingOrder.createdAt },
          },
        });

        const maintenanceCount = await tx.maintenanceOrder.count({
          where: {
            items: { some: { deviceId: { in: deviceIMEIs } } },
            createdAt: { gte: existingOrder.createdAt },
          },
        });

        const returnsCount = await tx.return.count({
          where: {
            items: { some: { deviceId: { in: deviceIMEIs } } },
            createdAt: { gte: existingOrder.createdAt },
          },
        });

        const evaluationsCount = await tx.evaluationOrder.count({
          where: {
            items: { some: { deviceId: { in: deviceIMEIs } } },
            createdAt: { gte: existingOrder.createdAt },
          },
        });

        // Note: warehouseTransfer table doesn't exist in current schema
        // const transfersCount = await tx.warehouseTransfer.count({
        //   where: {
        //     deviceIds: { hasSome: deviceIMEIs },
        //     createdAt: { gte: existingOrder.createdAt },
        //   },
        // });
        const transfersCount = 0; // Placeholder until warehouseTransfer is implemented

        const totalRelatedOperations = salesCount + maintenanceCount + returnsCount + evaluationsCount + transfersCount;

        if (totalRelatedOperations > 0) {
          const relatedOperations = [];
          if (salesCount > 0) relatedOperations.push(`${salesCount} عملية بيع`);
          if (maintenanceCount > 0) relatedOperations.push(`${maintenanceCount} أمر صيانة`);
          if (returnsCount > 0) relatedOperations.push(`${returnsCount} عملية إرجاع`);
          if (evaluationsCount > 0) relatedOperations.push(`${evaluationsCount} أمر تقييم`);
          if (transfersCount > 0) relatedOperations.push(`${transfersCount} عملية نقل مخزني`);

          throw new Error(`لا يمكن حذف أمر التوريد لأن الأجهزة الموجودة فيه مرتبطة بعمليات لاحقة: ${relatedOperations.join(', ')}`);
        }
      }

      // Delete devices that were only part of this supply order
      if (deviceIMEIs.length > 0) {
        await tx.device.deleteMany({
          where: { id: { in: deviceIMEIs } },
        });
      }

      // Delete the order itself
      await tx.supplyOrder.delete({
        where: { id },
      });

      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted supply order: ${existingOrder.supplyOrderId} and ${deviceIMEIs.length} devices`,
      });

      return {
        message: 'Supply order deleted successfully',
        deletedDevices: deviceIMEIs.length,
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete supply order:', error);
    if (error instanceof Error) {
        if (error.message === 'Supply order not found') {
            return NextResponse.json({ error: 'Supply order not found' }, { status: 404 });
        }
        if (error.message.includes('لا يمكن حذف أمر التوريد')) {
            return NextResponse.json({ error: error.message }, { status: 400 });
        }
    }
    return NextResponse.json({ error: 'Failed to delete supply order' }, { status: 500 });
  }
}
