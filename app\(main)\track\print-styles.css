/* تنسيقات خاصة بالطباعة لصفحة تتبع الجهاز */

@media print {
  /* إخفاء العناصر غير المطلوبة */
  .no-print,
  button,
  .btn,
  nav,
  .navigation,
  .sidebar,
  .header-actions {
    display: none !important;
  }

  /* تنسيق عام للطباعة */
  body {
    font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: white;
    direction: rtl;
  }

  /* تنسيق الصفحة */
  @page {
    size: A4;
    margin: 15mm;
  }

  /* تنسيق العناوين */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
    font-weight: bold;
    color: #000;
    page-break-after: avoid;
  }

  h1 {
    font-size: 18pt;
    margin-bottom: 12pt;
  }

  h2 {
    font-size: 16pt;
    margin-bottom: 10pt;
  }

  h3 {
    font-size: 14pt;
    margin-bottom: 8pt;
  }

  /* تنسيق البطاقات */
  .card {
    border: 1px solid #ccc;
    margin-bottom: 15pt;
    page-break-inside: avoid;
    background: white;
  }

  .card-header {
    background: #f5f5f5;
    padding: 8pt;
    border-bottom: 1px solid #ccc;
  }

  .card-content {
    padding: 8pt;
  }

  .card-title {
    font-size: 14pt;
    font-weight: bold;
    margin: 0;
  }

  .card-description {
    font-size: 10pt;
    color: #666;
    margin-top: 2pt;
  }

  /* تنسيق التايم لاين */
  .timeline-container {
    margin: 0;
    padding: 0;
  }

  .timeline-event {
    margin-bottom: 12pt;
    padding: 8pt;
    border-right: 3px solid #007bff;
    background: #f8f9fa;
    page-break-inside: avoid;
  }

  .timeline-title {
    font-weight: bold;
    font-size: 12pt;
    margin-bottom: 4pt;
  }

  .timeline-date {
    font-size: 9pt;
    color: #666;
  }

  /* تنسيق الجداول */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 12pt;
    page-break-inside: auto;
  }

  th, td {
    border: 1px solid #ccc;
    padding: 6pt;
    text-align: right;
    vertical-align: top;
  }

  th {
    background: #f0f0f0;
    font-weight: bold;
    font-size: 11pt;
  }

  td {
    font-size: 10pt;
  }

  /* تنسيق النصوص */
  p {
    margin-bottom: 6pt;
    line-height: 1.4;
  }

  strong {
    font-weight: bold;
  }

  /* تنسيق الشبكة */
  .grid {
    display: block;
  }

  .grid > div {
    margin-bottom: 6pt;
  }

  /* تنسيق المسافات */
  .space-y-2 > * + * {
    margin-top: 6pt;
  }

  .space-y-4 > * + * {
    margin-top: 12pt;
  }

  .space-y-6 > * + * {
    margin-top: 18pt;
  }

  /* تنسيق الألوان للطباعة */
  .text-primary {
    color: #000 !important;
  }

  .text-muted-foreground {
    color: #666 !important;
  }

  .bg-primary {
    background: #f0f0f0 !important;
    color: #000 !important;
  }

  /* تنسيق خاص بمعلومات الضمان */
  .warranty-info {
    background: #f9f9f9;
    border: 1px solid #ddd;
    padding: 8pt;
  }

  /* تنسيق الأيقونات - إخفاؤها في الطباعة */
  .lucide,
  svg {
    display: none;
  }

  /* تنسيق خاص بنسخة العميل */
  .customer-view-container {
    margin: 0;
    padding: 0;
  }

  .customer-view-container .card {
    margin-bottom: 12pt;
  }

  /* تجنب تقسيم العناصر المهمة */
  .print-section {
    page-break-inside: avoid;
  }

  /* تنسيق التاريخ والوقت */
  .print-timestamp {
    font-size: 9pt;
    color: #666;
    text-align: center;
    margin-top: 12pt;
    border-top: 1px solid #ccc;
    padding-top: 6pt;
  }
}

/* تنسيقات عامة لتحسين العرض */
.print-ready {
  font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
}

.print-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.print-subtitle {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-bottom: 30px;
}

/* تحسينات للخطوط العربية */
.arabic-text {
  font-family: 'Cairo', 'Noto Sans Arabic', 'Amiri', Arial, sans-serif !important;
  direction: rtl;
  text-align: right;
}

/* تنسيق خاص للعناصر المهمة */
.highlight-print {
  background: #ffffcc !important;
  border: 1px solid #ffcc00 !important;
  padding: 8pt !important;
}

@media print {
  .highlight-print {
    background: #f0f0f0 !important;
    border: 1px solid #ccc !important;
  }
}
