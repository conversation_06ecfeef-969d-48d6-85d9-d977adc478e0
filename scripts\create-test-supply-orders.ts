import { PrismaClient } from '@prisma/client';
import { generateUniqueId } from '../lib/transaction-utils';

const prisma = new PrismaClient();

async function createTestSupplyOrders() {
  try {
    console.log('إنشاء أوامر توريد تجريبية...');

    // إنشاء أمر توريد أول
    const supplyNumber1 = await generateUniqueId('supplyOrder', 'SUP-');
    const order1 = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: supplyNumber1,
        supplierId: 1,
        invoiceNumber: 'INV-001',
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'System Administrator',
        notes: 'أمر توريد تجريبي 1',
        status: 'completed'
      }
    });

    console.log(`تم إنشاء الأمر الأول: ${order1.supplyOrderId}`);

    // إنشاء بعض العناصر للأمر الأول
    await prisma.supplyOrderItem.create({
      data: {
        supplyOrderId: order1.id,
        imei: '123456789012345',
        model: 'iPhone 14',
        manufacturer: 'Apple',
        condition: 'جديد'
      }
    });

    // إنشاء أمر توريد ثاني
    const supplyNumber2 = await generateUniqueId('supplyOrder', 'SUP-');
    const order2 = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: supplyNumber2,
        supplierId: 1,
        invoiceNumber: 'INV-002',
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'System Administrator',
        notes: 'أمر توريد تجريبي 2',
        status: 'completed'
      }
    });

    console.log(`تم إنشاء الأمر الثاني: ${order2.supplyOrderId}`);

    // إنشاء بعض العناصر للأمر الثاني
    await prisma.supplyOrderItem.create({
      data: {
        supplyOrderId: order2.id,
        imei: '123456789012346',
        model: 'Samsung Galaxy S23',
        manufacturer: 'Samsung',
        condition: 'جديد'
      }
    });

    // عرض الأوامر المُنشأة
    const allOrders = await prisma.supplyOrder.findMany({
      include: { items: true },
      orderBy: { id: 'asc' }
    });

    console.log('\nجميع أوامر التوريد:');
    allOrders.forEach(order => {
      console.log(`- ${order.supplyOrderId} (ID: ${order.id}) - ${order.items.length} عنصر`);
    });

  } catch (error) {
    console.error('خطأ في إنشاء أوامر توريد تجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestSupplyOrders();
