// اختبار تحميل البيانات من الواجهة
async function testDataLoading() {
  try {
    console.log('🧪 اختبار تحميل البيانات من الواجهة...\n');
    
    const devToken = btoa('user:admin:admin');
    
    // اختبار 1: تحميل المخازن (بدون تفويض)
    console.log('1️⃣ اختبار تحميل المخازن...');
    const warehousesResponse = await fetch('http://localhost:9005/api/warehouses-simple?limit=100');
    
    if (warehousesResponse.ok) {
      const warehousesData = await warehousesResponse.json();
      console.log(`✅ المخازن: ${warehousesData.data?.length || 0} مخزن`);
      warehousesData.data?.slice(0, 3).forEach((w, i) => {
        console.log(`   ${i+1}. ${w.name} (ID: ${w.id})`);
      });
    } else {
      console.log(`❌ خطأ في تحميل المخازن: ${warehousesResponse.status}`);
    }
    
    // اختبار 2: تحميل العملاء (مع تفويض)
    console.log('\n2️⃣ اختبار تحميل العملاء...');
    const clientsResponse = await fetch('http://localhost:9005/api/clients?limit=100', {
      headers: {
        'Authorization': `Bearer ${devToken}`
      }
    });
    
    if (clientsResponse.ok) {
      const clientsData = await clientsResponse.json();
      console.log(`✅ العملاء: ${clientsData.data?.length || 0} عميل`);
      clientsData.data?.slice(0, 3).forEach((c, i) => {
        console.log(`   ${i+1}. ${c.name} (ID: ${c.id})`);
      });
    } else {
      console.log(`❌ خطأ في تحميل العملاء: ${clientsResponse.status}`);
    }
    
    // اختبار 3: تحميل الموردين (مع تفويض)
    console.log('\n3️⃣ اختبار تحميل الموردين...');
    const suppliersResponse = await fetch('http://localhost:9005/api/suppliers?limit=100', {
      headers: {
        'Authorization': `Bearer ${devToken}`
      }
    });
    
    if (suppliersResponse.ok) {
      const suppliersData = await suppliersResponse.json();
      console.log(`✅ الموردين: ${suppliersData.data?.length || 0} مورد`);
      suppliersData.data?.slice(0, 3).forEach((s, i) => {
        console.log(`   ${i+1}. ${s.name} (ID: ${s.id})`);
      });
    } else {
      console.log(`❌ خطأ في تحميل الموردين: ${suppliersResponse.status}`);
    }
    
    // اختبار 4: إضافة عميل جديد
    console.log('\n4️⃣ اختبار إضافة عميل جديد...');
    const newClientResponse = await fetch('http://localhost:9005/api/clients', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${devToken}`
      },
      body: JSON.stringify({
        name: `عميل اختبار ${Date.now()}`,
        phone: '0555666777',
        email: `test${Date.now()}@example.com`
      })
    });
    
    if (newClientResponse.ok) {
      const newClient = await newClientResponse.json();
      console.log(`✅ تم إنشاء عميل جديد: ${newClient.name} (ID: ${newClient.id})`);
      
      // التحقق من الإضافة
      console.log('\n5️⃣ التحقق من إضافة العميل...');
      const checkResponse = await fetch('http://localhost:9005/api/clients?limit=100', {
        headers: { 'Authorization': `Bearer ${devToken}` }
      });
      
      if (checkResponse.ok) {
        const checkData = await checkResponse.json();
        console.log(`✅ العدد الجديد للعملاء: ${checkData.data?.length || 0}`);
        
        // البحث عن العميل الجديد
        const foundClient = checkData.data?.find(c => c.id === newClient.id);
        if (foundClient) {
          console.log(`✅ تم العثور على العميل الجديد في القائمة: ${foundClient.name}`);
        } else {
          console.log('❌ لم يتم العثور على العميل الجديد في القائمة');
        }
      }
    } else {
      const errorText = await newClientResponse.text();
      console.log(`❌ خطأ في إضافة العميل: ${newClientResponse.status} - ${errorText}`);
    }
    
    console.log('\n📊 ملخص الاختبار:');
    console.log('✅ إذا رأيت هذه الرسالة فإن جميع APIs تعمل بشكل صحيح');
    console.log('💡 المشكلة إذن في الواجهة (store.tsx) وليس في قاعدة البيانات');
    
  } catch (error) {
    console.error('❌ خطأ عام في الاختبار:', error.message);
  }
}

testDataLoading();
