#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkSupplyOrderIssues() {
  try {
    console.log('🔍 فحص مشاكل أوامر التوريد والأجهزة...');

    // 1. فحص جدول أوامر التوريد
    console.log('\n📋 فحص جدول أوامر التوريد...');
    
    try {
      const supplyOrders = await prisma.supplyOrder.findMany({
        include: {
          items: true
        },
        orderBy: { id: 'desc' }
      });
      
      console.log(`✅ تم العثور على ${supplyOrders.length} أمر توريد`);
      
      if (supplyOrders.length > 0) {
        console.log('\n📋 أوامر التوريد:');
        supplyOrders.forEach(order => {
          console.log(`- ID: ${order.id}, رقم الأمر: ${order.supplyOrderId}, التاريخ: ${order.supplyDate}, عدد الأجهزة: ${order.items.length}`);
        });
      }
    } catch (error) {
      console.error('❌ خطأ في الوصول إلى جدول أوامر التوريد:', error);
    }

    // 2. فحص جدول الأجهزة
    console.log('\n📱 فحص جدول الأجهزة...');
    
    try {
      const devices = await prisma.device.findMany({
        orderBy: { dateAdded: 'desc' }
      });
      
      console.log(`✅ تم العثور على ${devices.length} جهاز في قاعدة البيانات`);
      
      if (devices.length > 0) {
        console.log('\n📱 عينة من الأجهزة:');
        devices.slice(0, 5).forEach(device => {
          console.log(`- IMEI: ${device.id}, الموديل: ${device.model}, الحالة: ${device.status}, المخزن: ${device.warehouseId}, التاريخ: ${device.dateAdded.toISOString().split('T')[0]}`);
        });
        
        if (devices.length > 5) {
          console.log(`... و ${devices.length - 5} جهاز آخر`);
        }
      }
    } catch (error) {
      console.error('❌ خطأ في الوصول إلى جدول الأجهزة:', error);
    }

    // 3. فحص العلاقة بين أوامر التوريد والأجهزة
    console.log('\n🔗 فحص العلاقة بين أوامر التوريد والأجهزة...');
    
    try {
      const supplyOrderItems = await prisma.supplyOrderItem.findMany({
        include: {
          supplyOrder: true
        }
      });
      
      console.log(`📦 تم العثور على ${supplyOrderItems.length} عنصر في أوامر التوريد`);
      
      if (supplyOrderItems.length > 0) {
        // فحص الأجهزة المسجلة في أوامر التوريد
        const deviceIMEIs = supplyOrderItems.map(item => item.imei);
        
        const devicesInSupply = await prisma.device.findMany({
          where: {
            id: { in: deviceIMEIs }
          }
        });
        
        console.log(`📱 من ${deviceIMEIs.length} جهاز في أوامر التوريد، ${devicesInSupply.length} موجود في جدول الأجهزة`);
        
        if (devicesInSupply.length < deviceIMEIs.length) {
          const missingDevices = deviceIMEIs.filter(imei => !devicesInSupply.find(d => d.id === imei));
          console.log(`⚠️ أجهزة مفقودة من جدول الأجهزة: ${missingDevices.length}`);
          missingDevices.slice(0, 5).forEach(imei => {
            console.log(`  - IMEI: ${imei}`);
          });
        }
      }
    } catch (error) {
      console.error('❌ خطأ في فحص العلاقة بين أوامر التوريد والأجهزة:', error);
    }

    // 4. فحص حالة الأجهزة
    console.log('\n📊 فحص حالة الأجهزة...');
    
    try {
      const deviceStatuses = await prisma.device.groupBy({
        by: ['status'],
        _count: {
          id: true
        }
      });
      
      console.log('📊 توزيع حالات الأجهزة:');
      deviceStatuses.forEach(status => {
        console.log(`- ${status.status}: ${status._count.id} جهاز`);
      });
    } catch (error) {
      console.error('❌ خطأ في فحص حالة الأجهزة:', error);
    }

    // 5. فحص توزيع الأجهزة على المخازن
    console.log('\n🏪 فحص توزيع الأجهزة على المخازن...');
    
    try {
      const devicesByWarehouse = await prisma.device.groupBy({
        by: ['warehouseId'],
        _count: {
          id: true
        }
      });
      
      console.log('🏪 توزيع الأجهزة على المخازن:');
      for (const warehouse of devicesByWarehouse) {
        if (warehouse.warehouseId) {
          const warehouseInfo = await prisma.warehouse.findUnique({
            where: { id: warehouse.warehouseId }
          });
          console.log(`- ${warehouseInfo?.name || `مخزن ${warehouse.warehouseId}`}: ${warehouse._count.id} جهاز`);
        } else {
          console.log(`- بدون مخزن: ${warehouse._count.id} جهاز`);
        }
      }
    } catch (error) {
      console.error('❌ خطأ في فحص توزيع الأجهزة على المخازن:', error);
    }

    // 6. اختبار إنشاء أمر توريد جديد
    console.log('\n🧪 اختبار إنشاء أمر توريد جديد...');
    
    try {
      const testSupplyOrder = await prisma.supplyOrder.create({
        data: {
          supplyOrderId: `SUP-TEST-${Date.now()}`,
          supplierId: 1,
          supplyDate: new Date().toISOString(),
          warehouseId: 1,
          employeeName: 'مستخدم اختبار',
          notes: 'أمر توريد اختبار'
        }
      });
      
      console.log(`✅ تم إنشاء أمر توريد اختبار: ${testSupplyOrder.supplyOrderId} (ID: ${testSupplyOrder.id})`);
      
      // إضافة عناصر اختبار
      const testItem = await prisma.supplyOrderItem.create({
        data: {
          supplyOrderId: testSupplyOrder.id,
          imei: `TEST-${Date.now()}`,
          model: 'موديل اختبار',
          manufacturer: 'شركة اختبار',
          condition: 'جديد'
        }
      });
      
      console.log(`✅ تم إضافة عنصر اختبار: IMEI ${testItem.imei}`);
      
      // إضافة الجهاز إلى جدول الأجهزة
      const testDevice = await prisma.device.create({
        data: {
          id: testItem.imei,
          model: testItem.model,
          status: 'متاح للبيع',
          storage: '128GB',
          price: 1000,
          condition: testItem.condition,
          warehouseId: testSupplyOrder.warehouseId,
          supplierId: testSupplyOrder.supplierId
        }
      });
      
      console.log(`✅ تم إضافة الجهاز إلى المخزون: ${testDevice.id}`);
      
      // حذف البيانات الاختبارية
      await prisma.device.delete({
        where: { id: testDevice.id }
      });
      
      await prisma.supplyOrderItem.delete({
        where: { id: testItem.id }
      });
      
      await prisma.supplyOrder.delete({
        where: { id: testSupplyOrder.id }
      });
      
      console.log('✅ تم حذف البيانات الاختبارية بنجاح');
      
    } catch (error) {
      console.error('❌ خطأ في اختبار إنشاء أمر التوريد:', error);
    }

    // 7. اختبار حذف أمر توريد موجود
    console.log('\n🗑️ اختبار آلية حذف أوامر التوريد...');
    
    try {
      // البحث عن أمر توريد موجود
      const existingOrder = await prisma.supplyOrder.findFirst({
        include: { items: true }
      });
      
      if (existingOrder) {
        console.log(`📋 تم العثور على أمر توريد: ${existingOrder.supplyOrderId} (ID: ${existingOrder.id})`);
        console.log(`📦 يحتوي على ${existingOrder.items.length} عنصر`);
        
        // فحص الأجهزة المرتبطة
        const relatedDevices = await prisma.device.findMany({
          where: {
            id: { in: existingOrder.items.map(item => item.imei) }
          }
        });
        
        console.log(`📱 الأجهزة المرتبطة: ${relatedDevices.length} من ${existingOrder.items.length}`);
        
        if (relatedDevices.length > 0) {
          console.log('📊 حالات الأجهزة المرتبطة:');
          const statusCounts = relatedDevices.reduce((acc, device) => {
            acc[device.status] = (acc[device.status] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);
          
          Object.entries(statusCounts).forEach(([status, count]) => {
            console.log(`  - ${status}: ${count} جهاز`);
          });
        }
        
      } else {
        console.log('❌ لم يتم العثور على أوامر توريد موجودة للاختبار');
      }
      
    } catch (error) {
      console.error('❌ خطأ في اختبار آلية حذف أوامر التوريد:', error);
    }

    console.log('\n✅ تم الانتهاء من فحص مشاكل أوامر التوريد والأجهزة!');
    
    // 8. التوصيات
    console.log('\n💡 التوصيات:');
    console.log('1. تأكد من أن API أوامر التوريد يعمل بشكل صحيح');
    console.log('2. تحقق من إنشاء الأجهزة عند إضافة أمر توريد جديد');
    console.log('3. تأكد من ربط الأجهزة بالمخازن بشكل صحيح');
    console.log('4. فحص آلية حذف أوامر التوريد والأجهزة المرتبطة');

  } catch (error) {
    console.error('❌ خطأ عام في فحص مشاكل أوامر التوريد:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSupplyOrderIssues();
