const { default: fetch } = require('node-fetch');

async function testWarehousesAPI() {
  try {
    console.log('Testing Warehouses API...');
    
    // Test GET - fetch all warehouses
    console.log('\n1. Testing GET /api/warehouses-simple');
    const getResponse = await fetch('http://localhost:9005/api/warehouses-simple', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('GET Response status:', getResponse.status);
    
    if (getResponse.ok) {
      const result = await getResponse.json();
      if (result.data) {
        // Paginated response
        console.log('Warehouses found:', result.data.length);
        console.log('Total warehouses:', result.pagination.total);
        result.data.forEach(w => {
          console.log(`- ${w.name} (${w.type}) - ${w.location}`);
        });
      } else {
        // Legacy response (array)
        console.log('Warehouses found:', result.length);
        result.forEach(w => {
          console.log(`- ${w.name} (${w.type}) - ${w.location}`);
        });
      }
    } else {
      const errorText = await getResponse.text();
      console.log('GET Error response:', errorText);
    }
    
    // Test POST - create new warehouse
    console.log('\n2. Testing POST /api/warehouses-simple');
    const newWarehouse = {
      name: 'مخزن اختبار API',
      type: 'فرعي',
      location: 'موقع اختبار'
    };
    
    const postResponse = await fetch('http://localhost:9005/api/warehouses-simple', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newWarehouse)
    });
    
    console.log('POST Response status:', postResponse.status);
    
    if (postResponse.ok) {
      const createdWarehouse = await postResponse.json();
      console.log('Created warehouse:', createdWarehouse);
    } else {
      const errorText = await postResponse.text();
      console.log('POST Error response:', errorText);
    }
    
  } catch (error) {
    console.error('Error testing Warehouses API:', error);
  }
}

testWarehousesAPI();
