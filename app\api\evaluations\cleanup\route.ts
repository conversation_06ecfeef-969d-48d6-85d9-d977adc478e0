import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { action } = await request.json();

    if (action === 'list_duplicates') {
      // البحث عن أوامر التقييم المكررة
      const duplicates = await prisma.evaluationOrder.groupBy({
        by: ['orderId'],
        having: {
          orderId: {
            _count: {
              gt: 1
            }
          }
        },
        _count: {
          orderId: true
        }
      });

      const duplicateDetails = [];
      for (const duplicate of duplicates) {
        const orders = await prisma.evaluationOrder.findMany({
          where: { orderId: duplicate.orderId },
          orderBy: { id: 'asc' }
        });
        duplicateDetails.push({
          orderId: duplicate.orderId,
          count: duplicate._count.orderId,
          orders: orders
        });
      }

      return NextResponse.json({
        duplicates: duplicateDetails,
        totalDuplicateGroups: duplicates.length
      });
    }

    if (action === 'remove_duplicates') {
      // حذف أوامر التقييم المكررة (الاحتفاظ بالأحدث فقط)
      const result = await executeInTransaction(async (tx) => {
        const duplicates = await tx.evaluationOrder.groupBy({
          by: ['orderId'],
          having: {
            orderId: {
              _count: {
                gt: 1
              }
            }
          }
        });

        let deletedCount = 0;
        const deletedOrders = [];

        for (const duplicate of duplicates) {
          // الحصول على جميع الأوامر بنفس الرقم
          const orders = await tx.evaluationOrder.findMany({
            where: { orderId: duplicate.orderId },
            orderBy: { id: 'asc' }
          });

          // حذف جميع الأوامر عدا الأحدث (آخر واحد)
          const ordersToDelete = orders.slice(0, -1);
          
          for (const orderToDelete of ordersToDelete) {
            await tx.evaluationOrder.delete({
              where: { id: orderToDelete.id }
            });
            
            deletedOrders.push({
              id: orderToDelete.id,
              orderId: orderToDelete.orderId,
              date: orderToDelete.date
            });
            
            deletedCount++;
          }
        }

        // إنشاء audit log
        await createAuditLogInTransaction(tx, {
          userId: authResult.user!.id,
          username: authResult.user!.username,
          operation: 'CLEANUP',
          details: `Removed ${deletedCount} duplicate evaluation orders`,
          tableName: 'evaluationOrder',
          recordId: 'cleanup'
        });

        return {
          deletedCount,
          deletedOrders,
          duplicateGroups: duplicates.length
        };
      });

      return NextResponse.json({
        success: true,
        message: `تم حذف ${result.deletedCount} أمر تقييم مكرر من ${result.duplicateGroups} مجموعة`,
        details: result
      });
    }

    if (action === 'list_all') {
      // عرض جميع أوامر التقييم
      const orders = await prisma.evaluationOrder.findMany({
        orderBy: { id: 'desc' },
        select: {
          id: true,
          orderId: true,
          employeeName: true,
          date: true,
          createdAt: true,
          items: true
        }
      });

      const processedOrders = orders.map(order => ({
        ...order,
        itemsCount: order.items ? 
          (typeof order.items === 'string' ? 
            (Array.isArray(order.items) ? order.items.length : [].length) : 
            Array.isArray(order.items) ? (Array.isArray(order.items) ? order.items.length : [].length) : 0
          ) : 0
      }));

      return NextResponse.json({
        orders: processedOrders,
        total: orders.length
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Cleanup operation failed:', error);
    return NextResponse.json(
      { error: 'Cleanup operation failed', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { ids } = await request.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'IDs array is required' },
        { status: 400 }
      );
    }

    const result = await executeInTransaction(async (tx) => {
      // حذف الأوامر المحددة
      const deletedOrders = await tx.evaluationOrder.findMany({
        where: { id: { in: ids } },
        select: { id: true, orderId: true }
      });

      await tx.evaluationOrder.deleteMany({
        where: { id: { in: ids } }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted evaluation orders: ${deletedOrders.map(o => o.orderId).join(', ')}`,
        tableName: 'evaluationOrder',
        recordId: ids.join(',')
      });

      return {
        deletedCount: deletedOrders.length,
        deletedOrders
      };
    });

    return NextResponse.json({
      success: true,
      message: `تم حذف ${result.deletedCount} أمر تقييم`,
      details: result
    });

  } catch (error) {
    console.error('Delete operation failed:', error);
    return NextResponse.json(
      { error: 'Delete operation failed', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
