'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/sidebar';
import {
  LayoutDashboard,
  ScanLine,
  Users2,
  DatabaseZap,
  Package,
  Undo2,
  ShoppingCart,
  Warehouse,
  PackagePlus,
  Wrench,
  ClipboardCheck,
  ArrowRightLeft,
  Shuffle,
  Users,
  FileText,
  ClipboardList,
  Settings,
  MessageSquareQuote,
  Mail,
} from 'lucide-react';
import { useCanView } from '@/hooks/usePermission';
import { PermissionPageKey } from '@/lib/types';

const links = [
  {
    href: '/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard />,
    permissionKey: 'dashboard' as PermissionPageKey,
  },
  {
    href: '/track',
    label: 'تتبع الجهاز',
    icon: <ScanLine />,
    permissionKey: 'track' as PermissionPageKey,
  },
  {
    href: '/supply',
    label: 'أوامر التوريد',
    icon: <PackagePlus />,
    permissionKey: 'supply' as PermissionPageKey,
  },
  {
    href: '/accept-devices',
    label: 'قبول أجهزة',
    icon: <ScanLine />,
    permissionKey: 'acceptDevices' as PermissionPageKey,
  },
  {
    href: '/grading',
    label: 'الفحص والتقييم',
    icon: <ClipboardCheck />,
    permissionKey: 'grading' as PermissionPageKey,
  },
  {
    href: '/inventory',
    label: 'المخزون',
    icon: <Package />,
    permissionKey: 'inventory' as PermissionPageKey,
  },
  {
    href: '/sales',
    label: 'المبيعات',
    icon: <ShoppingCart />,
    permissionKey: 'sales' as PermissionPageKey,
  },
  {
    href: '/maintenance',
    label: 'الصيانة',
    icon: <Wrench />,
    permissionKey: 'maintenance' as PermissionPageKey,
  },
  {
    href: '/maintenance-transfer',
    label: 'استلام وتسليم الصيانة',
    icon: <ArrowRightLeft />,
    permissionKey: 'maintenanceTransfer' as PermissionPageKey,
  },
  {
    href: '/warehouse-transfer',
    label: 'التحويل المخزني',
    icon: <Shuffle />,
    permissionKey: 'warehouseTransfer' as PermissionPageKey,
  },
  {
    href: '/clients',
    label: 'العملاء والموردين',
    icon: <Users2 />,
    permissionKey: 'clients' as PermissionPageKey,
  },
  {
    href: '/pricing',
    label: 'أداة التسعير',
    icon: <DatabaseZap />,
    permissionKey: 'pricing' as PermissionPageKey,
  },
  {
    href: '/returns',
    label: 'المرتجعات',
    icon: <Undo2 />,
    permissionKey: 'returns' as PermissionPageKey,
  },
  {
    href: '/warehouses',
    label: 'إدارة المخازن',
    icon: <Warehouse />,
    permissionKey: 'warehouses' as PermissionPageKey,
  },
  {
    href: '/messaging',
    label: 'المراسلات',
    icon: <Mail />,
    permissionKey: 'messaging' as PermissionPageKey,
  },
  {
    href: '/users',
    label: 'إدارة المستخدمين',
    icon: <Users />,
    permissionKey: 'users' as PermissionPageKey,
  },
  {
    href: '/user-switcher',
    label: 'تبديل المستخدم',
    icon: <Users />,
    permissionKey: 'dashboard' as PermissionPageKey,
  },
  {
    href: '/reports',
    label: 'التقارير',
    icon: <FileText />,
    permissionKey: 'reports' as PermissionPageKey,
  },
  {
    href: '/stocktaking',
    label: 'الجرد',
    icon: <ClipboardList />,
    permissionKey: 'stocktaking' as PermissionPageKey,
  },
  {
    href: '/requests',
    label: 'طلبات الموظفين',
    icon: <MessageSquareQuote />,
    permissionKey: 'requests' as PermissionPageKey,
  },
  {
    href: '/settings',
    label: 'إعدادات النظام',
    icon: <Settings />,
    permissionKey: 'settings' as PermissionPageKey,
  },
];

// مكون عنصر التنقل الفردي مع فحص الصلاحية
function NavItem({ link, pathname }: { link: typeof links[0]; pathname: string }) {
  // نستخدم useCanView مع معالجة الأخطاء
  const { useStore } = require('@/context/store');
  const store = useStore();
  
  // للتأكد من وجود المستخدم وصلاحياته
  let canView = false;
  
  if (store && store.currentUser) {
    // التحقق من المستخدم الإداري
    const isAdmin = store.currentUser.username === 'admin' || 
                   store.currentUser.id === 1 || 
                   store.currentUser.role === 'admin' ||
                   store.currentUser.name === 'مدير النظام' || 
                   store.currentUser.name === 'مدير النظام 2' ||
                   store.currentUser.name?.includes('مدير');
    
    if (isAdmin) {
      canView = true; // المدير يمكنه الوصول لجميع الصفحات
    } else if (store.currentUser.permissions) {
      const permissions = store.currentUser.permissions[link.permissionKey];
      canView = permissions ? permissions.view : false;
    } else {
      // في حالة عدم وجود صلاحيات، اعرض لوحة التحكم فقط
      canView = link.permissionKey === 'dashboard';
    }
  } else {
    // في حالة عدم وجود مستخدم، اعرض لوحة التحكم فقط
    canView = link.permissionKey === 'dashboard';
  }

  // إخفاء العنصر إذا لم تكن لدى المستخدم صلاحية العرض
  if (!canView) return null;

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={
          pathname.startsWith(link.href) &&
          (link.href !== '/' || pathname === '/')
        }
        tooltip={link.label}
      >
        <Link href={link.href}>
          {link.icon}
          <span>{link.label}</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

export function MainNav() {
  const pathname = usePathname();

  return (
    <SidebarMenu>
      {links.map((link) => (
        <NavItem key={link.href} link={link} pathname={pathname} />
      ))}
    </SidebarMenu>
  );
}
