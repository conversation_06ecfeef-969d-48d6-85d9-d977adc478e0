// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// نموذج المستخدمين
model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  name            String?
  username        String?  @unique @default("user")
  role            String?  @default("user")
  phone           String?  @default("")
  photo           String?  @default("")
  status          String?  @default("Active")
  lastLogin       String?
  branchLocation  String?  // موقع الفرع
  warehouseAccess Json?    // صلاحيات الوصول للمخازن كـ JSON array
  permissions     Json?    // للصلاحيات المخزنة كـ JSON
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt @default(now())
  
  // العلاقات الأساسية
  posts                   Post[]
  createdDatabases        Database[]
  
  // العمليات الرئيسية
  supplyOrders           SupplyOrder[]
  supplyOrderDrafts      SupplyOrderDraft[]
  sales                  Sale[]
  returns                Return[]
  evaluationOrders       EvaluationOrder[]
  maintenanceOrders      MaintenanceOrder[]
  maintenanceReceipts    MaintenanceReceiptOrder[]
  deliveryOrders         DeliveryOrder[]
  stocktakeOperations    StocktakeOperation[]
  warehouseTransfers     WarehouseTransfer[]
  employeeRequests       EmployeeRequest[]
  
  // الرسائل والتواصل
  sentMessages          InternalMessage[] @relation("SentMessages")
  receivedMessages      InternalMessage[] @relation("ReceivedMessages")
  
  // العلاقات الإضافية
  processedReturns      Return[] @relation("ProcessedReturns")
  acknowledgedEvaluations EvaluationOrder[] @relation("AcknowledgedEvaluations")
  receivedTransfers     WarehouseTransfer[] @relation("ReceivedTransfers")
  assignedRequests      EmployeeRequest[] @relation("AssignedRequests")
  acknowledgedMaintenance MaintenanceLog[] @relation("AcknowledgedMaintenance")
  deviceMovements       DeviceMovement[]
  auditLogs             AuditLog[]

  @@map("users")
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  author    User    @relation(fields: [authorId], references: [id])
  authorId  Int
}

// إعدادات النظام
model SystemSetting {
  id              Int      @id @default(1)
  logoUrl         String   @default("")
  companyNameAr   String   @default("")
  companyNameEn   String   @default("")
  addressAr       String   @default("")
  addressEn       String   @default("")
  phone           String   @default("")
  email           String   @default("")
  website         String   @default("")
  footerTextAr    String   @default("")
  footerTextEn    String   @default("")
  updatedAt       DateTime @updatedAt @default(now())
  createdAt       DateTime @default(now())
}

// الشركات المصنعة
model Manufacturer {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  nameEn    String?  // الاسم بالإنجليزية
  code      String?  @unique // رمز الشركة
  status    String   @default("Active") // Active, Inactive
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات
  deviceModels    DeviceModel[]
  devices         Device[]
  warehouseStock  WarehouseStock[]
}

// موديلات الأجهزة
model DeviceModel {
  id             Int      @id @default(autoincrement())
  name           String
  nameEn         String?  // الاسم بالإنجليزية
  manufacturerId Int
  category       String   @default("هاتف ذكي")
  categoryEn     String   @default("Smartphone")
  specifications Json?    // المواصفات التقنية
  status         String   @default("Active") // Active, Inactive
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // العلاقات
  manufacturer   Manufacturer @relation(fields: [manufacturerId], references: [id])
  devices        Device[]
  warehouseStock WarehouseStock[]
  
  @@unique([name, manufacturerId])
}

// المخازن
model Warehouse {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  nameEn      String?  // الاسم بالإنجليزية
  code        String?  @unique // رمز المخزن
  type        String   @default("main") // main, branch, maintenance, evaluation
  location    String
  address     String?  // العنوان التفصيلي
  phone       String?  // هاتف المخزن
  managerId   Int?     // مدير المخزن
  capacity    Int?     // السعة القصوى
  status      String   @default("Active") // Active, Inactive, Maintenance
  description String?  // وصف المخزن
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt @default(now())

  // العلاقات
  devices                Device[]
  supplyOrders          SupplyOrder[]
  supplyOrderDrafts     SupplyOrderDraft[]
  sales                 Sale[]
  returns               Return[]
  evaluationOrders      EvaluationOrder[]
  maintenanceOrders     MaintenanceOrder[]
  maintenanceReceipts   MaintenanceReceiptOrder[]
  deliveryOrders        DeliveryOrder[]
  deliveryTargets       DeliveryOrder[] @relation("TargetWarehouse")
  stocktakeOperations   StocktakeOperation[]
  warehouseTransfersFrom WarehouseTransfer[] @relation("FromWarehouse")
  warehouseTransfersTo   WarehouseTransfer[] @relation("ToWarehouse")
  warehouseStock        WarehouseStock[]
}

// الموردين
model Supplier {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  nameEn      String?  // الاسم بالإنجليزية
  code        String?  @unique // رمز المورد
  phone       String?
  email       String?
  address     String?  // العنوان
  contactPerson String? // الشخص المسؤول
  taxNumber   String?  // الرقم الضريبي
  status      String   @default("Active") // Active, Inactive
  notes       String?  // ملاحظات
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  supplyOrders SupplyOrder[]
  supplyOrderDrafts SupplyOrderDraft[]
  devices      Device[]
}

// العملاء
model Client {
  id          Int      @id @default(autoincrement())
  name        String
  nameEn      String?  // الاسم بالإنجليزية
  code        String?  @unique // رمز العميل
  phone       String?
  email       String?
  address     String?  // العنوان
  idNumber    String?  // رقم الهوية
  taxNumber   String?  // الرقم الضريبي
  type        String   @default("individual") // individual, company
  status      String   @default("Active") // Active, Inactive
  notes       String?  // ملاحظات
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  sales            Sale[]
  returns          Return[]
  maintenanceOrders MaintenanceOrder[]
  deliveryOrders   DeliveryOrder[]
}

// الأجهزة - النموذج الرئيسي لتتبع جميع الأجهزة
model Device {
  id                String   @id // IMEI أو Serial Number
  barcode           String?  @unique // الباركود المولد
  manufacturerId    Int
  deviceModelId     Int
  warehouseId       Int
  supplierId        Int?     // المورد الأصلي
  status            String   @default("في المخزن") // في المخزن، مُباع، قيد الصيانة، معطل، مُستبدل
  condition         String   @default("جديد") // جديد، مستخدم، معاد تصنيع
  grade             String?  // تقييم الجهاز A+, A, B, C
  storage           String?  // مساحة التخزين
  color             String?  // اللون
  price             Float?   // السعر الحالي
  costPrice         Float?   // سعر التكلفة
  warrantyPeriod    Int?     // فترة الضمان بالأشهر
  purchaseDate      DateTime? // تاريخ الشراء
  lastMaintenanceDate DateTime? // تاريخ آخر صيانة
  notes             String?  // ملاحظات
  isActive          Boolean  @default(true) // فعال أم لا
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt @default(now())

  // العلاقات
  manufacturer       Manufacturer @relation(fields: [manufacturerId], references: [id])
  deviceModel        DeviceModel @relation(fields: [deviceModelId], references: [id])
  warehouse          Warehouse @relation(fields: [warehouseId], references: [id])
  supplier           Supplier? @relation(fields: [supplierId], references: [id])
  
  // تتبع العمليات
  supplyOrderItems          SupplyOrderItem[]
  saleItems                 SaleItem[]
  returnItems               ReturnItem[]
  replacementForReturns     ReturnItem[] @relation("ReplacementDevice")
  evaluationOrderItems      EvaluationOrderItem[]
  maintenanceOrderItems     MaintenanceOrderItem[]
  maintenanceReceiptItems   MaintenanceReceiptOrderItem[]
  deliveryOrderItems        DeliveryOrderItem[]
  stocktakeItems           StocktakeItem[]
  transferItems            WarehouseTransferItem[]
  deviceMovements          DeviceMovement[]
  maintenanceLogs          MaintenanceLog[]
  
  @@index([status])
  @@index([warehouseId])
  @@index([manufacturerId])
  @@index([deviceModelId])
}

// مخزون المخازن - تجميع الأجهزة المتشابهة
model WarehouseStock {
  id              Int      @id @default(autoincrement())
  warehouseId     Int
  manufacturerId  Int
  deviceModelId   Int
  condition       String   // جديد، مستخدم
  totalQuantity   Int      @default(0)
  availableQuantity Int    @default(0) // الكمية المتاحة للبيع
  reservedQuantity Int     @default(0) // الكمية المحجوزة
  lastUpdated     DateTime @updatedAt @default(now())
  
  // العلاقات
  warehouse       Warehouse @relation(fields: [warehouseId], references: [id])
  manufacturer    Manufacturer @relation(fields: [manufacturerId], references: [id])
  deviceModel     DeviceModel @relation(fields: [deviceModelId], references: [id])
  
  @@unique([warehouseId, manufacturerId, deviceModelId, condition])
  @@map("warehouse_stock")
}

// حركات الأجهزة - تتبع جميع التحركات
model DeviceMovement {
  id              Int      @id @default(autoincrement())
  deviceId        String
  movementType    String   // supply, sale, return, transfer, maintenance, evaluation
  fromWarehouseId Int?
  toWarehouseId   Int?
  fromStatus      String?  // الحالة السابقة
  toStatus        String   // الحالة الجديدة
  referenceType   String?  // نوع المرجع: supply_order, sale, return, etc.
  referenceId     String?  // معرف المرجع
  referenceNumber String?  // رقم المرجع
  employeeId      Int
  employeeName    String
  notes           String?
  movementDate    DateTime @default(now())
  
  // العلاقات
  device          Device @relation(fields: [deviceId], references: [id])
  employee        User @relation(fields: [employeeId], references: [id])
  
  @@map("device_movements")
}

// سجل العمليات التدقيقية
model AuditLog {
  id        Int      @id @default(autoincrement())
  timestamp DateTime @default(now())
  userId    Int
  username  String
  operation String   // CREATE, UPDATE, DELETE, TRANSFER, etc.
  tableName String?  // اسم الجدول المتأثر
  recordId  String?  // معرف السجل
  oldValues Json?    // القيم القديمة
  newValues Json?    // القيم الجديدة
  details   String
  ipAddress String?  // عنوان IP
  userAgent String?  // معلومات المتصفح
  
  // العلاقات
  user      User @relation(fields: [userId], references: [id])
  
  @@index([timestamp])
  @@index([userId])
  @@index([operation])
}

// أوامر التوريد
model SupplyOrder {
  id                Int      @id @default(autoincrement())
  orderNumber       String   @unique // رقم الأمر المولد تلقائياً
  supplierId        Int
  warehouseId       Int
  employeeId        Int
  invoiceNumber     String?  // رقم فاتورة المورد
  invoiceDate       DateTime? // تاريخ الفاتورة
  supplyDate        DateTime @default(now()) // تاريخ التوريد
  totalAmount       Float?   // المبلغ الإجمالي
  totalQuantity     Int      @default(0) // إجمالي الكمية
  notes             String?  // ملاحظات
  attachments       Json?    // مرفقات كـ JSON
  referenceNumber   String?  // رقم مرجعي
  status            String   @default("draft") // draft, confirmed, completed, cancelled
  approvedBy        Int?     // معتمد من قِبل
  approvedAt        DateTime? // تاريخ الاعتماد
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt @default(now())

  // العلاقات
  supplier          Supplier @relation(fields: [supplierId], references: [id])
  warehouse         Warehouse @relation(fields: [warehouseId], references: [id])
  employee          User @relation(fields: [employeeId], references: [id])
  items             SupplyOrderItem[]
  
  @@map("supply_orders")
}

// مسودات أوامر التوريد
model SupplyOrderDraft {
  id            Int      @id @default(autoincrement())
  draftId       String   @unique
  userId        Int
  supplierName  String?
  supplierId    Int?
  warehouseId   Int?
  employeeName  String
  supplyDate    DateTime
  notes         String?
  items         Json     @default("[]")
  attachments   Json     @default("[]")
  formState     Json     @default("{}")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt @default(now())

  // العلاقات
  user          User @relation(fields: [userId], references: [id])
  supplier      Supplier? @relation(fields: [supplierId], references: [id])
  warehouse     Warehouse? @relation(fields: [warehouseId], references: [id])
  
  @@map("SupplyOrderDraft")
}

model SupplyOrderItem {
  id              Int      @id @default(autoincrement())
  supplyOrderId   Int
  deviceId        String   // IMEI/Serial Number
  manufacturerId  Int      // مُستخرج من الجهاز
  deviceModelId   Int      // مُستخرج من الجهاز
  condition       String   // جديد، مستخدم
  costPrice       Float?   // سعر التكلفة
  sellingPrice    Float?   // سعر البيع المقترح
  warrantyPeriod  Int?     // فترة الضمان بالأشهر
  notes           String?  // ملاحظات خاصة بالجهاز
  createdAt       DateTime @default(now())

  // العلاقات
  supplyOrder     SupplyOrder @relation(fields: [supplyOrderId], references: [id], onDelete: Cascade)
  device          Device @relation(fields: [deviceId], references: [id])
  
  @@map("supply_order_items")
}

// أوامر البيع
model Sale {
  id                Int      @id @default(autoincrement())
  saleNumber        String   @unique // رقم البيع المولد تلقائياً
  opNumber          String?  // رقم العملية
  clientId          Int
  warehouseId       Int
  employeeId        Int
  saleDate          DateTime @default(now())
  totalAmount       Float    // المبلغ الإجمالي
  totalQuantity     Int      @default(0) // إجمالي الكمية
  discount          Float?   @default(0) // الخصم
  tax               Float?   @default(0) // الضريبة
  netAmount         Float    // صافي المبلغ
  paymentMethod     String?  @default("cash") // cash, card, transfer
  warrantyPeriod    String?  // فترة الضمان
  notes             String?  // ملاحظات
  attachments       Json?    // مرفقات كـ JSON
  status            String   @default("completed") // draft, confirmed, completed, cancelled
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt @default(now())

  // العلاقات
  client            Client @relation(fields: [clientId], references: [id])
  warehouse         Warehouse @relation(fields: [warehouseId], references: [id])
  employee          User @relation(fields: [employeeId], references: [id])
  items             SaleItem[]
  returns           Return[] // المرتجعات المرتبطة بهذا البيع
  
  @@map("sales")
}

model SaleItem {
  id              Int      @id @default(autoincrement())
  saleId          Int
  deviceId        String
  unitPrice       Float    // سعر الوحدة
  finalPrice      Float    // السعر النهائي بعد الخصم
  discount        Float?   @default(0) // خصم على هذا الصنف
  warrantyPeriod  String?  // فترة ضمان خاصة
  notes           String?  // ملاحظات
  createdAt       DateTime @default(now())

  // العلاقات
  sale            Sale @relation(fields: [saleId], references: [id], onDelete: Cascade)
  device          Device @relation(fields: [deviceId], references: [id])
  
  @@map("sale_items")
}

// أوامر الإرجاع
model Return {
  id                Int      @id @default(autoincrement())
  returnNumber      String   @unique // رقم الإرجاع المولد تلقائياً
  saleId            Int      // البيع الأصلي
  clientId          Int
  warehouseId       Int
  employeeId        Int
  returnDate        DateTime @default(now())
  returnType        String   @default("refund") // refund, replacement, repair
  totalAmount       Float?   @default(0) // قيمة المرتجع
  totalQuantity     Int      @default(0) // إجمالي الكمية
  refundAmount      Float?   @default(0) // مبلغ الاسترداد
  processingFee     Float?   @default(0) // رسوم المعالجة
  notes             String?  // ملاحظات
  attachments       Json?    // مرفقات كـ JSON
  status            String   @default("pending") // pending, approved, rejected, completed
  processedBy       Int?     // معالج من قِبل
  processedAt       DateTime? // تاريخ المعالجة
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt @default(now())

  // العلاقات
  originalSale      Sale @relation(fields: [saleId], references: [id])
  client            Client @relation(fields: [clientId], references: [id])
  warehouse         Warehouse @relation(fields: [warehouseId], references: [id])
  employee          User @relation(fields: [employeeId], references: [id])
  processor         User? @relation("ProcessedReturns", fields: [processedBy], references: [id])
  items             ReturnItem[]
  
  @@map("returns")
}

model ReturnItem {
  id                    Int      @id @default(autoincrement())
  returnId              Int
  deviceId              String   // الجهاز المُرجع
  returnReason          String   // سبب الإرجاع
  deviceCondition       String   // حالة الجهاز عند الإرجاع
  isDefective           Boolean  @default(false) // معطل أم لا
  replacementDeviceId   String?  // جهاز الاستبدال (إن وُجد)
  isReplacement         Boolean  @default(false) // هل هو استبدال
  refundAmount          Float?   @default(0) // مبلغ الاسترداد لهذا الصنف
  notes                 String?  // ملاحظات
  createdAt             DateTime @default(now())

  // العلاقات
  return                Return @relation(fields: [returnId], references: [id], onDelete: Cascade)
  device                Device @relation(fields: [deviceId], references: [id])
  replacementDevice     Device? @relation("ReplacementDevice", fields: [replacementDeviceId], references: [id])
  
  @@map("return_items")
}

// أوامر التقييم
model EvaluationOrder {
  id                Int      @id @default(autoincrement())
  orderNumber       String   @unique // رقم أمر التقييم
  warehouseId       Int
  employeeId        Int
  evaluationDate    DateTime @default(now())
  totalQuantity     Int      @default(0) // إجمالي الكمية
  completedQuantity Int      @default(0) // الكمية المكتملة
  notes             String?  // ملاحظات
  status            String   @default("pending") // pending, in_progress, completed, cancelled
  acknowledgedBy    Int?     // معتمد من قِبل
  acknowledgedAt    DateTime? // تاريخ الاعتماد
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt @default(now())

  // العلاقات
  warehouse         Warehouse @relation(fields: [warehouseId], references: [id])
  employee          User @relation(fields: [employeeId], references: [id])
  acknowledger      User? @relation("AcknowledgedEvaluations", fields: [acknowledgedBy], references: [id])
  items             EvaluationOrderItem[]

  @@map("evaluation_orders")
}

model EvaluationOrderItem {
  id                Int      @id @default(autoincrement())
  evaluationOrderId Int
  deviceId          String
  previousGrade     String?  // التقييم السابق
  externalGrade     String   // تقييم المظهر الخارجي
  screenGrade       String   // تقييم الشاشة
  networkGrade      String   // تقييم الشبكة
  batteryGrade      String?  // تقييم البطارية
  finalGrade        String   // التقييم النهائي
  recommendedPrice  Float?   // السعر المقترح
  fault             String?  // العطل (إن وُجد)
  damageType        String?  // نوع الضرر
  isAccepted        Boolean  @default(true) // مقبول أم مرفوض
  rejectionReason   String?  // سبب الرفض
  notes             String?  // ملاحظات
  evaluatedAt       DateTime @default(now())

  // العلاقات
  evaluationOrder   EvaluationOrder @relation(fields: [evaluationOrderId], references: [id], onDelete: Cascade)
  device            Device @relation(fields: [deviceId], references: [id])

  @@map("evaluation_order_items")
}

// أوامر الصيانة (الإرسال للصيانة)
model MaintenanceOrder {
  id                        Int      @id @default(autoincrement())
  orderNumber               String   @unique // رقم أمر الصيانة
  warehouseId               Int?     // المخزن (للصيانة من المخزن)
  clientId                  Int?     // العميل (للصيانة المباشرة)
  employeeId                Int      // الموظف المُرسِل
  maintenanceProviderId     Int?     // مقدم خدمة الصيانة
  maintenanceProviderName   String?  // اسم مقدم الصيانة
  orderDate                 DateTime @default(now())
  expectedReturnDate        DateTime? // التاريخ المتوقع للإرجاع
  totalQuantity             Int      @default(0) // إجمالي الكمية
  estimatedCost             Float?   // التكلفة المقدرة
  notes                     String?  // ملاحظات
  attachments               Json?    // مرفقات
  referenceNumber           String?  // رقم مرجعي
  source                    String   @default("warehouse") // warehouse, direct
  status                    String   @default("sent") // sent, in_progress, completed, cancelled
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt @default(now())

  // العلاقات
  warehouse                 Warehouse? @relation(fields: [warehouseId], references: [id])
  client                    Client? @relation(fields: [clientId], references: [id])
  employee                  User @relation(fields: [employeeId], references: [id])
  items                     MaintenanceOrderItem[]
  receipts                  MaintenanceReceiptOrder[] // إيصالات الاستلام

  @@map("maintenance_orders")
}

model MaintenanceOrderItem {
  id                 Int      @id @default(autoincrement())
  maintenanceOrderId Int
  deviceId           String
  reportedFault      String?  // العطل المُبلغ عنه
  deviceCondition    String?  // حالة الجهاز عند الإرسال
  estimatedCost      Float?   // التكلفة المقدرة لهذا الجهاز
  priority           String?  @default("normal") // low, normal, high, urgent
  notes              String?  // ملاحظات
  sentAt             DateTime @default(now())

  // العلاقات
  maintenanceOrder   MaintenanceOrder @relation(fields: [maintenanceOrderId], references: [id], onDelete: Cascade)
  device             Device @relation(fields: [deviceId], references: [id])

  @@map("maintenance_order_items")
}

// إيصالات استلام الصيانة
model MaintenanceReceiptOrder {
  id                        Int      @id @default(autoincrement())
  receiptNumber             String   @unique // رقم الإيصال
  maintenanceOrderId        Int      // أمر الصيانة الأصلي
  warehouseId               Int      // المخزن المستلم
  employeeId                Int      // الموظف المستلم
  maintenanceProviderName   String?  // اسم مقدم الصيانة
  receiptDate               DateTime @default(now())
  totalCost                 Float?   @default(0) // التكلفة الإجمالية
  totalQuantity             Int      @default(0) // إجمالي الكمية
  notes                     String?  // ملاحظات
  attachments               Json?    // مرفقات
  referenceNumber           String?  // رقم مرجعي
  status                    String   @default("received") // received, verified, completed
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt @default(now())

  // العلاقات
  maintenanceOrder          MaintenanceOrder @relation(fields: [maintenanceOrderId], references: [id])
  warehouse                 Warehouse @relation(fields: [warehouseId], references: [id])
  employee                  User @relation(fields: [employeeId], references: [id])
  items                     MaintenanceReceiptOrderItem[]

  @@map("maintenance_receipt_orders")
}

model MaintenanceReceiptOrderItem {
  id                        Int      @id @default(autoincrement())
  maintenanceReceiptOrderId Int
  deviceId                  String
  maintenanceResult         String   // ناجح، فاشل، جزئي
  repairDetails             String?  // تفاصيل الإصلاح
  fault                     String?  // العطل الفعلي
  partsReplaced             String?  // القطع المستبدلة
  repairCost                Float?   // تكلفة الإصلاح
  warrantyPeriod            Int?     // فترة ضمان الصيانة (بالأشهر)
  newCondition              String?  // الحالة الجديدة بعد الصيانة
  newGrade                  String?  // التقييم الجديد
  damage                    String?  // أضرار إضافية
  isSuccessful              Boolean  @default(true) // نجحت الصيانة أم لا
  notes                     String?  // ملاحظات
  completedAt               DateTime @default(now())

  // العلاقات
  maintenanceReceiptOrder   MaintenanceReceiptOrder @relation(fields: [maintenanceReceiptOrderId], references: [id], onDelete: Cascade)
  device                    Device @relation(fields: [deviceId], references: [id])

  @@map("maintenance_receipt_order_items")
}

// أوامر التسليم (للعملاء أو للمخازن)
model DeliveryOrder {
  id                  Int      @id @default(autoincrement())
  deliveryNumber      String   @unique // رقم التسليم
  sourceWarehouseId   Int      // المخزن المُرسِل
  targetWarehouseId   Int?     // المخزن المستلم (للنقل الداخلي)
  clientId            Int?     // العميل (للتسليم للعميل)
  employeeId          Int      // الموظف المسؤول
  deliveryDate        DateTime @default(now())
  deliveryType        String   @default("warehouse") // warehouse, client, maintenance
  totalQuantity       Int      @default(0) // إجمالي الكمية
  deliveryAddress     String?  // عنوان التسليم
  driverName          String?  // اسم السائق
  vehicleInfo         String?  // معلومات المركبة
  notes               String?  // ملاحظات
  attachments         Json?    // مرفقات
  referenceNumber     String?  // رقم مرجعي
  status              String   @default("pending") // pending, in_transit, delivered, cancelled
  deliveredAt         DateTime? // تاريخ التسليم الفعلي
  receivedBy          String?  // اسم المستلم
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt @default(now())

  // العلاقات
  sourceWarehouse     Warehouse @relation(fields: [sourceWarehouseId], references: [id])
  targetWarehouse     Warehouse? @relation("TargetWarehouse", fields: [targetWarehouseId], references: [id])
  client              Client? @relation(fields: [clientId], references: [id])
  employee            User @relation(fields: [employeeId], references: [id])
  items               DeliveryOrderItem[]

  @@map("delivery_orders")
}

model DeliveryOrderItem {
  id              Int      @id @default(autoincrement())
  deliveryOrderId Int
  deviceId        String
  deliveryResult  String   @default("pending") // pending, delivered, damaged, lost
  condition       String?  // حالة الجهاز عند التسليم
  receivedCondition String? // حالة الجهاز عند الاستلام
  fault           String?  // عطل (إن وُجد)
  damage          String?  // ضرر (إن وُجد)
  notes           String?  // ملاحظات
  deliveredAt     DateTime? // تاريخ التسليم
  
  // العلاقات
  deliveryOrder   DeliveryOrder @relation(fields: [deliveryOrderId], references: [id], onDelete: Cascade)
  device          Device @relation(fields: [deviceId], references: [id])

  @@map("delivery_order_items")
}

// نقل المخازن
model WarehouseTransfer {
  id                  Int      @id @default(autoincrement())
  transferNumber      String   @unique // رقم النقل
  fromWarehouseId     Int      // المخزن المُرسِل
  toWarehouseId       Int      // المخزن المستلم
  employeeId          Int      // الموظف المسؤول
  transferDate        DateTime @default(now())
  transferType        String   @default("internal") // internal, external
  totalQuantity       Int      @default(0) // إجمالي الكمية
  reason              String?  // سبب النقل
  notes               String?  // ملاحظات
  attachments         Json?    // مرفقات
  referenceNumber     String?  // رقم مرجعي
  status              String   @default("pending") // pending, in_transit, completed, cancelled
  sentAt              DateTime? // تاريخ الإرسال
  receivedAt          DateTime? // تاريخ الاستلام
  receivedBy          Int?     // المستلم
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt @default(now())

  // العلاقات
  fromWarehouse       Warehouse @relation("FromWarehouse", fields: [fromWarehouseId], references: [id])
  toWarehouse         Warehouse @relation("ToWarehouse", fields: [toWarehouseId], references: [id])
  employee            User @relation(fields: [employeeId], references: [id])
  receiver            User? @relation("ReceivedTransfers", fields: [receivedBy], references: [id])
  items               WarehouseTransferItem[]

  @@map("warehouse_transfers")
}

model WarehouseTransferItem {
  id                Int      @id @default(autoincrement())
  transferId        Int
  deviceId          String
  transferReason    String?  // سبب نقل هذا الجهاز تحديداً
  condition         String?  // حالة الجهاز عند النقل
  receivedCondition String?  // حالة الجهاز عند الاستلام
  notes             String?  // ملاحظات
  sentAt            DateTime @default(now())
  receivedAt        DateTime? // تاريخ الاستلام

  // العلاقات
  transfer          WarehouseTransfer @relation(fields: [transferId], references: [id], onDelete: Cascade)
  device            Device @relation(fields: [deviceId], references: [id])

  @@map("warehouse_transfer_items")
}

// عمليات الجرد
model StocktakeOperation {
  id                Int      @id @default(autoincrement())
  operationNumber   String   @unique // رقم عملية الجرد
  warehouseId       Int      // المخزن
  employeeId        Int      // الموظف المسؤول
  operationType     String   @default("full") // full, partial, cycle
  startDate         DateTime @default(now())
  endDate           DateTime? // تاريخ الانتهاء
  expectedQuantity  Int?     // الكمية المتوقعة
  actualQuantity    Int?     // الكمية الفعلية
  discrepancyQuantity Int?   // كمية الاختلاف
  notes             String?  // ملاحظات
  status            String   @default("in_progress") // in_progress, completed, cancelled
  completedAt       DateTime? // تاريخ الإكمال
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt @default(now())

  // العلاقات
  warehouse         Warehouse @relation(fields: [warehouseId], references: [id])
  employee          User @relation(fields: [employeeId], references: [id])
  items             StocktakeItem[]

  @@map("stocktake_operations")
}

model StocktakeItem {
  id                  Int      @id @default(autoincrement())
  stocktakeId         Int
  deviceId            String
  expectedStatus      String?  // الحالة المتوقعة
  actualStatus        String   // الحالة الفعلية
  isFound             Boolean  @default(true) // موجود أم مفقود
  condition           String?  // حالة الجهاز
  discrepancyType     String?  // نوع الاختلاف: found, missing, damaged, wrong_location
  previousLocation    String?  // الموقع السابق
  currentLocation     String?  // الموقع الحالي
  notes               String?  // ملاحظات
  checkedAt           DateTime @default(now())

  // العلاقات
  stocktake           StocktakeOperation @relation(fields: [stocktakeId], references: [id], onDelete: Cascade)
  device              Device @relation(fields: [deviceId], references: [id])

  @@map("stocktake_items")
}

// طلبات الموظفين
model EmployeeRequest {
  id                   Int      @id @default(autoincrement())
  requestNumber        String   @unique // رقم الطلب
  requestType          String   // تعديل، حذف، إعادة نظر، إضافة
  category             String   @default("general") // general, supply, sales, returns, maintenance, etc.
  priority             String   @default("normal") // low, normal, high, urgent
  title                String   // عنوان الطلب
  description          String   // وصف مفصل للطلب
  requestedChanges     Json?    // التغييرات المطلوبة كـ JSON
  justification        String?  // المبرر
  expectedImpact       String?  // التأثير المتوقع
  attachments          Json?    // مرفقات كـ JSON
  relatedOrderType     String?  // sales, returns, supply, maintenance, etc.
  relatedOrderId       Int?     // معرف الأمر المرتبط
  relatedOrderNumber   String?  // رقم الأمر المرتبط
  employeeId           Int      // الموظف المُقدِم للطلب
  assignedTo           Int?     // المُكلف بالمراجعة
  status               String   @default("submitted") // submitted, under_review, approved, rejected, completed, cancelled
  adminNotes           String?  // ملاحظات الإدارة
  rejectionReason      String?  // سبب الرفض
  completionNotes      String?  // ملاحظات الإكمال
  requestDate          DateTime @default(now())
  reviewDate           DateTime? // تاريخ بدء المراجعة
  responseDate         DateTime? // تاريخ الرد
  completionDate       DateTime? // تاريخ الإكمال
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt @default(now())

  // العلاقات
  employee             User @relation(fields: [employeeId], references: [id])
  assignee             User? @relation("AssignedRequests", fields: [assignedTo], references: [id])
  messages             InternalMessage[] // الرسائل المرتبطة

  @@map("employee_requests")
}

// الرسائل الداخلية
model InternalMessage {
  id                  Int      @id @default(autoincrement())
  threadId            String   // معرف المحادثة
  messageType         String   @default("general") // general, request, notification, announcement
  senderId            Int      // معرف المرسل
  recipientId         Int?     // معرف المستقبل (null للرسائل العامة)
  recipientType       String   @default("user") // user, group, all
  recipientIds        Json?    // قائمة معرفات المستقبلين للمجموعات
  subject             String?  // موضوع الرسالة
  content             String   // محتوى الرسالة
  attachments         Json?    // مرفقات كـ JSON
  priority            String   @default("normal") // low, normal, high, urgent
  isRead              Boolean  @default(false) // مقروءة أم لا
  readAt              DateTime? // تاريخ القراءة
  parentMessageId     Int?     // معرف الرسالة الأصلية للردود
  employeeRequestId   Int?     // ربط بطلب موظف
  relatedOrderType    String?  // نوع الأمر المرتبط
  relatedOrderId      Int?     // معرف الأمر المرتبط
  status              String   @default("sent") // sent, delivered, read, replied, resolved
  resolutionNote      String?  // ملاحظة الحل
  expiryDate          DateTime? // تاريخ انتهاء الصلاحية
  sentAt              DateTime @default(now())
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt @default(now())

  // العلاقات
  sender              User @relation("SentMessages", fields: [senderId], references: [id])
  recipient           User? @relation("ReceivedMessages", fields: [recipientId], references: [id])
  parentMessage       InternalMessage? @relation("MessageReplies", fields: [parentMessageId], references: [id])
  replies             InternalMessage[] @relation("MessageReplies")
  employeeRequest     EmployeeRequest? @relation(fields: [employeeRequestId], references: [id])

  @@map("internal_messages")
}

// سجل الصيانة (للتتبع التاريخي)
model MaintenanceLog {
  id              Int      @id @default(autoincrement())
  deviceId        String   // الجهاز
  maintenanceType String   // نوع الصيانة: preventive, corrective, warranty
  performedBy     String   // من قام بالصيانة
  maintenanceDate DateTime @default(now())
  description     String   // وصف العمل المنجز
  partsUsed       String?  // القطع المستخدمة
  cost            Float?   // التكلفة
  warrantyPeriod  Int?     // فترة الضمان (بالأشهر)
  beforeCondition String?  // الحالة قبل الصيانة
  afterCondition  String   // الحالة بعد الصيانة
  notes           String?  // ملاحظات
  status          String   @default("completed") // completed, pending, failed
  nextMaintenanceDate DateTime? // تاريخ الصيانة القادمة
  acknowledgedBy  Int?     // معتمد من قِبل
  acknowledgedAt  DateTime? // تاريخ الاعتماد
  createdAt       DateTime @default(now())

  // العلاقات
  device          Device @relation(fields: [deviceId], references: [id])
  acknowledger    User? @relation("AcknowledgedMaintenance", fields: [acknowledgedBy], references: [id])

  @@map("maintenance_logs")
}

// إدارة قواعد البيانات
model DatabaseConnection {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  host        String
  port        Int      @default(5432)
  database    String
  username    String
  password    String   // مشفر
  isActive    Boolean  @default(false)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  backups     DatabaseBackup[]
  databases   Database[]
  
  @@map("database_connections")
}

model DatabaseBackup {
  id           Int                @id @default(autoincrement())
  name         String
  description  String?
  filePath     String
  fileSize     String
  backupType   String             @default("manual") // manual, automatic
  status       String             @default("completed") // pending, completed, failed
  createdBy    String?
  createdAt    DateTime           @default(now())
  
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  connectionId Int
  
  @@map("database_backups")
}

model Database {
  id           Int                @id @default(autoincrement())
  name         String
  connectionId Int
  owner        String             @default("")
  template     String             @default("template0")
  encoding     String             @default("UTF8")
  createdBy    Int
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt @default(now())
  
  // العلاقات
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  creator      User               @relation(fields: [createdBy], references: [id])
  
  @@unique([name, connectionId])
  @@map("databases")
}
