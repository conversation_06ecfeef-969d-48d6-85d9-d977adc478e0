generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                      Int                       @id @default(autoincrement())
  email                   String                    @unique
  name                    String?
  username                String?                   @unique @default("user")
  role                    String?                   @default("user")
  phone                   String?                   @default("")
  photo                   String?                   @default("")
  status                  String?                   @default("Active")
  lastLogin               String?
  branchLocation          String?
  warehouseAccess         Json?
  permissions             Json?
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @default(now()) @updatedAt
  auditLogs               AuditLog[]
  posts                   Post[]
  supplyOrderDrafts       SupplyOrderDraft[]
  createdDatabases        Database[]
  deliveryOrders          DeliveryOrder[]
  deviceMovements         DeviceMovement[]
  assignedRequests        EmployeeRequest[]         @relation("AssignedRequests")
  employeeRequests        EmployeeRequest[]
  acknowledgedEvaluations EvaluationOrder[]         @relation("AcknowledgedEvaluations")
  evaluationOrders        EvaluationOrder[]
  receivedMessages        InternalMessage[]         @relation("ReceivedMessages")
  sentMessages            InternalMessage[]         @relation("SentMessages")
  acknowledgedMaintenance MaintenanceLog[]          @relation("AcknowledgedMaintenance")
  maintenanceOrders       MaintenanceOrder[]
  maintenanceReceipts     MaintenanceReceiptOrder[]
  returns                 Return[]
  processedReturns        Return[]                  @relation("ProcessedReturns")
  sales                   Sale[]
  stocktakeOperations     StocktakeOperation[]
  supplyOrders            SupplyOrder[]
  warehouseTransfers      WarehouseTransfer[]
  receivedTransfers       WarehouseTransfer[]       @relation("ReceivedTransfers")

  @@map("users")
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  authorId  Int
  author    User    @relation(fields: [authorId], references: [id])
}

model SystemSetting {
  id            Int      @id @default(1)
  logoUrl       String   @default("")
  companyNameAr String   @default("")
  companyNameEn String   @default("")
  addressAr     String   @default("")
  addressEn     String   @default("")
  phone         String   @default("")
  email         String   @default("")
  website       String   @default("")
  footerTextAr  String   @default("")
  footerTextEn  String   @default("")
  updatedAt     DateTime @default(now()) @updatedAt
  createdAt     DateTime @default(now())
}

model Manufacturer {
  id             Int              @id @default(autoincrement())
  name           String           @unique
  nameEn         String?
  code           String?          @unique
  status         String           @default("Active")
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  devices        Device[]
  deviceModels   DeviceModel[]
  warehouseStock WarehouseStock[]
}

model DeviceModel {
  id             Int              @id @default(autoincrement())
  name           String
  nameEn         String?
  manufacturerId Int
  category       String           @default("هاتف ذكي")
  categoryEn     String           @default("Smartphone")
  specifications Json?
  status         String           @default("Active")
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  devices        Device[]
  manufacturer   Manufacturer     @relation(fields: [manufacturerId], references: [id])
  warehouseStock WarehouseStock[]

  @@unique([name, manufacturerId])
}

model Warehouse {
  id                     Int                       @id @default(autoincrement())
  name                   String                    @unique
  nameEn                 String?
  code                   String?                   @unique
  type                   String                    @default("main")
  location               String
  address                String?
  phone                  String?
  managerId              Int?
  capacity               Int?
  status                 String                    @default("Active")
  description            String?
  createdAt              DateTime                  @default(now())
  updatedAt              DateTime                  @default(now()) @updatedAt
  devices                Device[]
  supplyOrderDrafts      SupplyOrderDraft[]
  deliveryOrders         DeliveryOrder[]
  deliveryTargets        DeliveryOrder[]           @relation("TargetWarehouse")
  evaluationOrders       EvaluationOrder[]
  maintenanceOrders      MaintenanceOrder[]
  maintenanceReceipts    MaintenanceReceiptOrder[]
  returns                Return[]
  sales                  Sale[]
  stocktakeOperations    StocktakeOperation[]
  supplyOrders           SupplyOrder[]
  warehouseStock         WarehouseStock[]
  warehouseTransfersFrom WarehouseTransfer[]       @relation("FromWarehouse")
  warehouseTransfersTo   WarehouseTransfer[]       @relation("ToWarehouse")
}

model Supplier {
  id                Int                @id @default(autoincrement())
  name              String             @unique
  nameEn            String?
  code              String?            @unique
  phone             String?
  email             String?
  address           String?
  contactPerson     String?
  taxNumber         String?
  status            String             @default("Active")
  notes             String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  devices           Device[]
  supplyOrderDrafts SupplyOrderDraft[]
  supplyOrders      SupplyOrder[]
}

model Client {
  id                Int                @id @default(autoincrement())
  name              String
  nameEn            String?
  code              String?            @unique
  phone             String?
  email             String?
  address           String?
  idNumber          String?
  taxNumber         String?
  type              String             @default("individual")
  status            String             @default("Active")
  notes             String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  deliveryOrders    DeliveryOrder[]
  maintenanceOrders MaintenanceOrder[]
  returns           Return[]
  sales             Sale[]
}

model Device {
  id                      String                        @id
  barcode                 String?                       @unique
  manufacturerId          Int
  deviceModelId           Int
  warehouseId             Int
  supplierId              Int?
  status                  String                        @default("في المخزن")
  condition               String                        @default("جديد")
  grade                   String?
  storage                 String?
  color                   String?
  price                   Float?
  costPrice               Float?
  warrantyPeriod          Int?
  purchaseDate            DateTime?
  lastMaintenanceDate     DateTime?
  notes                   String?
  isActive                Boolean                       @default(true)
  createdAt               DateTime                      @default(now())
  updatedAt               DateTime                      @default(now()) @updatedAt
  deviceModel             DeviceModel                   @relation(fields: [deviceModelId], references: [id])
  manufacturer            Manufacturer                  @relation(fields: [manufacturerId], references: [id])
  supplier                Supplier?                     @relation(fields: [supplierId], references: [id])
  warehouse               Warehouse                     @relation(fields: [warehouseId], references: [id])
  deliveryOrderItems      DeliveryOrderItem[]
  deviceMovements         DeviceMovement[]
  evaluationOrderItems    EvaluationOrderItem[]
  maintenanceLogs         MaintenanceLog[]
  maintenanceOrderItems   MaintenanceOrderItem[]
  maintenanceReceiptItems MaintenanceReceiptOrderItem[]
  returnItems             ReturnItem[]
  replacementForReturns   ReturnItem[]                  @relation("ReplacementDevice")
  saleItems               SaleItem[]
  stocktakeItems          StocktakeItem[]
  supplyOrderItems        SupplyOrderItem[]
  transferItems           WarehouseTransferItem[]

  @@index([status])
  @@index([warehouseId])
  @@index([manufacturerId])
  @@index([deviceModelId])
}

model WarehouseStock {
  id                Int          @id @default(autoincrement())
  warehouseId       Int
  manufacturerId    Int
  deviceModelId     Int
  condition         String
  totalQuantity     Int          @default(0)
  availableQuantity Int          @default(0)
  reservedQuantity  Int          @default(0)
  lastUpdated       DateTime     @default(now()) @updatedAt
  deviceModel       DeviceModel  @relation(fields: [deviceModelId], references: [id])
  manufacturer      Manufacturer @relation(fields: [manufacturerId], references: [id])
  warehouse         Warehouse    @relation(fields: [warehouseId], references: [id])

  @@unique([warehouseId, manufacturerId, deviceModelId, condition])
  @@map("warehouse_stock")
}

model DeviceMovement {
  id              Int      @id @default(autoincrement())
  deviceId        String
  movementType    String
  fromWarehouseId Int?
  toWarehouseId   Int?
  fromStatus      String?
  toStatus        String
  referenceType   String?
  referenceId     String?
  referenceNumber String?
  employeeId      Int
  employeeName    String
  notes           String?
  movementDate    DateTime @default(now())
  device          Device   @relation(fields: [deviceId], references: [id])
  employee        User     @relation(fields: [employeeId], references: [id])

  @@map("device_movements")
}

model AuditLog {
  id        Int      @id @default(autoincrement())
  timestamp DateTime @default(now())
  userId    Int
  username  String
  operation String
  tableName String?
  recordId  String?
  oldValues Json?
  newValues Json?
  details   String
  ipAddress String?
  userAgent String?
  user      User     @relation(fields: [userId], references: [id])

  @@index([timestamp])
  @@index([userId])
  @@index([operation])
}

model SupplyOrder {
  id              Int               @id @default(autoincrement())
  orderNumber     String            @unique
  supplierId      Int
  warehouseId     Int
  employeeId      Int
  invoiceNumber   String?
  invoiceDate     DateTime?
  supplyDate      DateTime          @default(now())
  totalAmount     Float?
  totalQuantity   Int               @default(0)
  notes           String?
  attachments     Json?
  referenceNumber String?
  status          String            @default("draft")
  approvedBy      Int?
  approvedAt      DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @default(now()) @updatedAt
  items           SupplyOrderItem[]
  employee        User              @relation(fields: [employeeId], references: [id])
  supplier        Supplier          @relation(fields: [supplierId], references: [id])
  warehouse       Warehouse         @relation(fields: [warehouseId], references: [id])

  @@map("supply_orders")
}

model SupplyOrderDraft {
  id           Int        @id @default(autoincrement())
  draftId      String     @unique
  userId       Int
  supplierName String?
  supplierId   Int?
  warehouseId  Int?
  employeeName String
  supplyDate   DateTime
  notes        String?
  items        Json       @default("[]")
  attachments  Json       @default("[]")
  formState    Json       @default("{}")
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @default(now()) @updatedAt
  supplier     Supplier?  @relation(fields: [supplierId], references: [id])
  user         User       @relation(fields: [userId], references: [id])
  warehouse    Warehouse? @relation(fields: [warehouseId], references: [id])

  @@map("SupplyOrderDraft")
}

model SupplyOrderItem {
  id             Int         @id @default(autoincrement())
  supplyOrderId  Int
  deviceId       String
  manufacturerId Int
  deviceModelId  Int
  condition      String
  costPrice      Float?
  sellingPrice   Float?
  warrantyPeriod Int?
  notes          String?
  createdAt      DateTime    @default(now())
  device         Device      @relation(fields: [deviceId], references: [id])
  supplyOrder    SupplyOrder @relation(fields: [supplyOrderId], references: [id], onDelete: Cascade)

  @@map("supply_order_items")
}

model Sale {
  id             Int        @id @default(autoincrement())
  saleNumber     String     @unique
  opNumber       String?
  clientId       Int
  warehouseId    Int
  employeeId     Int
  saleDate       DateTime   @default(now())
  totalAmount    Float
  totalQuantity  Int        @default(0)
  discount       Float?     @default(0)
  tax            Float?     @default(0)
  netAmount      Float
  paymentMethod  String?    @default("cash")
  warrantyPeriod String?
  notes          String?
  attachments    Json?
  status         String     @default("completed")
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @default(now()) @updatedAt
  returns        Return[]
  items          SaleItem[]
  client         Client     @relation(fields: [clientId], references: [id])
  employee       User       @relation(fields: [employeeId], references: [id])
  warehouse      Warehouse  @relation(fields: [warehouseId], references: [id])

  @@map("sales")
}

model SaleItem {
  id             Int      @id @default(autoincrement())
  saleId         Int
  deviceId       String
  unitPrice      Float
  finalPrice     Float
  discount       Float?   @default(0)
  warrantyPeriod String?
  notes          String?
  createdAt      DateTime @default(now())
  device         Device   @relation(fields: [deviceId], references: [id])
  sale           Sale     @relation(fields: [saleId], references: [id], onDelete: Cascade)

  @@map("sale_items")
}

model Return {
  id            Int          @id @default(autoincrement())
  returnNumber  String       @unique
  saleId        Int
  clientId      Int
  warehouseId   Int
  employeeId    Int
  returnDate    DateTime     @default(now())
  returnType    String       @default("refund")
  totalAmount   Float?       @default(0)
  totalQuantity Int          @default(0)
  refundAmount  Float?       @default(0)
  processingFee Float?       @default(0)
  notes         String?
  attachments   Json?
  status        String       @default("pending")
  processedBy   Int?
  processedAt   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @default(now()) @updatedAt
  items         ReturnItem[]
  client        Client       @relation(fields: [clientId], references: [id])
  employee      User         @relation(fields: [employeeId], references: [id])
  processor     User?        @relation("ProcessedReturns", fields: [processedBy], references: [id])
  originalSale  Sale         @relation(fields: [saleId], references: [id])
  warehouse     Warehouse    @relation(fields: [warehouseId], references: [id])

  @@map("returns")
}

model ReturnItem {
  id                  Int      @id @default(autoincrement())
  returnId            Int
  deviceId            String
  returnReason        String
  deviceCondition     String
  isDefective         Boolean  @default(false)
  replacementDeviceId String?
  isReplacement       Boolean  @default(false)
  refundAmount        Float?   @default(0)
  notes               String?
  createdAt           DateTime @default(now())
  device              Device   @relation(fields: [deviceId], references: [id])
  replacementDevice   Device?  @relation("ReplacementDevice", fields: [replacementDeviceId], references: [id])
  return              Return   @relation(fields: [returnId], references: [id], onDelete: Cascade)

  @@map("return_items")
}

model EvaluationOrder {
  id                Int                   @id @default(autoincrement())
  orderNumber       String                @unique
  warehouseId       Int
  employeeId        Int
  evaluationDate    DateTime              @default(now())
  totalQuantity     Int                   @default(0)
  completedQuantity Int                   @default(0)
  notes             String?
  status            String                @default("pending")
  acknowledgedBy    Int?
  acknowledgedAt    DateTime?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @default(now()) @updatedAt
  items             EvaluationOrderItem[]
  acknowledger      User?                 @relation("AcknowledgedEvaluations", fields: [acknowledgedBy], references: [id])
  employee          User                  @relation(fields: [employeeId], references: [id])
  warehouse         Warehouse             @relation(fields: [warehouseId], references: [id])

  @@map("evaluation_orders")
}

model EvaluationOrderItem {
  id                Int             @id @default(autoincrement())
  evaluationOrderId Int
  deviceId          String
  previousGrade     String?
  externalGrade     String
  screenGrade       String
  networkGrade      String
  batteryGrade      String?
  finalGrade        String
  recommendedPrice  Float?
  fault             String?
  damageType        String?
  isAccepted        Boolean         @default(true)
  rejectionReason   String?
  notes             String?
  evaluatedAt       DateTime        @default(now())
  device            Device          @relation(fields: [deviceId], references: [id])
  evaluationOrder   EvaluationOrder @relation(fields: [evaluationOrderId], references: [id], onDelete: Cascade)

  @@map("evaluation_order_items")
}

model MaintenanceOrder {
  id                      Int                       @id @default(autoincrement())
  orderNumber             String                    @unique
  warehouseId             Int?
  clientId                Int?
  employeeId              Int
  maintenanceProviderId   Int?
  maintenanceProviderName String?
  orderDate               DateTime                  @default(now())
  expectedReturnDate      DateTime?
  totalQuantity           Int                       @default(0)
  estimatedCost           Float?
  notes                   String?
  attachments             Json?
  referenceNumber         String?
  source                  String                    @default("warehouse")
  status                  String                    @default("sent")
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @default(now()) @updatedAt
  items                   MaintenanceOrderItem[]
  client                  Client?                   @relation(fields: [clientId], references: [id])
  employee                User                      @relation(fields: [employeeId], references: [id])
  warehouse               Warehouse?                @relation(fields: [warehouseId], references: [id])
  receipts                MaintenanceReceiptOrder[]

  @@map("maintenance_orders")
}

model MaintenanceOrderItem {
  id                 Int              @id @default(autoincrement())
  maintenanceOrderId Int
  deviceId           String
  reportedFault      String?
  deviceCondition    String?
  estimatedCost      Float?
  priority           String?          @default("normal")
  notes              String?
  sentAt             DateTime         @default(now())
  device             Device           @relation(fields: [deviceId], references: [id])
  maintenanceOrder   MaintenanceOrder @relation(fields: [maintenanceOrderId], references: [id], onDelete: Cascade)

  @@map("maintenance_order_items")
}

model MaintenanceReceiptOrder {
  id                      Int                           @id @default(autoincrement())
  receiptNumber           String                        @unique
  maintenanceOrderId      Int
  warehouseId             Int
  employeeId              Int
  maintenanceProviderName String?
  receiptDate             DateTime                      @default(now())
  totalCost               Float?                        @default(0)
  totalQuantity           Int                           @default(0)
  notes                   String?
  attachments             Json?
  referenceNumber         String?
  status                  String                        @default("received")
  createdAt               DateTime                      @default(now())
  updatedAt               DateTime                      @default(now()) @updatedAt
  items                   MaintenanceReceiptOrderItem[]
  employee                User                          @relation(fields: [employeeId], references: [id])
  maintenanceOrder        MaintenanceOrder              @relation(fields: [maintenanceOrderId], references: [id])
  warehouse               Warehouse                     @relation(fields: [warehouseId], references: [id])

  @@map("maintenance_receipt_orders")
}

model MaintenanceReceiptOrderItem {
  id                        Int                     @id @default(autoincrement())
  maintenanceReceiptOrderId Int
  deviceId                  String
  maintenanceResult         String
  repairDetails             String?
  fault                     String?
  partsReplaced             String?
  repairCost                Float?
  warrantyPeriod            Int?
  newCondition              String?
  newGrade                  String?
  damage                    String?
  isSuccessful              Boolean                 @default(true)
  notes                     String?
  completedAt               DateTime                @default(now())
  device                    Device                  @relation(fields: [deviceId], references: [id])
  maintenanceReceiptOrder   MaintenanceReceiptOrder @relation(fields: [maintenanceReceiptOrderId], references: [id], onDelete: Cascade)

  @@map("maintenance_receipt_order_items")
}

model DeliveryOrder {
  id                Int                 @id @default(autoincrement())
  deliveryNumber    String              @unique
  sourceWarehouseId Int
  targetWarehouseId Int?
  clientId          Int?
  employeeId        Int
  deliveryDate      DateTime            @default(now())
  deliveryType      String              @default("warehouse")
  totalQuantity     Int                 @default(0)
  deliveryAddress   String?
  driverName        String?
  vehicleInfo       String?
  notes             String?
  attachments       Json?
  referenceNumber   String?
  status            String              @default("pending")
  deliveredAt       DateTime?
  receivedBy        String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @default(now()) @updatedAt
  items             DeliveryOrderItem[]
  client            Client?             @relation(fields: [clientId], references: [id])
  employee          User                @relation(fields: [employeeId], references: [id])
  sourceWarehouse   Warehouse           @relation(fields: [sourceWarehouseId], references: [id])
  targetWarehouse   Warehouse?          @relation("TargetWarehouse", fields: [targetWarehouseId], references: [id])

  @@map("delivery_orders")
}

model DeliveryOrderItem {
  id                Int           @id @default(autoincrement())
  deliveryOrderId   Int
  deviceId          String
  deliveryResult    String        @default("pending")
  condition         String?
  receivedCondition String?
  fault             String?
  damage            String?
  notes             String?
  deliveredAt       DateTime?
  deliveryOrder     DeliveryOrder @relation(fields: [deliveryOrderId], references: [id], onDelete: Cascade)
  device            Device        @relation(fields: [deviceId], references: [id])

  @@map("delivery_order_items")
}

model WarehouseTransfer {
  id              Int                     @id @default(autoincrement())
  transferNumber  String                  @unique
  fromWarehouseId Int
  toWarehouseId   Int
  employeeId      Int
  transferDate    DateTime                @default(now())
  transferType    String                  @default("internal")
  totalQuantity   Int                     @default(0)
  reason          String?
  notes           String?
  attachments     Json?
  referenceNumber String?
  status          String                  @default("pending")
  sentAt          DateTime?
  receivedAt      DateTime?
  receivedBy      Int?
  createdAt       DateTime                @default(now())
  updatedAt       DateTime                @default(now()) @updatedAt
  items           WarehouseTransferItem[]
  employee        User                    @relation(fields: [employeeId], references: [id])
  fromWarehouse   Warehouse               @relation("FromWarehouse", fields: [fromWarehouseId], references: [id])
  receiver        User?                   @relation("ReceivedTransfers", fields: [receivedBy], references: [id])
  toWarehouse     Warehouse               @relation("ToWarehouse", fields: [toWarehouseId], references: [id])

  @@map("warehouse_transfers")
}

model WarehouseTransferItem {
  id                Int               @id @default(autoincrement())
  transferId        Int
  deviceId          String
  transferReason    String?
  condition         String?
  receivedCondition String?
  notes             String?
  sentAt            DateTime          @default(now())
  receivedAt        DateTime?
  device            Device            @relation(fields: [deviceId], references: [id])
  transfer          WarehouseTransfer @relation(fields: [transferId], references: [id], onDelete: Cascade)

  @@map("warehouse_transfer_items")
}

model StocktakeOperation {
  id                  Int             @id @default(autoincrement())
  operationNumber     String          @unique
  warehouseId         Int
  employeeId          Int
  operationType       String          @default("full")
  startDate           DateTime        @default(now())
  endDate             DateTime?
  expectedQuantity    Int?
  actualQuantity      Int?
  discrepancyQuantity Int?
  notes               String?
  status              String          @default("in_progress")
  completedAt         DateTime?
  createdAt           DateTime        @default(now())
  updatedAt           DateTime        @default(now()) @updatedAt
  items               StocktakeItem[]
  employee            User            @relation(fields: [employeeId], references: [id])
  warehouse           Warehouse       @relation(fields: [warehouseId], references: [id])

  @@map("stocktake_operations")
}

model StocktakeItem {
  id               Int                @id @default(autoincrement())
  stocktakeId      Int
  deviceId         String
  expectedStatus   String?
  actualStatus     String
  isFound          Boolean            @default(true)
  condition        String?
  discrepancyType  String?
  previousLocation String?
  currentLocation  String?
  notes            String?
  checkedAt        DateTime           @default(now())
  device           Device             @relation(fields: [deviceId], references: [id])
  stocktake        StocktakeOperation @relation(fields: [stocktakeId], references: [id], onDelete: Cascade)

  @@map("stocktake_items")
}

model EmployeeRequest {
  id                 Int               @id @default(autoincrement())
  requestNumber      String            @unique
  requestType        String
  category           String            @default("general")
  priority           String            @default("normal")
  title              String
  description        String
  requestedChanges   Json?
  justification      String?
  expectedImpact     String?
  attachments        Json?
  relatedOrderType   String?
  relatedOrderId     Int?
  relatedOrderNumber String?
  employeeId         Int
  assignedTo         Int?
  status             String            @default("submitted")
  adminNotes         String?
  rejectionReason    String?
  completionNotes    String?
  requestDate        DateTime          @default(now())
  reviewDate         DateTime?
  responseDate       DateTime?
  completionDate     DateTime?
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @default(now()) @updatedAt
  assignee           User?             @relation("AssignedRequests", fields: [assignedTo], references: [id])
  employee           User              @relation(fields: [employeeId], references: [id])
  messages           InternalMessage[]

  @@map("employee_requests")
}

model InternalMessage {
  id                Int               @id @default(autoincrement())
  threadId          String
  messageType       String            @default("general")
  senderId          Int
  recipientId       Int?
  recipientType     String            @default("user")
  recipientIds      Json?
  subject           String?
  content           String
  attachments       Json?
  priority          String            @default("normal")
  isRead            Boolean           @default(false)
  readAt            DateTime?
  parentMessageId   Int?
  employeeRequestId Int?
  relatedOrderType  String?
  relatedOrderId    Int?
  status            String            @default("sent")
  resolutionNote    String?
  expiryDate        DateTime?
  sentAt            DateTime          @default(now())
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @default(now()) @updatedAt
  employeeRequest   EmployeeRequest?  @relation(fields: [employeeRequestId], references: [id])
  parentMessage     InternalMessage?  @relation("MessageReplies", fields: [parentMessageId], references: [id])
  replies           InternalMessage[] @relation("MessageReplies")
  recipient         User?             @relation("ReceivedMessages", fields: [recipientId], references: [id])
  sender            User              @relation("SentMessages", fields: [senderId], references: [id])

  @@map("internal_messages")
}

model MaintenanceLog {
  id                  Int       @id @default(autoincrement())
  deviceId            String
  maintenanceType     String
  performedBy         String
  maintenanceDate     DateTime  @default(now())
  description         String
  partsUsed           String?
  cost                Float?
  warrantyPeriod      Int?
  beforeCondition     String?
  afterCondition      String
  notes               String?
  status              String    @default("completed")
  nextMaintenanceDate DateTime?
  acknowledgedBy      Int?
  acknowledgedAt      DateTime?
  createdAt           DateTime  @default(now())
  acknowledger        User?     @relation("AcknowledgedMaintenance", fields: [acknowledgedBy], references: [id])
  device              Device    @relation(fields: [deviceId], references: [id])

  @@map("maintenance_logs")
}

model DatabaseConnection {
  id        Int              @id @default(autoincrement())
  name      String           @unique
  host      String
  port      Int              @default(5432)
  database  String
  username  String
  password  String
  isActive  Boolean          @default(false)
  isDefault Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  backups   DatabaseBackup[]
  databases Database[]

  @@map("database_connections")
}

model DatabaseBackup {
  id           Int                @id @default(autoincrement())
  name         String
  description  String?
  filePath     String
  fileSize     String
  backupType   String             @default("manual")
  status       String             @default("completed")
  createdBy    String?
  createdAt    DateTime           @default(now())
  connectionId Int
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@map("database_backups")
}

model Database {
  id           Int                @id @default(autoincrement())
  name         String
  connectionId Int
  owner        String             @default("")
  template     String             @default("template0")
  encoding     String             @default("UTF8")
  createdBy    Int
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @default(now()) @updatedAt
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  creator      User               @relation(fields: [createdBy], references: [id])

  @@unique([name, connectionId])
  @@map("databases")
}
