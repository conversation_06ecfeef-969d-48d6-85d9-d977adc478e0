generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AuditLog {
  id        Int      @id @default(autoincrement())
  timestamp DateTime @default(now())
  userId    Int
  username  String
  operation String
  tableName String?
  recordId  String?
  oldValues Json?
  newValues Json?
  details   String
  ipAddress String?
  userAgent String?
  users     users    @relation(fields: [userId], references: [id])

  @@index([operation])
  @@index([timestamp])
  @@index([userId])
}

model Client {
  id                 Int                  @id @default(autoincrement())
  name               String
  nameEn             String?
  code               String?              @unique
  phone              String?
  email              String?
  address            String?
  idNumber           String?
  taxNumber          String?
  type               String               @default("individual")
  status             String               @default("Active")
  notes              String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime
  delivery_orders    delivery_orders[]
  maintenance_orders maintenance_orders[]
  returns            returns[]
  sales              sales[]
}

model Device {
  id                                                    String                            @id
  barcode                                               String?                           @unique
  manufacturerId                                        Int
  deviceModelId                                         Int
  warehouseId                                           Int
  supplierId                                            Int?
  status                                                String                            @default("في المخزن")
  condition                                             String                            @default("جديد")
  grade                                                 String?
  storage                                               String?
  color                                                 String?
  price                                                 Float?
  costPrice                                             Float?
  warrantyPeriod                                        Int?
  purchaseDate                                          DateTime?
  lastMaintenanceDate                                   DateTime?
  notes                                                 String?
  isActive                                              Boolean                           @default(true)
  createdAt                                             DateTime                          @default(now())
  updatedAt                                             DateTime                          @default(now())
  DeviceModel                                           DeviceModel                       @relation(fields: [deviceModelId], references: [id])
  Manufacturer                                          Manufacturer                      @relation(fields: [manufacturerId], references: [id])
  Supplier                                              Supplier?                         @relation(fields: [supplierId], references: [id])
  Warehouse                                             Warehouse                         @relation(fields: [warehouseId], references: [id])
  delivery_order_items                                  delivery_order_items[]
  device_movements                                      device_movements[]
  evaluation_order_items                                evaluation_order_items[]
  maintenance_logs                                      maintenance_logs[]
  maintenance_order_items                               maintenance_order_items[]
  maintenance_receipt_order_items                       maintenance_receipt_order_items[]
  return_items_return_items_deviceIdToDevice            return_items[]                    @relation("return_items_deviceIdToDevice")
  return_items_return_items_replacementDeviceIdToDevice return_items[]                    @relation("return_items_replacementDeviceIdToDevice")
  sale_items                                            sale_items[]
  stocktake_items                                       stocktake_items[]
  supply_order_items                                    supply_order_items[]
  warehouse_transfer_items                              warehouse_transfer_items[]

  @@index([deviceModelId])
  @@index([manufacturerId])
  @@index([status])
  @@index([warehouseId])
}

model DeviceModel {
  id              Int               @id @default(autoincrement())
  name            String
  nameEn          String?
  manufacturerId  Int
  category        String            @default("هاتف ذكي")
  categoryEn      String            @default("Smartphone")
  specifications  Json?
  status          String            @default("Active")
  createdAt       DateTime          @default(now())
  updatedAt       DateTime
  Device          Device[]
  Manufacturer    Manufacturer      @relation(fields: [manufacturerId], references: [id])
  warehouse_stock warehouse_stock[]

  @@unique([name, manufacturerId])
}

model Manufacturer {
  id              Int               @id @default(autoincrement())
  name            String            @unique
  nameEn          String?
  code            String?           @unique
  status          String            @default("Active")
  createdAt       DateTime          @default(now())
  updatedAt       DateTime
  Device          Device[]
  DeviceModel     DeviceModel[]
  warehouse_stock warehouse_stock[]
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  authorId  Int
  users     users   @relation(fields: [authorId], references: [id])
}

model Supplier {
  id               Int                @id @default(autoincrement())
  name             String             @unique
  nameEn           String?
  code             String?            @unique
  phone            String?
  email            String?
  address          String?
  contactPerson    String?
  taxNumber        String?
  status           String             @default("Active")
  notes            String?
  createdAt        DateTime           @default(now())
  updatedAt        DateTime
  Device           Device[]
  SupplyOrderDraft SupplyOrderDraft[]
  supply_orders    supply_orders[]
}

model SupplyOrderDraft {
  id           Int        @id @default(autoincrement())
  draftId      String     @unique
  userId       Int
  supplierName String?
  supplierId   Int?
  warehouseId  Int?
  employeeName String
  supplyDate   DateTime
  notes        String?
  items        Json       @default("[]")
  attachments  Json       @default("[]")
  formState    Json       @default("{}")
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @default(now())
  Supplier     Supplier?  @relation(fields: [supplierId], references: [id])
  users        users      @relation(fields: [userId], references: [id])
  Warehouse    Warehouse? @relation(fields: [warehouseId], references: [id])
}

model SystemSetting {
  id            Int      @id @default(1)
  logoUrl       String   @default("")
  companyNameAr String   @default("")
  companyNameEn String   @default("")
  addressAr     String   @default("")
  addressEn     String   @default("")
  phone         String   @default("")
  email         String   @default("")
  website       String   @default("")
  footerTextAr  String   @default("")
  footerTextEn  String   @default("")
  updatedAt     DateTime @default(now())
  createdAt     DateTime @default(now())
}

model Warehouse {
  id                                                                 Int                          @id @default(autoincrement())
  name                                                               String                       @unique
  nameEn                                                             String?
  code                                                               String?                      @unique
  type                                                               String                       @default("main")
  location                                                           String
  address                                                            String?
  phone                                                              String?
  managerId                                                          Int?
  capacity                                                           Int?
  status                                                             String                       @default("Active")
  description                                                        String?
  createdAt                                                          DateTime                     @default(now())
  updatedAt                                                          DateTime                     @default(now())
  Device                                                             Device[]
  SupplyOrderDraft                                                   SupplyOrderDraft[]
  delivery_orders_delivery_orders_sourceWarehouseIdToWarehouse       delivery_orders[]            @relation("delivery_orders_sourceWarehouseIdToWarehouse")
  delivery_orders_delivery_orders_targetWarehouseIdToWarehouse       delivery_orders[]            @relation("delivery_orders_targetWarehouseIdToWarehouse")
  evaluation_orders                                                  evaluation_orders[]
  maintenance_orders                                                 maintenance_orders[]
  maintenance_receipt_orders                                         maintenance_receipt_orders[]
  returns                                                            returns[]
  sales                                                              sales[]
  stocktake_operations                                               stocktake_operations[]
  supply_orders                                                      supply_orders[]
  warehouse_stock                                                    warehouse_stock[]
  warehouse_transfers_warehouse_transfers_fromWarehouseIdToWarehouse warehouse_transfers[]        @relation("warehouse_transfers_fromWarehouseIdToWarehouse")
  warehouse_transfers_warehouse_transfers_toWarehouseIdToWarehouse   warehouse_transfers[]        @relation("warehouse_transfers_toWarehouseIdToWarehouse")
}

model database_backups {
  id                   Int                  @id @default(autoincrement())
  name                 String
  description          String?
  filePath             String
  fileSize             String
  backupType           String               @default("manual")
  status               String               @default("completed")
  createdBy            String?
  createdAt            DateTime             @default(now())
  connectionId         Int
  database_connections database_connections @relation(fields: [connectionId], references: [id], onDelete: Cascade)
}

model database_connections {
  id               Int                @id @default(autoincrement())
  name             String             @unique
  host             String
  port             Int                @default(5432)
  database         String
  username         String
  password         String
  isActive         Boolean            @default(false)
  isDefault        Boolean            @default(false)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime
  database_backups database_backups[]
  databases        databases[]
}

model databases {
  id                   Int                  @id @default(autoincrement())
  name                 String
  connectionId         Int
  owner                String               @default("")
  template             String               @default("template0")
  encoding             String               @default("UTF8")
  createdBy            Int
  createdAt            DateTime             @default(now())
  updatedAt            DateTime             @default(now())
  database_connections database_connections @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  users                users                @relation(fields: [createdBy], references: [id])

  @@unique([name, connectionId])
}

model delivery_order_items {
  id                Int             @id @default(autoincrement())
  deliveryOrderId   Int
  deviceId          String
  deliveryResult    String          @default("pending")
  condition         String?
  receivedCondition String?
  fault             String?
  damage            String?
  notes             String?
  deliveredAt       DateTime?
  delivery_orders   delivery_orders @relation(fields: [deliveryOrderId], references: [id], onDelete: Cascade)
  Device            Device          @relation(fields: [deviceId], references: [id])
}

model delivery_orders {
  id                                                     Int                    @id @default(autoincrement())
  deliveryNumber                                         String                 @unique
  sourceWarehouseId                                      Int
  targetWarehouseId                                      Int?
  clientId                                               Int?
  employeeId                                             Int
  deliveryDate                                           DateTime               @default(now())
  deliveryType                                           String                 @default("warehouse")
  totalQuantity                                          Int                    @default(0)
  deliveryAddress                                        String?
  driverName                                             String?
  vehicleInfo                                            String?
  notes                                                  String?
  attachments                                            Json?
  referenceNumber                                        String?
  status                                                 String                 @default("pending")
  deliveredAt                                            DateTime?
  receivedBy                                             String?
  createdAt                                              DateTime               @default(now())
  updatedAt                                              DateTime               @default(now())
  delivery_order_items                                   delivery_order_items[]
  Client                                                 Client?                @relation(fields: [clientId], references: [id])
  users                                                  users                  @relation(fields: [employeeId], references: [id])
  Warehouse_delivery_orders_sourceWarehouseIdToWarehouse Warehouse              @relation("delivery_orders_sourceWarehouseIdToWarehouse", fields: [sourceWarehouseId], references: [id])
  Warehouse_delivery_orders_targetWarehouseIdToWarehouse Warehouse?             @relation("delivery_orders_targetWarehouseIdToWarehouse", fields: [targetWarehouseId], references: [id])
}

model device_movements {
  id              Int      @id @default(autoincrement())
  deviceId        String
  movementType    String
  fromWarehouseId Int?
  toWarehouseId   Int?
  fromStatus      String?
  toStatus        String
  referenceType   String?
  referenceId     String?
  referenceNumber String?
  employeeId      Int
  employeeName    String
  notes           String?
  movementDate    DateTime @default(now())
  Device          Device   @relation(fields: [deviceId], references: [id])
  users           users    @relation(fields: [employeeId], references: [id])
}

model employee_requests {
  id                                        Int                 @id @default(autoincrement())
  requestNumber                             String              @unique
  requestType                               String
  category                                  String              @default("general")
  priority                                  String              @default("normal")
  title                                     String
  description                               String
  requestedChanges                          Json?
  justification                             String?
  expectedImpact                            String?
  attachments                               Json?
  relatedOrderType                          String?
  relatedOrderId                            Int?
  relatedOrderNumber                        String?
  employeeId                                Int
  assignedTo                                Int?
  status                                    String              @default("submitted")
  adminNotes                                String?
  rejectionReason                           String?
  completionNotes                           String?
  requestDate                               DateTime            @default(now())
  reviewDate                                DateTime?
  responseDate                              DateTime?
  completionDate                            DateTime?
  createdAt                                 DateTime            @default(now())
  updatedAt                                 DateTime            @default(now())
  users_employee_requests_assignedToTousers users?              @relation("employee_requests_assignedToTousers", fields: [assignedTo], references: [id])
  users_employee_requests_employeeIdTousers users               @relation("employee_requests_employeeIdTousers", fields: [employeeId], references: [id])
  internal_messages                         internal_messages[]
}

model evaluation_order_items {
  id                Int               @id @default(autoincrement())
  evaluationOrderId Int
  deviceId          String
  previousGrade     String?
  externalGrade     String
  screenGrade       String
  networkGrade      String
  batteryGrade      String?
  finalGrade        String
  recommendedPrice  Float?
  fault             String?
  damageType        String?
  isAccepted        Boolean           @default(true)
  rejectionReason   String?
  notes             String?
  evaluatedAt       DateTime          @default(now())
  Device            Device            @relation(fields: [deviceId], references: [id])
  evaluation_orders evaluation_orders @relation(fields: [evaluationOrderId], references: [id], onDelete: Cascade)
}

model evaluation_orders {
  id                                            Int                      @id @default(autoincrement())
  orderNumber                                   String                   @unique
  warehouseId                                   Int
  employeeId                                    Int
  evaluationDate                                DateTime                 @default(now())
  totalQuantity                                 Int                      @default(0)
  completedQuantity                             Int                      @default(0)
  notes                                         String?
  status                                        String                   @default("pending")
  acknowledgedBy                                Int?
  acknowledgedAt                                DateTime?
  createdAt                                     DateTime                 @default(now())
  updatedAt                                     DateTime                 @default(now())
  evaluation_order_items                        evaluation_order_items[]
  users_evaluation_orders_acknowledgedByTousers users?                   @relation("evaluation_orders_acknowledgedByTousers", fields: [acknowledgedBy], references: [id])
  users_evaluation_orders_employeeIdTousers     users                    @relation("evaluation_orders_employeeIdTousers", fields: [employeeId], references: [id])
  Warehouse                                     Warehouse                @relation(fields: [warehouseId], references: [id])
}

model internal_messages {
  id                                         Int                 @id @default(autoincrement())
  threadId                                   String
  messageType                                String              @default("general")
  senderId                                   Int
  recipientId                                Int?
  recipientType                              String              @default("user")
  recipientIds                               Json?
  subject                                    String?
  content                                    String
  attachments                                Json?
  priority                                   String              @default("normal")
  isRead                                     Boolean             @default(false)
  readAt                                     DateTime?
  parentMessageId                            Int?
  employeeRequestId                          Int?
  relatedOrderType                           String?
  relatedOrderId                             Int?
  status                                     String              @default("sent")
  resolutionNote                             String?
  expiryDate                                 DateTime?
  sentAt                                     DateTime            @default(now())
  createdAt                                  DateTime            @default(now())
  updatedAt                                  DateTime            @default(now())
  employee_requests                          employee_requests?  @relation(fields: [employeeRequestId], references: [id])
  internal_messages                          internal_messages?  @relation("internal_messagesTointernal_messages", fields: [parentMessageId], references: [id])
  other_internal_messages                    internal_messages[] @relation("internal_messagesTointernal_messages")
  users_internal_messages_recipientIdTousers users?              @relation("internal_messages_recipientIdTousers", fields: [recipientId], references: [id])
  users_internal_messages_senderIdTousers    users               @relation("internal_messages_senderIdTousers", fields: [senderId], references: [id])
}

model maintenance_logs {
  id                  Int       @id @default(autoincrement())
  deviceId            String
  maintenanceType     String
  performedBy         String
  maintenanceDate     DateTime  @default(now())
  description         String
  partsUsed           String?
  cost                Float?
  warrantyPeriod      Int?
  beforeCondition     String?
  afterCondition      String
  notes               String?
  status              String    @default("completed")
  nextMaintenanceDate DateTime?
  acknowledgedBy      Int?
  acknowledgedAt      DateTime?
  createdAt           DateTime  @default(now())
  users               users?    @relation(fields: [acknowledgedBy], references: [id])
  Device              Device    @relation(fields: [deviceId], references: [id])
}

model maintenance_order_items {
  id                 Int                @id @default(autoincrement())
  maintenanceOrderId Int
  deviceId           String
  reportedFault      String?
  deviceCondition    String?
  estimatedCost      Float?
  priority           String?            @default("normal")
  notes              String?
  sentAt             DateTime           @default(now())
  Device             Device             @relation(fields: [deviceId], references: [id])
  maintenance_orders maintenance_orders @relation(fields: [maintenanceOrderId], references: [id], onDelete: Cascade)
}

model maintenance_orders {
  id                         Int                          @id @default(autoincrement())
  orderNumber                String                       @unique
  warehouseId                Int?
  clientId                   Int?
  employeeId                 Int
  maintenanceProviderId      Int?
  maintenanceProviderName    String?
  orderDate                  DateTime                     @default(now())
  expectedReturnDate         DateTime?
  totalQuantity              Int                          @default(0)
  estimatedCost              Float?
  notes                      String?
  attachments                Json?
  referenceNumber            String?
  source                     String                       @default("warehouse")
  status                     String                       @default("sent")
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @default(now())
  maintenance_order_items    maintenance_order_items[]
  Client                     Client?                      @relation(fields: [clientId], references: [id])
  users                      users                        @relation(fields: [employeeId], references: [id])
  Warehouse                  Warehouse?                   @relation(fields: [warehouseId], references: [id])
  maintenance_receipt_orders maintenance_receipt_orders[]
}

model maintenance_receipt_order_items {
  id                         Int                        @id @default(autoincrement())
  maintenanceReceiptOrderId  Int
  deviceId                   String
  maintenanceResult          String
  repairDetails              String?
  fault                      String?
  partsReplaced              String?
  repairCost                 Float?
  warrantyPeriod             Int?
  newCondition               String?
  newGrade                   String?
  damage                     String?
  isSuccessful               Boolean                    @default(true)
  notes                      String?
  completedAt                DateTime                   @default(now())
  Device                     Device                     @relation(fields: [deviceId], references: [id])
  maintenance_receipt_orders maintenance_receipt_orders @relation(fields: [maintenanceReceiptOrderId], references: [id], onDelete: Cascade)
}

model maintenance_receipt_orders {
  id                              Int                               @id @default(autoincrement())
  receiptNumber                   String                            @unique
  maintenanceOrderId              Int
  warehouseId                     Int
  employeeId                      Int
  maintenanceProviderName         String?
  receiptDate                     DateTime                          @default(now())
  totalCost                       Float?                            @default(0)
  totalQuantity                   Int                               @default(0)
  notes                           String?
  attachments                     Json?
  referenceNumber                 String?
  status                          String                            @default("received")
  createdAt                       DateTime                          @default(now())
  updatedAt                       DateTime                          @default(now())
  maintenance_receipt_order_items maintenance_receipt_order_items[]
  users                           users                             @relation(fields: [employeeId], references: [id])
  maintenance_orders              maintenance_orders                @relation(fields: [maintenanceOrderId], references: [id])
  Warehouse                       Warehouse                         @relation(fields: [warehouseId], references: [id])
}

model return_items {
  id                                              Int      @id @default(autoincrement())
  returnId                                        Int
  deviceId                                        String
  returnReason                                    String
  deviceCondition                                 String
  isDefective                                     Boolean  @default(false)
  replacementDeviceId                             String?
  isReplacement                                   Boolean  @default(false)
  refundAmount                                    Float?   @default(0)
  notes                                           String?
  createdAt                                       DateTime @default(now())
  Device_return_items_deviceIdToDevice            Device   @relation("return_items_deviceIdToDevice", fields: [deviceId], references: [id])
  Device_return_items_replacementDeviceIdToDevice Device?  @relation("return_items_replacementDeviceIdToDevice", fields: [replacementDeviceId], references: [id])
  returns                                         returns  @relation(fields: [returnId], references: [id], onDelete: Cascade)
}

model returns {
  id                               Int            @id @default(autoincrement())
  returnNumber                     String         @unique
  saleId                           Int
  clientId                         Int
  warehouseId                      Int
  employeeId                       Int
  returnDate                       DateTime       @default(now())
  returnType                       String         @default("refund")
  totalAmount                      Float?         @default(0)
  totalQuantity                    Int            @default(0)
  refundAmount                     Float?         @default(0)
  processingFee                    Float?         @default(0)
  notes                            String?
  attachments                      Json?
  status                           String         @default("pending")
  processedBy                      Int?
  processedAt                      DateTime?
  createdAt                        DateTime       @default(now())
  updatedAt                        DateTime       @default(now())
  return_items                     return_items[]
  Client                           Client         @relation(fields: [clientId], references: [id])
  users_returns_employeeIdTousers  users          @relation("returns_employeeIdTousers", fields: [employeeId], references: [id])
  users_returns_processedByTousers users?         @relation("returns_processedByTousers", fields: [processedBy], references: [id])
  sales                            sales          @relation(fields: [saleId], references: [id])
  Warehouse                        Warehouse      @relation(fields: [warehouseId], references: [id])
}

model sale_items {
  id             Int      @id @default(autoincrement())
  saleId         Int
  deviceId       String
  unitPrice      Float
  finalPrice     Float
  discount       Float?   @default(0)
  warrantyPeriod String?
  notes          String?
  createdAt      DateTime @default(now())
  Device         Device   @relation(fields: [deviceId], references: [id])
  sales          sales    @relation(fields: [saleId], references: [id], onDelete: Cascade)
}

model sales {
  id             Int          @id @default(autoincrement())
  saleNumber     String       @unique
  opNumber       String?
  clientId       Int
  warehouseId    Int
  employeeId     Int
  saleDate       DateTime     @default(now())
  totalAmount    Float
  totalQuantity  Int          @default(0)
  discount       Float?       @default(0)
  tax            Float?       @default(0)
  netAmount      Float
  paymentMethod  String?      @default("cash")
  warrantyPeriod String?
  notes          String?
  attachments    Json?
  status         String       @default("completed")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @default(now())
  returns        returns[]
  sale_items     sale_items[]
  Client         Client       @relation(fields: [clientId], references: [id])
  users          users        @relation(fields: [employeeId], references: [id])
  Warehouse      Warehouse    @relation(fields: [warehouseId], references: [id])
}

model stocktake_items {
  id                   Int                  @id @default(autoincrement())
  stocktakeId          Int
  deviceId             String
  expectedStatus       String?
  actualStatus         String
  isFound              Boolean              @default(true)
  condition            String?
  discrepancyType      String?
  previousLocation     String?
  currentLocation      String?
  notes                String?
  checkedAt            DateTime             @default(now())
  Device               Device               @relation(fields: [deviceId], references: [id])
  stocktake_operations stocktake_operations @relation(fields: [stocktakeId], references: [id], onDelete: Cascade)
}

model stocktake_operations {
  id                  Int               @id @default(autoincrement())
  operationNumber     String            @unique
  warehouseId         Int
  employeeId          Int
  operationType       String            @default("full")
  startDate           DateTime          @default(now())
  endDate             DateTime?
  expectedQuantity    Int?
  actualQuantity      Int?
  discrepancyQuantity Int?
  notes               String?
  status              String            @default("in_progress")
  completedAt         DateTime?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @default(now())
  stocktake_items     stocktake_items[]
  users               users             @relation(fields: [employeeId], references: [id])
  Warehouse           Warehouse         @relation(fields: [warehouseId], references: [id])
}

model supply_order_items {
  id             Int           @id @default(autoincrement())
  supplyOrderId  Int
  deviceId       String
  manufacturerId Int
  deviceModelId  Int
  condition      String
  costPrice      Float?
  sellingPrice   Float?
  warrantyPeriod Int?
  notes          String?
  createdAt      DateTime      @default(now())
  Device         Device        @relation(fields: [deviceId], references: [id])
  supply_orders  supply_orders @relation(fields: [supplyOrderId], references: [id], onDelete: Cascade)
}

model supply_orders {
  id                 Int                  @id @default(autoincrement())
  orderNumber        String               @unique
  supplierId         Int
  warehouseId        Int
  employeeId         Int
  invoiceNumber      String?
  invoiceDate        DateTime?
  supplyDate         DateTime             @default(now())
  totalAmount        Float?
  totalQuantity      Int                  @default(0)
  notes              String?
  attachments        Json?
  referenceNumber    String?
  status             String               @default("draft")
  approvedBy         Int?
  approvedAt         DateTime?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @default(now())
  supply_order_items supply_order_items[]
  users              users                @relation(fields: [employeeId], references: [id])
  Supplier           Supplier             @relation(fields: [supplierId], references: [id])
  Warehouse          Warehouse            @relation(fields: [warehouseId], references: [id])
}

model users {
  id                                                        Int                          @id @default(autoincrement())
  email                                                     String                       @unique
  name                                                      String?
  username                                                  String?                      @unique @default("user")
  role                                                      String?                      @default("user")
  phone                                                     String?                      @default("")
  photo                                                     String?                      @default("")
  status                                                    String?                      @default("Active")
  lastLogin                                                 String?
  branchLocation                                            String?
  warehouseAccess                                           Json?
  permissions                                               Json?
  createdAt                                                 DateTime                     @default(now())
  updatedAt                                                 DateTime                     @default(now())
  AuditLog                                                  AuditLog[]
  Post                                                      Post[]
  SupplyOrderDraft                                          SupplyOrderDraft[]
  databases                                                 databases[]
  delivery_orders                                           delivery_orders[]
  device_movements                                          device_movements[]
  employee_requests_employee_requests_assignedToTousers     employee_requests[]          @relation("employee_requests_assignedToTousers")
  employee_requests_employee_requests_employeeIdTousers     employee_requests[]          @relation("employee_requests_employeeIdTousers")
  evaluation_orders_evaluation_orders_acknowledgedByTousers evaluation_orders[]          @relation("evaluation_orders_acknowledgedByTousers")
  evaluation_orders_evaluation_orders_employeeIdTousers     evaluation_orders[]          @relation("evaluation_orders_employeeIdTousers")
  internal_messages_internal_messages_recipientIdTousers    internal_messages[]          @relation("internal_messages_recipientIdTousers")
  internal_messages_internal_messages_senderIdTousers       internal_messages[]          @relation("internal_messages_senderIdTousers")
  maintenance_logs                                          maintenance_logs[]
  maintenance_orders                                        maintenance_orders[]
  maintenance_receipt_orders                                maintenance_receipt_orders[]
  returns_returns_employeeIdTousers                         returns[]                    @relation("returns_employeeIdTousers")
  returns_returns_processedByTousers                        returns[]                    @relation("returns_processedByTousers")
  sales                                                     sales[]
  stocktake_operations                                      stocktake_operations[]
  supply_orders                                             supply_orders[]
  warehouse_transfers_warehouse_transfers_employeeIdTousers warehouse_transfers[]        @relation("warehouse_transfers_employeeIdTousers")
  warehouse_transfers_warehouse_transfers_receivedByTousers warehouse_transfers[]        @relation("warehouse_transfers_receivedByTousers")
}

model warehouse_stock {
  id                Int          @id @default(autoincrement())
  warehouseId       Int
  manufacturerId    Int
  deviceModelId     Int
  condition         String
  totalQuantity     Int          @default(0)
  availableQuantity Int          @default(0)
  reservedQuantity  Int          @default(0)
  lastUpdated       DateTime     @default(now())
  DeviceModel       DeviceModel  @relation(fields: [deviceModelId], references: [id])
  Manufacturer      Manufacturer @relation(fields: [manufacturerId], references: [id])
  Warehouse         Warehouse    @relation(fields: [warehouseId], references: [id])

  @@unique([warehouseId, manufacturerId, deviceModelId, condition])
}

model warehouse_transfer_items {
  id                  Int                 @id @default(autoincrement())
  transferId          Int
  deviceId            String
  transferReason      String?
  condition           String?
  receivedCondition   String?
  notes               String?
  sentAt              DateTime            @default(now())
  receivedAt          DateTime?
  Device              Device              @relation(fields: [deviceId], references: [id])
  warehouse_transfers warehouse_transfers @relation(fields: [transferId], references: [id], onDelete: Cascade)
}

model warehouse_transfers {
  id                                                       Int                        @id @default(autoincrement())
  transferNumber                                           String                     @unique
  fromWarehouseId                                          Int
  toWarehouseId                                            Int
  employeeId                                               Int
  transferDate                                             DateTime                   @default(now())
  transferType                                             String                     @default("internal")
  totalQuantity                                            Int                        @default(0)
  reason                                                   String?
  notes                                                    String?
  attachments                                              Json?
  referenceNumber                                          String?
  status                                                   String                     @default("pending")
  sentAt                                                   DateTime?
  receivedAt                                               DateTime?
  receivedBy                                               Int?
  createdAt                                                DateTime                   @default(now())
  updatedAt                                                DateTime                   @default(now())
  warehouse_transfer_items                                 warehouse_transfer_items[]
  users_warehouse_transfers_employeeIdTousers              users                      @relation("warehouse_transfers_employeeIdTousers", fields: [employeeId], references: [id])
  Warehouse_warehouse_transfers_fromWarehouseIdToWarehouse Warehouse                  @relation("warehouse_transfers_fromWarehouseIdToWarehouse", fields: [fromWarehouseId], references: [id])
  users_warehouse_transfers_receivedByTousers              users?                     @relation("warehouse_transfers_receivedByTousers", fields: [receivedBy], references: [id])
  Warehouse_warehouse_transfers_toWarehouseIdToWarehouse   Warehouse                  @relation("warehouse_transfers_toWarehouseIdToWarehouse", fields: [toWarehouseId], references: [id])
}
