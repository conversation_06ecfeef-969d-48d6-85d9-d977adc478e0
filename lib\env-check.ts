/**
 * التحقق من متغيرات البيئة المطلوبة
 */

export function checkRequiredEnvVars() {
  const requiredVars = [
    'DATABASE_URL'
    // 'JWT_SECRET' - غير مطلوب مع النظام المبسط
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('Missing required environment variables:', missingVars);
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
}

// التحقق عند تحميل الملف
if (typeof window === 'undefined') { // فقط في server-side
  try {
    checkRequiredEnvVars();
  } catch (error) {
    console.error('Environment check failed:', error);
  }
}
