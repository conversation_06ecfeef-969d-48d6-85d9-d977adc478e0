// مسح cache المتصفح وإعادة تحميل البيانات
// يجب تشغيل هذا الكود في console المتصفح

console.log('🧹 مسح cache الواجهة الأمامية...');

// 1. مسح localStorage
localStorage.clear();
console.log('✅ تم مسح localStorage');

// 2. مسح sessionStorage  
sessionStorage.clear();
console.log('✅ تم مسح sessionStorage');

// 3. مسح cache المتصفح (إذا كان متاحاً)
if ('caches' in window) {
  caches.keys().then(function(names) {
    names.forEach(function(name) {
      caches.delete(name);
    });
  });
  console.log('✅ تم مسح cache المتصفح');
}

// 4. إعادة تحميل الصفحة بدون cache
console.log('🔄 إعادة تحميل الصفحة...');
window.location.reload(true);

console.log('✨ تم مسح جميع البيانات المحفوظة محلياً');
