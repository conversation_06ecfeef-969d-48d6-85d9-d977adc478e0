import { NextRequest, NextResponse } from 'next/server';
import { unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';

const ATTACHMENTS_DIR = path.join(process.cwd(), 'public', 'attachments');

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { fileName } = await request.json();

    if (!fileName) {
      return NextResponse.json({ error: 'اسم الملف مطلوب' }, { status: 400 });
    }

    // التحقق من أن اسم الملف آمن (لا يحتوي على مسارات خطيرة)
    if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      return NextResponse.json({ error: 'اسم ملف غير صالح' }, { status: 400 });
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      const filePath = path.join(ATTACHMENTS_DIR, fileName);

      if (!existsSync(filePath)) {
        throw new Error('File not found');
      }

      // حذف الملف من النظام
      await unlink(filePath);

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted file: ${fileName}`,
        tableName: 'attachment',
        recordId: fileName
      });

      return { success: true, message: 'تم حذف الملف بنجاح' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('خطأ في حذف الملف:', error);

    if (error instanceof Error && error.message === 'File not found') {
      return NextResponse.json({ error: 'الملف غير موجود' }, { status: 404 });
    }

    return NextResponse.json({ error: 'فشل في حذف الملف' }, { status: 500 });
  }
}
