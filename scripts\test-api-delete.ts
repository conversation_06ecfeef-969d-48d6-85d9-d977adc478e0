import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestSupplyOrderForDeletion() {
  try {
    console.log('إنشاء أمر توريد تجريبي لاختبار الحذف...\n');

    // إنشاء أمر توريد تجريبي
    const testOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: 'SUP-DELETE-TEST',
        supplierId: 1,
        supplyDate: new Date().toISOString(),
        warehouseId: 1,
        employeeName: 'Test User',
        status: 'pending',
        notes: 'أمر توريد تجريبي للحذف'
      }
    });

    console.log(`تم إنشاء أمر التوريد: ${testOrder.supplyOrderId} (ID: ${testOrder.id})`);

    // اختبار حذف الأمر باستخدام fetch API
    console.log('اختبار حذف الأمر عبر API...');
    
    try {
      // إنشاء token للتفويض
      const devToken = btoa('user:admin:admin'); // user:username:role
      
      const response = await fetch(`http://localhost:3000/api/supply/${testOrder.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${devToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`حالة الاستجابة: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ تم حذف الأمر بنجاح عبر API:', result);
      } else {
        const error = await response.text();
        console.log('❌ فشل في حذف الأمر عبر API:', error);
      }
    } catch (apiError) {
      console.log('❌ خطأ في الاتصال بـ API:', apiError);
      
      // إذا فشل API، احذف الأمر مباشرة من قاعدة البيانات
      console.log('حذف الأمر مباشرة من قاعدة البيانات...');
      await prisma.supplyOrder.delete({
        where: { id: testOrder.id }
      });
      console.log('تم حذف الأمر التجريبي');
    }

  } catch (error) {
    console.error('خطأ في اختبار الحذف:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestSupplyOrderForDeletion();
