const { default: fetch } = require('node-fetch');

async function testWarehouseFullFunctionality() {
  try {
    console.log('🧪 Testing complete warehouse functionality...\n');
    
    // 1. Test GET - fetch all warehouses
    console.log('1️⃣ Testing GET /api/warehouses-simple');
    const getResponse = await fetch('http://localhost:9005/api/warehouses-simple');
    
    if (getResponse.ok) {
      const result = await getResponse.json();
      const warehouses = result.data || result;
      console.log(`✅ Found ${warehouses.length} warehouses`);
      
      // Show first few warehouses
      warehouses.slice(0, 3).forEach(w => {
        console.log(`   - ${w.name} (${w.type}) - ${w.location}`);
      });
      
      if (warehouses.length > 3) {
        console.log(`   ... and ${warehouses.length - 3} more`);
      }
    } else {
      console.log('❌ Failed to fetch warehouses');
      return;
    }
    
    // 2. Test POST - create new warehouse
    console.log('\n2️⃣ Testing POST - Create new warehouse');
    const timestamp = Date.now();
    const newWarehouse = {
      name: `مخزن اختبار شامل ${timestamp}`,
      type: 'فرعي',
      location: `موقع اختبار شامل ${timestamp}`
    };
    
    const postResponse = await fetch('http://localhost:9005/api/warehouses-simple', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newWarehouse)
    });
    
    let createdWarehouse = null;
    if (postResponse.ok) {
      createdWarehouse = await postResponse.json();
      console.log(`✅ Created warehouse: ${createdWarehouse.name} (ID: ${createdWarehouse.id})`);
    } else {
      const error = await postResponse.text();
      console.log('❌ Failed to create warehouse:', error);
      return;
    }
    
    // 3. Test PUT - update warehouse
    console.log('\n3️⃣ Testing PUT - Update warehouse');
    const updatedData = {
      id: createdWarehouse.id,
      name: createdWarehouse.name + ' - محدث',
      type: 'رئيسي',
      location: createdWarehouse.location + ' - محدث'
    };
    
    const putResponse = await fetch('http://localhost:9005/api/warehouses-simple', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedData)
    });
    
    if (putResponse.ok) {
      const updatedWarehouse = await putResponse.json();
      console.log(`✅ Updated warehouse: ${updatedWarehouse.name}`);
    } else {
      const error = await putResponse.text();
      console.log('❌ Failed to update warehouse:', error);
    }
    
    // 4. Test search functionality
    console.log('\n4️⃣ Testing search functionality');
    const searchResponse = await fetch('http://localhost:9005/api/warehouses-simple?search=اختبار');
    
    if (searchResponse.ok) {
      const searchResult = await searchResponse.json();
      const searchWarehouses = searchResult.data || searchResult;
      console.log(`✅ Search found ${searchWarehouses.length} warehouses containing "اختبار"`);
    } else {
      console.log('❌ Search failed');
    }
    
    // 5. Test pagination
    console.log('\n5️⃣ Testing pagination');
    const paginationResponse = await fetch('http://localhost:9005/api/warehouses-simple?page=1&limit=3');
    
    if (paginationResponse.ok) {
      const paginationResult = await paginationResponse.json();
      if (paginationResult.pagination) {
        console.log(`✅ Pagination works: Page ${paginationResult.pagination.page}, Total: ${paginationResult.pagination.total}`);
      } else {
        console.log('⚠️ Pagination format not detected');
      }
    } else {
      console.log('❌ Pagination test failed');
    }
    
    // 6. Test DELETE - delete the test warehouse
    console.log('\n6️⃣ Testing DELETE - Delete test warehouse');
    const deleteResponse = await fetch(`http://localhost:9005/api/warehouses-simple?id=${createdWarehouse.id}`, {
      method: 'DELETE'
    });
    
    if (deleteResponse.ok) {
      console.log(`✅ Deleted test warehouse (ID: ${createdWarehouse.id})`);
    } else {
      const error = await deleteResponse.text();
      console.log('❌ Failed to delete warehouse:', error);
    }
    
    console.log('\n🎉 Warehouse functionality test completed!');
    
  } catch (error) {
    console.error('❌ Error during warehouse functionality test:', error);
  }
}

testWarehouseFullFunctionality();
