# 🛡️ دليل إصلاح نظام الصلاحيات - شامل ومفصل

## 📋 نظرة عامة

هذا الدليل يوضح كيفية تطبيق نظام الصلاحيات على جميع الصفحات غير المحمية في التطبيق. كل صفحة تحتاج إلى:

1. **حماية الصفحة الكاملة** باستخدام `PermissionGuard`
2. **حماية الأزرار والإجراءات** باستخدام `ActionGuard`
3. **التحقق من الصلاحيات في الكود** باستخدام `usePermission`

---

## 🚀 القالب العام للإصلاح

### الاستيرادات المطلوبة:
```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';
```

### البنية الأساسية:
```typescript
export default function PageName() {
  const { canCreate, canEdit, canDelete } = usePermission('pageKey');

  return (
    <PermissionGuard pageKey="pageKey">
      {/* محتوى الصفحة */}
      
      <ActionGuard pageKey="pageKey" action="create">
        <Button>إنشاء جديد</Button>
      </ActionGuard>
      
      <ActionGuard pageKey="pageKey" action="edit">
        <Button>تعديل</Button>
      </ActionGuard>
      
      <ActionGuard pageKey="pageKey" action="delete">
        <Button>حذف</Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

---

## 📊 الصفحات المطلوب إصلاحها (17 صفحة)

### 🔴 المرحلة 1: الصفحات الحرجة (أولوية عالية)

#### 1. صفحة المستخدمين - `/users/page.tsx`

**pageKey:** `users`

**الإصلاحات المطلوبة:**
- حماية الصفحة الكاملة
- حماية أزرار إضافة/تعديل/حذف المستخدمين
- حماية تعديل الصلاحيات

**الكود المطلوب إضافته:**

```typescript
// في بداية الملف - إضافة الاستيرادات
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

// داخل المكون
export default function UsersPage() {
  const { canCreate, canEdit, canDelete } = usePermission('users');

  return (
    <PermissionGuard pageKey="users">
      {/* المحتوى الحالي */}
      
      {/* حماية زر إضافة مستخدم جديد */}
      <ActionGuard pageKey="users" action="create">
        <Button onClick={handleAddUser}>
          إضافة مستخدم جديد
        </Button>
      </ActionGuard>
      
      {/* حماية أزرار التعديل في الجدول */}
      <ActionGuard pageKey="users" action="edit">
        <Button onClick={() => handleEditUser(user.id)}>
          تعديل
        </Button>
      </ActionGuard>
      
      {/* حماية أزرار الحذف */}
      <ActionGuard pageKey="users" action="delete">
        <Button onClick={() => handleDeleteUser(user.id)}>
          حذف
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

#### 2. صفحة الإعدادات - `/settings/page.tsx`

**pageKey:** `settings`

**الإصلاحات المطلوبة:**
- حماية الصفحة الكاملة
- حماية أزرار الحفظ والتحديث

**الكود المطلوب:**

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function SettingsPage() {
  return (
    <PermissionGuard pageKey="settings">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="settings" action="edit">
        <Button onClick={handleSaveSettings}>
          حفظ الإعدادات
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

#### 3. صفحة المبيعات - `/sales/page.tsx`

**pageKey:** `sales`

**الإصلاحات المطلوبة:**
- حماية الصفحة الكاملة
- حماية أزرار إنشاء/تعديل/حذف المبيعات

**الكود المطلوب:**

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

export default function SalesPage() {
  const { canCreate, canEdit, canDelete } = usePermission('sales');

  return (
    <PermissionGuard pageKey="sales">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="sales" action="create">
        <Button onClick={handleCreateSale}>
          إنشاء عملية بيع جديدة
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="sales" action="edit">
        <Button onClick={() => handleEditSale(sale.id)}>
          تعديل
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="sales" action="delete">
        <Button onClick={() => handleDeleteSale(sale.id)}>
          حذف
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

#### 4. صفحة المخزون - `/inventory/page.tsx`

**pageKey:** `inventory`

**الإصلاحات المطلوبة:**
- حماية الصفحة الكاملة
- حماية أزرار إضافة/تعديل الأجهزة

**الكود المطلوب:**

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function InventoryPage() {
  return (
    <PermissionGuard pageKey="inventory">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="inventory" action="create">
        <Button onClick={handleAddDevice}>
          إضافة جهاز جديد
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="inventory" action="edit">
        <Button onClick={() => handleEditDevice(device.id)}>
          تعديل
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

### 🟡 المرحلة 2: الصفحات المتوسطة

#### 5. صفحة التوريد - `/supply/page.tsx`

**pageKey:** `supply`

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function SupplyPage() {
  return (
    <PermissionGuard pageKey="supply">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="supply" action="create">
        <Button onClick={handleCreateSupplyOrder}>
          إنشاء أمر توريد جديد
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="supply" action="edit">
        <Button onClick={() => handleEditSupplyOrder(order.id)}>
          تعديل
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="supply" action="delete">
        <Button onClick={() => handleDeleteSupplyOrder(order.id)}>
          حذف
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

#### 6. صفحة المرتجعات - `/returns/page.tsx`

**pageKey:** `returns`

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

export default function ReturnsPage() {
  const { canAcceptWithoutWarranty } = usePermission('returns');

  return (
    <PermissionGuard pageKey="returns">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="returns" action="create">
        <Button onClick={handleCreateReturn}>
          إنشاء مرتجع جديد
        </Button>
      </ActionGuard>
      
      {/* صلاحية خاصة للمرتجعات */}
      {canAcceptWithoutWarranty && (
        <Button onClick={handleAcceptWithoutWarranty}>
          قبول بدون ضمان
        </Button>
      )}
    </PermissionGuard>
  );
}
```

#### 7. صفحة العملاء - `/clients/page.tsx`

**pageKey:** `clients`

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function ClientsPage() {
  return (
    <PermissionGuard pageKey="clients">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="clients" action="create">
        <Button onClick={handleAddClient}>
          إضافة عميل جديد
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="clients" action="edit">
        <Button onClick={() => handleEditClient(client.id)}>
          تعديل
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="clients" action="delete">
        <Button onClick={() => handleDeleteClient(client.id)}>
          حذف
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

#### 8. صفحة المخازن - `/warehouses/page.tsx`

**pageKey:** `warehouses`

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function WarehousesPage() {
  return (
    <PermissionGuard pageKey="warehouses">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="warehouses" action="create">
        <Button onClick={handleCreateWarehouse}>
          إنشاء مخزن جديد
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="warehouses" action="edit">
        <Button onClick={() => handleEditWarehouse(warehouse.id)}>
          تعديل
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="warehouses" action="delete">
        <Button onClick={() => handleDeleteWarehouse(warehouse.id)}>
          حذف
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

### 🟢 المرحلة 3: الصفحات التشغيلية

#### 9. صفحة التتبع - `/track/page.tsx`

**pageKey:** `track`

```typescript
import { PermissionGuard } from '@/components/PermissionGuard';

export default function TrackPage() {
  return (
    <PermissionGuard pageKey="track">
      {/* المحتوى الحالي - صفحة عرض فقط */}
    </PermissionGuard>
  );
}
```

#### 10. صفحة التقييم - `/grading/page.tsx`

**pageKey:** `grading`

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function GradingPage() {
  return (
    <PermissionGuard pageKey="grading">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="grading" action="create">
        <Button onClick={handleCreateEvaluation}>
          إنشاء تقييم جديد
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="grading" action="edit">
        <Button onClick={() => handleEditEvaluation(eval.id)}>
          تعديل التقييم
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

#### 11. صفحة الجرد - `/stocktaking/page.tsx`

**pageKey:** `stocktaking`

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function StocktakingPage() {
  return (
    <PermissionGuard pageKey="stocktaking">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="stocktaking" action="create">
        <Button onClick={handleStartStocktake}>
          بدء جرد جديد
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="stocktaking" action="edit">
        <Button onClick={() => handleEditStocktake(stocktake.id)}>
          تعديل الجرد
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

#### 12. صفحة التقارير - `/reports/page.tsx`

**pageKey:** `reports`

```typescript
import { PermissionGuard } from '@/components/PermissionGuard';

export default function ReportsPage() {
  return (
    <PermissionGuard pageKey="reports">
      {/* المحتوى الحالي - صفحة عرض وتصدير فقط */}
    </PermissionGuard>
  );
}
```

### 🔵 المرحلة 4: الصفحات الإضافية

#### 13. صفحة الرسائل - `/messaging/page.tsx`

**pageKey:** `messaging`

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

export default function MessagingPage() {
  const { canViewAll } = usePermission('messaging');

  return (
    <PermissionGuard pageKey="messaging">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="messaging" action="create">
        <Button onClick={handleSendMessage}>
          إرسال رسالة جديدة
        </Button>
      </ActionGuard>
      
      {/* صلاحية خاصة لعرض جميع الرسائل */}
      {canViewAll && (
        <div>
          {/* عرض جميع الرسائل */}
        </div>
      )}
    </PermissionGuard>
  );
}
```

#### 14. صفحة الطلبات - `/requests/page.tsx`

**pageKey:** `requests`

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

export default function RequestsPage() {
  return (
    <PermissionGuard pageKey="requests">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="requests" action="create">
        <Button onClick={handleCreateRequest}>
          إنشاء طلب جديد
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="requests" action="edit">
        <Button onClick={() => handleEditRequest(request.id)}>
          تعديل الطلب
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

---

## 🔧 إصلاحات إضافية مطلوبة

### الصفحات المتبقية:

#### 15. قبول الأجهزة - `/accept-devices/page.tsx`
**pageKey:** `acceptDevices`

#### 16. أداة التسعير - `/pricing/page.tsx`  
**pageKey:** `pricing`

#### 17. التحويل المخزني - `/warehouse-transfer/page.tsx`
**pageKey:** `warehouseTransfer`

---

## 📋 قائمة التحقق للتطبيق

### لكل صفحة:
- [ ] إضافة الاستيرادات المطلوبة
- [ ] لف المحتوى في `PermissionGuard`
- [ ] حماية جميع الأزرار بـ `ActionGuard`
- [ ] استخدام `usePermission` للصلاحيات الخاصة
- [ ] اختبار الصفحة مع مستخدمين مختلفين

### اختبار شامل:
- [ ] اختبار الوصول مع مستخدم بدون صلاحيات
- [ ] اختبار الوصول مع مستخدم بصلاحيات محدودة
- [ ] اختبار الوصول مع مستخدم بصلاحيات كاملة
- [ ] التأكد من ظهور رسائل الخطأ المناسبة

---

## 🚀 نصائح للتطبيق السريع

### 1. البدء بالصفحات الحرجة:
ابدأ بصفحات المستخدمين والإعدادات والمبيعات

### 2. استخدام البحث والاستبدال:
ابحث عن أنماط الأزرار واستبدلها بالنسخة المحمية

### 3. الاختبار المرحلي:
اختبر كل صفحة بعد إصلاحها مباشرة

### 4. التوثيق:
وثق أي تغييرات خاصة أو استثناءات

---

## ⚠️ تحذيرات مهمة

1. **لا تنس الاستيرادات**: تأكد من إضافة جميع الاستيرادات المطلوبة
2. **اختبر مع مستخدمين مختلفين**: تأكد من عمل النظام مع جميع مستويات الصلاحيات
3. **احتفظ بنسخة احتياطية**: قم بعمل backup قبل التطبيق
4. **راجع pageKey**: تأكد من استخدام المفتاح الصحيح لكل صفحة

---

هذا الدليل يوفر خطة شاملة لحماية جميع الصفحات في التطبيق. ابدأ بالصفحات الحرجة وانتقل تدريجياً للباقي.
