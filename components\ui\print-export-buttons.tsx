"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Printer, FileDown, FileText, Download } from 'lucide-react';
import { usePrintExport, PrintData, PrintExportOptions } from '@/hooks/usePrintExport';
import { cn } from '@/lib/utils';

interface PrintExportButtonsProps {
  data: PrintData;
  options?: PrintExportOptions;
  className?: string;
  variant?: 'default' | 'compact' | 'dropdown';
  showLabels?: boolean;
  disabled?: boolean;
}

export function PrintExportButtons({
  data,
  options = {},
  className,
  variant = 'default',
  showLabels = true,
  disabled = false
}: PrintExportButtonsProps) {
  const { printData, exportToPDF, isLoading, error } = usePrintExport();

  const handlePrint = () => {
    printData(data, options);
  };

  const handleExportPDF = () => {
    exportToPDF(data, options);
  };

  if (variant === 'compact') {
    return (
      <div className={cn("flex gap-2", className)}>
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrint}
          disabled={disabled || isLoading}
          title="طباعة"
        >
          <Printer className="h-4 w-4" />
          {showLabels && <span className="mr-2">طباعة</span>}
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleExportPDF}
          disabled={disabled || isLoading}
          title="تصدير PDF"
        >
          <FileDown className="h-4 w-4" />
          {showLabels && <span className="mr-2">PDF</span>}
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("flex gap-3", className)}>
      <Button
        variant="outline"
        onClick={handlePrint}
        disabled={disabled || isLoading}
        className="flex items-center gap-2"
      >
        <Printer className="h-4 w-4" />
        {showLabels && "طباعة"}
      </Button>
      
      <Button
        variant="outline"
        onClick={handleExportPDF}
        disabled={disabled || isLoading}
        className="flex items-center gap-2"
      >
        <FileDown className="h-4 w-4" />
        {showLabels && "تصدير PDF"}
      </Button>

      {error && (
        <div className="text-sm text-red-600 mt-2">
          {error}
        </div>
      )}
    </div>
  );
}

// مكون مبسط للطباعة السريعة
interface QuickPrintProps {
  title: string;
  data: Record<string, any>;
  fileName?: string;
  className?: string;
}

export function QuickPrint({ title, data, fileName, className }: QuickPrintProps) {
  const printData: PrintData = {
    title,
    sections: [
      {
        title: 'المعلومات',
        type: 'info',
        data
      }
    ]
  };

  return (
    <PrintExportButtons
      data={printData}
      options={{ fileName }}
      className={className}
      variant="compact"
    />
  );
}

// مكون للجداول
interface TablePrintProps {
  title: string;
  data: any[];
  columns: string[];
  fileName?: string;
  className?: string;
}

export function TablePrint({ title, data, columns, fileName, className }: TablePrintProps) {
  const printData: PrintData = {
    title,
    sections: [
      {
        title: 'البيانات',
        type: 'table',
        data,
        columns
      }
    ]
  };

  return (
    <PrintExportButtons
      data={printData}
      options={{ fileName }}
      className={className}
    />
  );
}

// مكون للتايم لاين
interface TimelinePrintProps {
  title: string;
  events: any[];
  fileName?: string;
  className?: string;
}

export function TimelinePrint({ title, events, fileName, className }: TimelinePrintProps) {
  const printData: PrintData = {
    title,
    sections: [
      {
        title: 'سجل الأحداث',
        type: 'timeline',
        data: events
      }
    ]
  };

  return (
    <PrintExportButtons
      data={printData}
      options={{ fileName }}
      className={className}
    />
  );
}

// مكون شامل للتقارير المعقدة
interface ReportPrintProps {
  title: string;
  subtitle?: string;
  info?: Record<string, any>;
  tableData?: { data: any[]; columns: string[]; title?: string };
  timelineData?: { events: any[]; title?: string };
  customSections?: Array<{
    title: string;
    content: string;
  }>;
  fileName?: string;
  className?: string;
}

export function ReportPrint({
  title,
  subtitle,
  info,
  tableData,
  timelineData,
  customSections,
  fileName,
  className
}: ReportPrintProps) {
  const sections: any[] = [];

  if (info) {
    sections.push({
      title: 'المعلومات الأساسية',
      type: 'info',
      data: info
    });
  }

  if (tableData) {
    sections.push({
      title: tableData.title || 'البيانات',
      type: 'table',
      data: tableData.data,
      columns: tableData.columns
    });
  }

  if (timelineData) {
    sections.push({
      title: timelineData.title || 'سجل الأحداث',
      type: 'timeline',
      data: timelineData.events
    });
  }

  if (customSections) {
    customSections.forEach(section => {
      sections.push({
        title: section.title,
        type: 'custom',
        data: section.content
      });
    });
  }

  const printData: PrintData = {
    title,
    subtitle,
    sections
  };

  return (
    <PrintExportButtons
      data={printData}
      options={{ fileName }}
      className={className}
    />
  );
}

// Hook مبسط للاستخدام المباشر
export function usePrintButton(data: PrintData, options?: PrintExportOptions) {
  const { printData, exportToPDF, isLoading, error } = usePrintExport();

  const PrintButton = ({ variant = 'outline', size = 'default', className }: any) => (
    <Button
      variant={variant}
      size={size}
      onClick={() => printData(data, options)}
      disabled={isLoading}
      className={cn("flex items-center gap-2", className)}
    >
      <Printer className="h-4 w-4" />
      طباعة
    </Button>
  );

  const ExportButton = ({ variant = 'outline', size = 'default', className }: any) => (
    <Button
      variant={variant}
      size={size}
      onClick={() => exportToPDF(data, options)}
      disabled={isLoading}
      className={cn("flex items-center gap-2", className)}
    >
      <FileDown className="h-4 w-4" />
      تصدير PDF
    </Button>
  );

  return {
    PrintButton,
    ExportButton,
    isLoading,
    error
  };
}
