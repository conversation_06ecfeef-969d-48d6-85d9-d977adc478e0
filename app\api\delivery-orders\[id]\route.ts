import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction } from '@/lib/transaction-utils';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const orderId = parseInt(params.id);

    if (!orderId || isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid delivery order ID' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if delivery order exists
      const existingOrder = await tx.deliveryOrder.findUnique({
        where: { id: orderId }
      });

      if (!existingOrder) {
        throw new Error('Delivery order not found');
      }

      // Parse items if they are stored as JSON string
      let items = [];
      if (existingOrder.items) {
        try {
          items = typeof existingOrder.items === 'string' 
            ? existingOrder.items 
            : existingOrder.items;
        } catch (error) {
          console.error('Error parsing delivery order items:', error);
          items = [];
        }
      }

      // Delete the delivery order
      await tx.deliveryOrder.delete({
        where: { id: orderId }
      });

      console.log(`Deleted delivery order: ${existingOrder.deliveryOrderNumber} by user: ${authResult.user!.username}`);

      return {
        success: true,
        deletedOrder: existingOrder,
        items: items
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete delivery order:', error);

    if (error instanceof Error && error.message === 'Delivery order not found') {
      return NextResponse.json({ error: 'Delivery order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete delivery order' }, { status: 500 });
  }
}
