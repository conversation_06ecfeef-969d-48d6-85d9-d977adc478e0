-- إنشاء جدول للمسودات منفصل عن الأوامر الفعلية
CREATE TABLE IF NOT EXISTS "SupplyOrderDraft" (
  "id" SERIAL PRIMARY KEY,
  "draftId" TEXT UNIQUE NOT NULL,
  "userId" INTEGER NOT NULL,
  "supplierName" TEXT,
  "supplierId" INTEGER REFERENCES "Supplier"("id"),
  "warehouseId" INTEGER REFERENCES "Warehouse"("id"),
  "employeeName" TEXT NOT NULL,
  "supplyDate" TEXT NOT NULL,
  "notes" TEXT,
  "items" JSONB DEFAULT '[]'::jsonb,
  "attachments" JSONB DEFAULT '[]'::jsonb,
  "formState" JSONB DEFAULT '{}'::jsonb,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء index للبحث السريع
CREATE INDEX IF NOT EXISTS "SupplyOrderDraft_userId_idx" ON "SupplyOrderDraft"("userId");
CREATE INDEX IF NOT EXISTS "SupplyOrderDraft_draftId_idx" ON "SupplyOrderDraft"("draftId");
