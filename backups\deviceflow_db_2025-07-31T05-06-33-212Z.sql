--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: AuditLog; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."AuditLog" (
    id integer NOT NULL,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "userId" integer NOT NULL,
    username text NOT NULL,
    operation text NOT NULL,
    details text NOT NULL
);


ALTER TABLE public."AuditLog" OWNER TO deviceflow_user;

--
-- Name: AuditLog_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."AuditLog_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."AuditLog_id_seq" OWNER TO deviceflow_user;

--
-- Name: AuditLog_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."AuditLog_id_seq" OWNED BY public."AuditLog".id;


--
-- Name: Client; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Client" (
    id integer NOT NULL,
    name text NOT NULL,
    phone text,
    email text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Client" OWNER TO deviceflow_user;

--
-- Name: Client_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Client_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Client_id_seq" OWNER TO deviceflow_user;

--
-- Name: Client_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Client_id_seq" OWNED BY public."Client".id;


--
-- Name: DeliveryOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."DeliveryOrder" (
    id integer NOT NULL,
    "deliveryOrderNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "warehouseId" integer NOT NULL,
    "warehouseName" text NOT NULL,
    "employeeName" text NOT NULL,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."DeliveryOrder" OWNER TO deviceflow_user;

--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."DeliveryOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."DeliveryOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."DeliveryOrder_id_seq" OWNED BY public."DeliveryOrder".id;


--
-- Name: Device; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Device" (
    id text NOT NULL,
    model text NOT NULL,
    status text NOT NULL,
    storage text NOT NULL,
    price double precision NOT NULL,
    condition text NOT NULL,
    "warehouseId" integer,
    "supplierId" integer,
    "dateAdded" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "replacementInfo" jsonb
);


ALTER TABLE public."Device" OWNER TO deviceflow_user;

--
-- Name: DeviceModel; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."DeviceModel" (
    id integer NOT NULL,
    name text NOT NULL,
    "manufacturerId" integer NOT NULL,
    category text DEFAULT 'هاتف ذكي'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."DeviceModel" OWNER TO deviceflow_user;

--
-- Name: DeviceModel_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."DeviceModel_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."DeviceModel_id_seq" OWNER TO deviceflow_user;

--
-- Name: DeviceModel_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."DeviceModel_id_seq" OWNED BY public."DeviceModel".id;


--
-- Name: MaintenanceOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."MaintenanceOrder" (
    id integer NOT NULL,
    "orderNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "employeeName" text NOT NULL,
    "maintenanceEmployeeId" integer,
    "maintenanceEmployeeName" text,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'wip'::text NOT NULL,
    source text DEFAULT 'warehouse'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."MaintenanceOrder" OWNER TO deviceflow_user;

--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."MaintenanceOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."MaintenanceOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."MaintenanceOrder_id_seq" OWNED BY public."MaintenanceOrder".id;


--
-- Name: MaintenanceReceiptOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."MaintenanceReceiptOrder" (
    id integer NOT NULL,
    "receiptNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "employeeName" text NOT NULL,
    "maintenanceEmployeeName" text,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."MaintenanceReceiptOrder" OWNER TO deviceflow_user;

--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."MaintenanceReceiptOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."MaintenanceReceiptOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."MaintenanceReceiptOrder_id_seq" OWNED BY public."MaintenanceReceiptOrder".id;


--
-- Name: Manufacturer; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Manufacturer" (
    id integer NOT NULL,
    name text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Manufacturer" OWNER TO deviceflow_user;

--
-- Name: Manufacturer_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Manufacturer_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Manufacturer_id_seq" OWNER TO deviceflow_user;

--
-- Name: Manufacturer_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Manufacturer_id_seq" OWNED BY public."Manufacturer".id;


--
-- Name: Post; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Post" (
    id integer NOT NULL,
    title text NOT NULL,
    content text,
    published boolean DEFAULT false NOT NULL,
    "authorId" integer NOT NULL
);


ALTER TABLE public."Post" OWNER TO deviceflow_user;

--
-- Name: Post_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Post_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Post_id_seq" OWNER TO deviceflow_user;

--
-- Name: Post_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Post_id_seq" OWNED BY public."Post".id;


--
-- Name: Return; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Return" (
    id integer NOT NULL,
    "roNumber" text NOT NULL,
    "opReturnNumber" text NOT NULL,
    date text NOT NULL,
    "saleId" integer NOT NULL,
    "soNumber" text NOT NULL,
    "clientName" text NOT NULL,
    "warehouseName" text NOT NULL,
    notes text,
    status text DEFAULT 'معلق'::text NOT NULL,
    "processedBy" text,
    "processedDate" text,
    "employeeName" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    attachments text
);


ALTER TABLE public."Return" OWNER TO deviceflow_user;

--
-- Name: Return_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Return_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Return_id_seq" OWNER TO deviceflow_user;

--
-- Name: Return_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Return_id_seq" OWNED BY public."Return".id;


--
-- Name: Sale; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Sale" (
    id integer NOT NULL,
    "soNumber" text NOT NULL,
    "opNumber" text NOT NULL,
    date text NOT NULL,
    "clientName" text NOT NULL,
    "warehouseName" text NOT NULL,
    notes text,
    "warrantyPeriod" text NOT NULL,
    "employeeName" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    attachments text
);


ALTER TABLE public."Sale" OWNER TO deviceflow_user;

--
-- Name: Sale_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Sale_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Sale_id_seq" OWNER TO deviceflow_user;

--
-- Name: Sale_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Sale_id_seq" OWNED BY public."Sale".id;


--
-- Name: Supplier; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Supplier" (
    id integer NOT NULL,
    name text NOT NULL,
    phone text,
    email text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Supplier" OWNER TO deviceflow_user;

--
-- Name: Supplier_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Supplier_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Supplier_id_seq" OWNER TO deviceflow_user;

--
-- Name: Supplier_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Supplier_id_seq" OWNED BY public."Supplier".id;


--
-- Name: SupplyOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."SupplyOrder" (
    id integer NOT NULL,
    "supplyOrderId" text NOT NULL,
    "supplierId" integer NOT NULL,
    "invoiceNumber" text,
    "supplyDate" text NOT NULL,
    "warehouseId" integer NOT NULL,
    "employeeName" text NOT NULL,
    notes text,
    "invoiceFileName" text,
    "referenceNumber" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    status text DEFAULT 'completed'::text
);


ALTER TABLE public."SupplyOrder" OWNER TO deviceflow_user;

--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."SupplyOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."SupplyOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."SupplyOrder_id_seq" OWNED BY public."SupplyOrder".id;


--
-- Name: SystemSetting; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."SystemSetting" (
    id integer DEFAULT 1 NOT NULL,
    "logoUrl" text DEFAULT ''::text NOT NULL,
    "companyNameAr" text DEFAULT ''::text NOT NULL,
    "companyNameEn" text DEFAULT ''::text NOT NULL,
    "addressAr" text DEFAULT ''::text NOT NULL,
    "addressEn" text DEFAULT ''::text NOT NULL,
    phone text DEFAULT ''::text NOT NULL,
    email text DEFAULT ''::text NOT NULL,
    website text DEFAULT ''::text NOT NULL,
    "footerTextAr" text DEFAULT ''::text NOT NULL,
    "footerTextEn" text DEFAULT ''::text NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."SystemSetting" OWNER TO deviceflow_user;

--
-- Name: Warehouse; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Warehouse" (
    id integer NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    location text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."Warehouse" OWNER TO deviceflow_user;

--
-- Name: Warehouse_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Warehouse_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Warehouse_id_seq" OWNER TO deviceflow_user;

--
-- Name: Warehouse_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Warehouse_id_seq" OWNED BY public."Warehouse".id;


--
-- Name: database_backups; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.database_backups (
    id integer NOT NULL,
    name text NOT NULL,
    description text,
    "filePath" text NOT NULL,
    "fileSize" text NOT NULL,
    "backupType" text DEFAULT 'manual'::text NOT NULL,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdBy" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "connectionId" integer NOT NULL
);


ALTER TABLE public.database_backups OWNER TO deviceflow_user;

--
-- Name: database_backups_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.database_backups_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.database_backups_id_seq OWNER TO deviceflow_user;

--
-- Name: database_backups_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.database_backups_id_seq OWNED BY public.database_backups.id;


--
-- Name: database_connections; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.database_connections (
    id integer NOT NULL,
    name text NOT NULL,
    host text NOT NULL,
    port integer DEFAULT 5432 NOT NULL,
    database text NOT NULL,
    username text NOT NULL,
    password text NOT NULL,
    "isActive" boolean DEFAULT false NOT NULL,
    "isDefault" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.database_connections OWNER TO deviceflow_user;

--
-- Name: database_connections_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.database_connections_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.database_connections_id_seq OWNER TO deviceflow_user;

--
-- Name: database_connections_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.database_connections_id_seq OWNED BY public.database_connections.id;


--
-- Name: databases; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.databases (
    id integer NOT NULL,
    name text NOT NULL,
    "connectionId" integer NOT NULL,
    owner text DEFAULT ''::text NOT NULL,
    template text DEFAULT 'template0'::text NOT NULL,
    encoding text DEFAULT 'UTF8'::text NOT NULL,
    "createdBy" integer NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.databases OWNER TO deviceflow_user;

--
-- Name: databases_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.databases_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.databases_id_seq OWNER TO deviceflow_user;

--
-- Name: databases_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.databases_id_seq OWNED BY public.databases.id;


--
-- Name: delivery_order_items; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.delivery_order_items (
    id integer NOT NULL,
    "deliveryOrderId" integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    result text NOT NULL,
    fault text,
    damage text,
    notes text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.delivery_order_items OWNER TO deviceflow_user;

--
-- Name: delivery_order_items_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.delivery_order_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.delivery_order_items_id_seq OWNER TO deviceflow_user;

--
-- Name: delivery_order_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.delivery_order_items_id_seq OWNED BY public.delivery_order_items.id;


--
-- Name: employee_requests; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.employee_requests (
    id integer NOT NULL,
    "requestNumber" text NOT NULL,
    "requestType" text NOT NULL,
    priority text NOT NULL,
    notes text NOT NULL,
    status text DEFAULT 'قيد المراجعة'::text NOT NULL,
    "requestDate" text NOT NULL,
    "employeeName" text NOT NULL,
    "employeeId" integer NOT NULL,
    "relatedOrderType" text,
    "relatedOrderId" integer,
    "relatedOrderDisplayId" text,
    "attachmentName" text,
    "adminNotes" text,
    "processedBy" integer,
    "processedDate" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.employee_requests OWNER TO deviceflow_user;

--
-- Name: employee_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.employee_requests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.employee_requests_id_seq OWNER TO deviceflow_user;

--
-- Name: employee_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.employee_requests_id_seq OWNED BY public.employee_requests.id;


--
-- Name: evaluation_order_items; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.evaluation_order_items (
    id integer NOT NULL,
    "evaluationOrderId" integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    "externalGrade" text NOT NULL,
    "screenGrade" text NOT NULL,
    "networkGrade" text NOT NULL,
    "finalGrade" text NOT NULL,
    fault text,
    "damageType" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.evaluation_order_items OWNER TO deviceflow_user;

--
-- Name: evaluation_order_items_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.evaluation_order_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.evaluation_order_items_id_seq OWNER TO deviceflow_user;

--
-- Name: evaluation_order_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.evaluation_order_items_id_seq OWNED BY public.evaluation_order_items.id;


--
-- Name: evaluation_orders; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.evaluation_orders (
    id integer NOT NULL,
    "orderId" text NOT NULL,
    "employeeName" text NOT NULL,
    date text NOT NULL,
    notes text,
    status text DEFAULT 'معلق'::text NOT NULL,
    "acknowledgedBy" text,
    "acknowledgedDate" text,
    "warehouseName" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.evaluation_orders OWNER TO deviceflow_user;

--
-- Name: evaluation_orders_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.evaluation_orders_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.evaluation_orders_id_seq OWNER TO deviceflow_user;

--
-- Name: evaluation_orders_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.evaluation_orders_id_seq OWNED BY public.evaluation_orders.id;


--
-- Name: internal_messages; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.internal_messages (
    id integer NOT NULL,
    "threadId" integer NOT NULL,
    "senderId" integer NOT NULL,
    "senderName" text NOT NULL,
    "recipientId" integer NOT NULL,
    "recipientName" text NOT NULL,
    "recipientIds" jsonb,
    text text NOT NULL,
    "attachmentName" text,
    "attachmentContent" text,
    "attachmentType" text,
    "attachmentUrl" text,
    "attachmentFileName" text,
    "attachmentSize" integer,
    "sentDate" text NOT NULL,
    status text DEFAULT 'مرسلة'::text NOT NULL,
    "isRead" boolean DEFAULT false NOT NULL,
    "parentMessageId" integer,
    "employeeRequestId" integer,
    "resolutionNote" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.internal_messages OWNER TO deviceflow_user;

--
-- Name: internal_messages_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.internal_messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.internal_messages_id_seq OWNER TO deviceflow_user;

--
-- Name: internal_messages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.internal_messages_id_seq OWNED BY public.internal_messages.id;


--
-- Name: maintenance_logs; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.maintenance_logs (
    id integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    "repairDate" text NOT NULL,
    notes text,
    result text,
    status text DEFAULT 'pending'::text NOT NULL,
    "acknowledgedDate" text,
    "warehouseName" text,
    "acknowledgedBy" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.maintenance_logs OWNER TO deviceflow_user;

--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.maintenance_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.maintenance_logs_id_seq OWNER TO deviceflow_user;

--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.maintenance_logs_id_seq OWNED BY public.maintenance_logs.id;


--
-- Name: maintenance_order_items; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.maintenance_order_items (
    id integer NOT NULL,
    "maintenanceOrderId" integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    fault text,
    notes text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.maintenance_order_items OWNER TO deviceflow_user;

--
-- Name: maintenance_order_items_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.maintenance_order_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.maintenance_order_items_id_seq OWNER TO deviceflow_user;

--
-- Name: maintenance_order_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.maintenance_order_items_id_seq OWNED BY public.maintenance_order_items.id;


--
-- Name: maintenance_receipt_order_items; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.maintenance_receipt_order_items (
    id integer NOT NULL,
    "maintenanceReceiptOrderId" integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    result text NOT NULL,
    fault text,
    damage text,
    notes text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.maintenance_receipt_order_items OWNER TO deviceflow_user;

--
-- Name: maintenance_receipt_order_items_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.maintenance_receipt_order_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.maintenance_receipt_order_items_id_seq OWNER TO deviceflow_user;

--
-- Name: maintenance_receipt_order_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.maintenance_receipt_order_items_id_seq OWNED BY public.maintenance_receipt_order_items.id;


--
-- Name: return_items; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.return_items (
    id integer NOT NULL,
    "returnId" integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    "returnReason" text NOT NULL,
    "replacementDeviceId" text,
    "isReplacement" boolean DEFAULT false NOT NULL,
    "originalDeviceId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.return_items OWNER TO deviceflow_user;

--
-- Name: return_items_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.return_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.return_items_id_seq OWNER TO deviceflow_user;

--
-- Name: return_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.return_items_id_seq OWNED BY public.return_items.id;


--
-- Name: sale_items; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.sale_items (
    id integer NOT NULL,
    "saleId" integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    price double precision NOT NULL,
    condition text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.sale_items OWNER TO deviceflow_user;

--
-- Name: sale_items_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.sale_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.sale_items_id_seq OWNER TO deviceflow_user;

--
-- Name: sale_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.sale_items_id_seq OWNED BY public.sale_items.id;


--
-- Name: supply_order_items; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.supply_order_items (
    id integer NOT NULL,
    "supplyOrderId" integer NOT NULL,
    imei text NOT NULL,
    model text NOT NULL,
    manufacturer text NOT NULL,
    condition text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.supply_order_items OWNER TO deviceflow_user;

--
-- Name: supply_order_items_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.supply_order_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.supply_order_items_id_seq OWNER TO deviceflow_user;

--
-- Name: supply_order_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.supply_order_items_id_seq OWNED BY public.supply_order_items.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.users (
    id integer NOT NULL,
    email text NOT NULL,
    name text,
    username text DEFAULT 'user'::text,
    role text DEFAULT 'user'::text,
    phone text DEFAULT ''::text,
    photo text DEFAULT ''::text,
    status text DEFAULT 'Active'::text,
    "lastLogin" text,
    "branchLocation" text,
    "warehouseAccess" jsonb,
    permissions jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.users OWNER TO deviceflow_user;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO deviceflow_user;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: AuditLog id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."AuditLog" ALTER COLUMN id SET DEFAULT nextval('public."AuditLog_id_seq"'::regclass);


--
-- Name: Client id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Client" ALTER COLUMN id SET DEFAULT nextval('public."Client_id_seq"'::regclass);


--
-- Name: DeliveryOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeliveryOrder" ALTER COLUMN id SET DEFAULT nextval('public."DeliveryOrder_id_seq"'::regclass);


--
-- Name: DeviceModel id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeviceModel" ALTER COLUMN id SET DEFAULT nextval('public."DeviceModel_id_seq"'::regclass);


--
-- Name: MaintenanceOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceOrder" ALTER COLUMN id SET DEFAULT nextval('public."MaintenanceOrder_id_seq"'::regclass);


--
-- Name: MaintenanceReceiptOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceReceiptOrder" ALTER COLUMN id SET DEFAULT nextval('public."MaintenanceReceiptOrder_id_seq"'::regclass);


--
-- Name: Manufacturer id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Manufacturer" ALTER COLUMN id SET DEFAULT nextval('public."Manufacturer_id_seq"'::regclass);


--
-- Name: Post id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post" ALTER COLUMN id SET DEFAULT nextval('public."Post_id_seq"'::regclass);


--
-- Name: Return id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Return" ALTER COLUMN id SET DEFAULT nextval('public."Return_id_seq"'::regclass);


--
-- Name: Sale id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Sale" ALTER COLUMN id SET DEFAULT nextval('public."Sale_id_seq"'::regclass);


--
-- Name: Supplier id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Supplier" ALTER COLUMN id SET DEFAULT nextval('public."Supplier_id_seq"'::regclass);


--
-- Name: SupplyOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SupplyOrder" ALTER COLUMN id SET DEFAULT nextval('public."SupplyOrder_id_seq"'::regclass);


--
-- Name: Warehouse id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Warehouse" ALTER COLUMN id SET DEFAULT nextval('public."Warehouse_id_seq"'::regclass);


--
-- Name: database_backups id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups ALTER COLUMN id SET DEFAULT nextval('public.database_backups_id_seq'::regclass);


--
-- Name: database_connections id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_connections ALTER COLUMN id SET DEFAULT nextval('public.database_connections_id_seq'::regclass);


--
-- Name: databases id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.databases ALTER COLUMN id SET DEFAULT nextval('public.databases_id_seq'::regclass);


--
-- Name: delivery_order_items id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.delivery_order_items ALTER COLUMN id SET DEFAULT nextval('public.delivery_order_items_id_seq'::regclass);


--
-- Name: employee_requests id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.employee_requests ALTER COLUMN id SET DEFAULT nextval('public.employee_requests_id_seq'::regclass);


--
-- Name: evaluation_order_items id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.evaluation_order_items ALTER COLUMN id SET DEFAULT nextval('public.evaluation_order_items_id_seq'::regclass);


--
-- Name: evaluation_orders id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.evaluation_orders ALTER COLUMN id SET DEFAULT nextval('public.evaluation_orders_id_seq'::regclass);


--
-- Name: internal_messages id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.internal_messages ALTER COLUMN id SET DEFAULT nextval('public.internal_messages_id_seq'::regclass);


--
-- Name: maintenance_logs id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_logs ALTER COLUMN id SET DEFAULT nextval('public.maintenance_logs_id_seq'::regclass);


--
-- Name: maintenance_order_items id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_order_items ALTER COLUMN id SET DEFAULT nextval('public.maintenance_order_items_id_seq'::regclass);


--
-- Name: maintenance_receipt_order_items id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_receipt_order_items ALTER COLUMN id SET DEFAULT nextval('public.maintenance_receipt_order_items_id_seq'::regclass);


--
-- Name: return_items id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.return_items ALTER COLUMN id SET DEFAULT nextval('public.return_items_id_seq'::regclass);


--
-- Name: sale_items id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.sale_items ALTER COLUMN id SET DEFAULT nextval('public.sale_items_id_seq'::regclass);


--
-- Name: supply_order_items id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.supply_order_items ALTER COLUMN id SET DEFAULT nextval('public.supply_order_items_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: AuditLog; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."AuditLog" (id, "timestamp", "userId", username, operation, details) FROM stdin;
1	2025-07-31 04:31:07.633	1	admin	UPDATE	Updated user: System Administrator (<EMAIL>)
2	2025-07-31 04:33:08.928	1	admin	CREATE	Created supply order: SUP-1
3	2025-07-31 04:40:46.702	1	admin	CREATE	Created supply order: SUP-API-TEST-1753936846363
4	2025-07-31 04:42:02.482	1	admin	CREATE	Created supply order: SUP-API-TEST-1753936922149
5	2025-07-31 04:42:03.907	1	admin	DELETE	Deleted supply order: SUP-API-TEST-1753936922149 and 1 devices
\.


--
-- Data for Name: Client; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Client" (id, name, phone, email, "createdAt", "updatedAt") FROM stdin;
1	222	\N	\N	2025-07-31 04:32:06.762	2025-07-31 04:32:06.762
\.


--
-- Data for Name: DeliveryOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."DeliveryOrder" (id, "deliveryOrderNumber", "referenceNumber", date, "warehouseId", "warehouseName", "employeeName", notes, "attachmentName", status, "createdAt") FROM stdin;
\.


--
-- Data for Name: Device; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Device" (id, model, status, storage, price, condition, "warehouseId", "supplierId", "dateAdded", "replacementInfo") FROM stdin;
222222222222222	Apple IPHONE 15 128GB	متاح للبيع	N/A	0	جديد	8	1	2025-07-31 04:33:08.923	\N
API-TEST-1753936846364	Apple iPhone 14 Pro	متاح للبيع	N/A	0	جديد	1	1	2025-07-31 04:40:46.7	\N
\.


--
-- Data for Name: DeviceModel; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."DeviceModel" (id, name, "manufacturerId", category, "createdAt", "updatedAt") FROM stdin;
1	iPhone 14	1	هاتف ذكي	2025-07-31 04:21:41.571	2025-07-31 04:21:41.571
2	iPhone 13	1	هاتف ذكي	2025-07-31 04:21:41.584	2025-07-31 04:21:41.584
3	Galaxy S23	2	هاتف ذكي	2025-07-31 04:21:41.586	2025-07-31 04:21:41.586
4	Galaxy A54	2	هاتف ذكي	2025-07-31 04:21:41.589	2025-07-31 04:21:41.589
5	موديل اختبار 1753935853086	9	هاتف ذكي	2025-07-31 04:24:13.54	2025-07-31 04:24:13.54
7	IPHONE 15 128GB	1	هاتف ذكي	2025-07-31 04:27:10.01	2025-07-31 04:27:10.01
8	موديل اختبار شامل 1753936059935	11	هاتف ذكي	2025-07-31 04:27:40.207	2025-07-31 04:27:40.207
\.


--
-- Data for Name: MaintenanceOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."MaintenanceOrder" (id, "orderNumber", "referenceNumber", date, "employeeName", "maintenanceEmployeeId", "maintenanceEmployeeName", notes, "attachmentName", status, source, "createdAt") FROM stdin;
\.


--
-- Data for Name: MaintenanceReceiptOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."MaintenanceReceiptOrder" (id, "receiptNumber", "referenceNumber", date, "employeeName", "maintenanceEmployeeName", notes, "attachmentName", status, "createdAt") FROM stdin;
\.


--
-- Data for Name: Manufacturer; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Manufacturer" (id, name, "createdAt", "updatedAt") FROM stdin;
1	Apple	2025-07-31 04:21:41.538	2025-07-31 04:21:41.538
2	Samsung	2025-07-31 04:21:41.544	2025-07-31 04:21:41.544
3	Huawei	2025-07-31 04:21:41.548	2025-07-31 04:21:41.548
4	Xiaomi	2025-07-31 04:21:41.552	2025-07-31 04:21:41.552
5	Oppo	2025-07-31 04:21:41.555	2025-07-31 04:21:41.555
6	Vivo	2025-07-31 04:21:41.558	2025-07-31 04:21:41.558
7	OnePlus	2025-07-31 04:21:41.56	2025-07-31 04:21:41.56
8	Realme	2025-07-31 04:21:41.562	2025-07-31 04:21:41.562
9	شركة اختبار 1753935852740	2025-07-31 04:24:13.058	2025-07-31 04:24:13.058
11	شركة اختبار شامل 1753936059369	2025-07-31 04:27:39.662	2025-07-31 04:27:39.662
\.


--
-- Data for Name: Post; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Post" (id, title, content, published, "authorId") FROM stdin;
\.


--
-- Data for Name: Return; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Return" (id, "roNumber", "opReturnNumber", date, "saleId", "soNumber", "clientName", "warehouseName", notes, status, "processedBy", "processedDate", "employeeName", "createdAt", attachments) FROM stdin;
\.


--
-- Data for Name: Sale; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Sale" (id, "soNumber", "opNumber", date, "clientName", "warehouseName", notes, "warrantyPeriod", "employeeName", "createdAt", attachments) FROM stdin;
\.


--
-- Data for Name: Supplier; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Supplier" (id, name, phone, email, "createdAt", "updatedAt") FROM stdin;
1	2222	\N	\N	2025-07-31 04:32:17.251	2025-07-31 04:32:17.251
\.


--
-- Data for Name: SupplyOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."SupplyOrder" (id, "supplyOrderId", "supplierId", "invoiceNumber", "supplyDate", "warehouseId", "employeeName", notes, "invoiceFileName", "referenceNumber", "createdAt", status) FROM stdin;
1	SUP-1	1	\N	2025-07-31T04:32	8	System Administrator		\N	\N	2025-07-31 04:33:08.905	completed
3	SUP-API-TEST-1753936846363	1	\N	2025-07-31T04:40:46.363Z	1	مستخدم اختبار API	أمر توريد اختبار API	\N	\N	2025-07-31 04:40:46.686	completed
\.


--
-- Data for Name: SystemSetting; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."SystemSetting" (id, "logoUrl", "companyNameAr", "companyNameEn", "addressAr", "addressEn", phone, email, website, "footerTextAr", "footerTextEn", "updatedAt", "createdAt") FROM stdin;
\.


--
-- Data for Name: Warehouse; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Warehouse" (id, name, type, location, "createdAt", "updatedAt") FROM stdin;
1	المخزن الرئيسي	رئيسي	المقر الرئيسي - صنعاء	2025-07-31 04:21:41.466	2025-07-31 04:21:41.466
2	مخزن الفرع الشمالي	فرعي	الفرع الشمالي - شارع الزبيري	2025-07-31 04:21:41.475	2025-07-31 04:21:41.475
5	omar albkri	رئيسي	yemen	2025-07-31 04:27:55.553	2025-07-31 04:27:55.553
6	albkri	رئيسي	yemen	2025-07-31 04:28:14.076	2025-07-31 04:28:14.076
7	Q2	رئيسي	yemen	2025-07-31 04:28:29.912	2025-07-31 04:28:29.912
8	66O	رئيسي	yemen	2025-07-31 04:29:17.73	2025-07-31 04:29:17.73
\.


--
-- Data for Name: database_backups; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.database_backups (id, name, description, "filePath", "fileSize", "backupType", status, "createdBy", "createdAt", "connectionId") FROM stdin;
\.


--
-- Data for Name: database_connections; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.database_connections (id, name, host, port, database, username, password, "isActive", "isDefault", "createdAt", "updatedAt") FROM stdin;
1	الاتصال الرئيسي	localhost	5432	deviceflow_db	deviceflow_user	om772828	t	t	2025-07-31 04:58:06.537	2025-07-31 05:05:16.438
\.


--
-- Data for Name: databases; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.databases (id, name, "connectionId", owner, template, encoding, "createdBy", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: delivery_order_items; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.delivery_order_items (id, "deliveryOrderId", "deviceId", model, result, fault, damage, notes, "createdAt") FROM stdin;
\.


--
-- Data for Name: employee_requests; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.employee_requests (id, "requestNumber", "requestType", priority, notes, status, "requestDate", "employeeName", "employeeId", "relatedOrderType", "relatedOrderId", "relatedOrderDisplayId", "attachmentName", "adminNotes", "processedBy", "processedDate", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: evaluation_order_items; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.evaluation_order_items (id, "evaluationOrderId", "deviceId", model, "externalGrade", "screenGrade", "networkGrade", "finalGrade", fault, "damageType", "createdAt") FROM stdin;
\.


--
-- Data for Name: evaluation_orders; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.evaluation_orders (id, "orderId", "employeeName", date, notes, status, "acknowledgedBy", "acknowledgedDate", "warehouseName", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: internal_messages; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.internal_messages (id, "threadId", "senderId", "senderName", "recipientId", "recipientName", "recipientIds", text, "attachmentName", "attachmentContent", "attachmentType", "attachmentUrl", "attachmentFileName", "attachmentSize", "sentDate", status, "isRead", "parentMessageId", "employeeRequestId", "resolutionNote", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: maintenance_logs; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.maintenance_logs (id, "deviceId", model, "repairDate", notes, result, status, "acknowledgedDate", "warehouseName", "acknowledgedBy", "createdAt") FROM stdin;
\.


--
-- Data for Name: maintenance_order_items; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.maintenance_order_items (id, "maintenanceOrderId", "deviceId", model, fault, notes, "createdAt") FROM stdin;
\.


--
-- Data for Name: maintenance_receipt_order_items; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.maintenance_receipt_order_items (id, "maintenanceReceiptOrderId", "deviceId", model, result, fault, damage, notes, "createdAt") FROM stdin;
\.


--
-- Data for Name: return_items; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.return_items (id, "returnId", "deviceId", model, "returnReason", "replacementDeviceId", "isReplacement", "originalDeviceId", "createdAt") FROM stdin;
\.


--
-- Data for Name: sale_items; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.sale_items (id, "saleId", "deviceId", model, price, condition, "createdAt") FROM stdin;
\.


--
-- Data for Name: supply_order_items; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.supply_order_items (id, "supplyOrderId", imei, model, manufacturer, condition, "createdAt") FROM stdin;
1	1	222222222222222	IPHONE 15 128GB	Apple	جديد	2025-07-31 04:33:08.913
3	3	API-TEST-1753936846364	iPhone 14 Pro	Apple	جديد	2025-07-31 04:40:46.692
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.users (id, email, name, username, role, phone, photo, status, "lastLogin", "branchLocation", "warehouseAccess", permissions, "createdAt", "updatedAt") FROM stdin;
1	<EMAIL>	System Administrator	admin	admin			Active	\N	\N	null	"{\\"dashboard\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"track\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"supply\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true},\\"acceptDevices\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"grading\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"inventory\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true},\\"sales\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true},\\"maintenance\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"maintenanceTransfer\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"warehouseTransfer\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"clients\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"pricing\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"returns\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"warehouses\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true},\\"users\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true},\\"reports\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"stocktaking\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"settings\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"requests\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"messaging\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false}}"	2025-07-31 04:24:12.382	2025-07-31 04:31:07.628
\.


--
-- Name: AuditLog_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."AuditLog_id_seq"', 5, true);


--
-- Name: Client_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Client_id_seq"', 1, true);


--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."DeliveryOrder_id_seq"', 1, false);


--
-- Name: DeviceModel_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."DeviceModel_id_seq"', 8, true);


--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."MaintenanceOrder_id_seq"', 1, false);


--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."MaintenanceReceiptOrder_id_seq"', 1, false);


--
-- Name: Manufacturer_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Manufacturer_id_seq"', 11, true);


--
-- Name: Post_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Post_id_seq"', 1, false);


--
-- Name: Return_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Return_id_seq"', 1, false);


--
-- Name: Sale_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Sale_id_seq"', 1, false);


--
-- Name: Supplier_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Supplier_id_seq"', 1, true);


--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."SupplyOrder_id_seq"', 4, true);


--
-- Name: Warehouse_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Warehouse_id_seq"', 8, true);


--
-- Name: database_backups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.database_backups_id_seq', 3, true);


--
-- Name: database_connections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.database_connections_id_seq', 1, true);


--
-- Name: databases_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.databases_id_seq', 1, false);


--
-- Name: delivery_order_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.delivery_order_items_id_seq', 1, false);


--
-- Name: employee_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.employee_requests_id_seq', 1, false);


--
-- Name: evaluation_order_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.evaluation_order_items_id_seq', 1, false);


--
-- Name: evaluation_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.evaluation_orders_id_seq', 1, false);


--
-- Name: internal_messages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.internal_messages_id_seq', 1, false);


--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.maintenance_logs_id_seq', 1, false);


--
-- Name: maintenance_order_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.maintenance_order_items_id_seq', 1, false);


--
-- Name: maintenance_receipt_order_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.maintenance_receipt_order_items_id_seq', 1, false);


--
-- Name: return_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.return_items_id_seq', 1, false);


--
-- Name: sale_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.sale_items_id_seq', 1, false);


--
-- Name: supply_order_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.supply_order_items_id_seq', 4, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.users_id_seq', 1, true);


--
-- Name: AuditLog AuditLog_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."AuditLog"
    ADD CONSTRAINT "AuditLog_pkey" PRIMARY KEY (id);


--
-- Name: Client Client_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Client"
    ADD CONSTRAINT "Client_pkey" PRIMARY KEY (id);


--
-- Name: DeliveryOrder DeliveryOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeliveryOrder"
    ADD CONSTRAINT "DeliveryOrder_pkey" PRIMARY KEY (id);


--
-- Name: DeviceModel DeviceModel_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeviceModel"
    ADD CONSTRAINT "DeviceModel_pkey" PRIMARY KEY (id);


--
-- Name: Device Device_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Device"
    ADD CONSTRAINT "Device_pkey" PRIMARY KEY (id);


--
-- Name: MaintenanceOrder MaintenanceOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceOrder"
    ADD CONSTRAINT "MaintenanceOrder_pkey" PRIMARY KEY (id);


--
-- Name: MaintenanceReceiptOrder MaintenanceReceiptOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceReceiptOrder"
    ADD CONSTRAINT "MaintenanceReceiptOrder_pkey" PRIMARY KEY (id);


--
-- Name: Manufacturer Manufacturer_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Manufacturer"
    ADD CONSTRAINT "Manufacturer_pkey" PRIMARY KEY (id);


--
-- Name: Post Post_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post"
    ADD CONSTRAINT "Post_pkey" PRIMARY KEY (id);


--
-- Name: Return Return_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Return"
    ADD CONSTRAINT "Return_pkey" PRIMARY KEY (id);


--
-- Name: Sale Sale_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Sale"
    ADD CONSTRAINT "Sale_pkey" PRIMARY KEY (id);


--
-- Name: Supplier Supplier_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Supplier"
    ADD CONSTRAINT "Supplier_pkey" PRIMARY KEY (id);


--
-- Name: SupplyOrder SupplyOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SupplyOrder"
    ADD CONSTRAINT "SupplyOrder_pkey" PRIMARY KEY (id);


--
-- Name: SystemSetting SystemSetting_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SystemSetting"
    ADD CONSTRAINT "SystemSetting_pkey" PRIMARY KEY (id);


--
-- Name: Warehouse Warehouse_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Warehouse"
    ADD CONSTRAINT "Warehouse_pkey" PRIMARY KEY (id);


--
-- Name: database_backups database_backups_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups
    ADD CONSTRAINT database_backups_pkey PRIMARY KEY (id);


--
-- Name: database_connections database_connections_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_connections
    ADD CONSTRAINT database_connections_pkey PRIMARY KEY (id);


--
-- Name: databases databases_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.databases
    ADD CONSTRAINT databases_pkey PRIMARY KEY (id);


--
-- Name: delivery_order_items delivery_order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.delivery_order_items
    ADD CONSTRAINT delivery_order_items_pkey PRIMARY KEY (id);


--
-- Name: employee_requests employee_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.employee_requests
    ADD CONSTRAINT employee_requests_pkey PRIMARY KEY (id);


--
-- Name: evaluation_order_items evaluation_order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.evaluation_order_items
    ADD CONSTRAINT evaluation_order_items_pkey PRIMARY KEY (id);


--
-- Name: evaluation_orders evaluation_orders_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.evaluation_orders
    ADD CONSTRAINT evaluation_orders_pkey PRIMARY KEY (id);


--
-- Name: internal_messages internal_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.internal_messages
    ADD CONSTRAINT internal_messages_pkey PRIMARY KEY (id);


--
-- Name: maintenance_logs maintenance_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_logs
    ADD CONSTRAINT maintenance_logs_pkey PRIMARY KEY (id);


--
-- Name: maintenance_order_items maintenance_order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_order_items
    ADD CONSTRAINT maintenance_order_items_pkey PRIMARY KEY (id);


--
-- Name: maintenance_receipt_order_items maintenance_receipt_order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_receipt_order_items
    ADD CONSTRAINT maintenance_receipt_order_items_pkey PRIMARY KEY (id);


--
-- Name: return_items return_items_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.return_items
    ADD CONSTRAINT return_items_pkey PRIMARY KEY (id);


--
-- Name: sale_items sale_items_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.sale_items
    ADD CONSTRAINT sale_items_pkey PRIMARY KEY (id);


--
-- Name: supply_order_items supply_order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.supply_order_items
    ADD CONSTRAINT supply_order_items_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: DeliveryOrder_deliveryOrderNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "DeliveryOrder_deliveryOrderNumber_key" ON public."DeliveryOrder" USING btree ("deliveryOrderNumber");


--
-- Name: DeviceModel_name_manufacturerId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "DeviceModel_name_manufacturerId_key" ON public."DeviceModel" USING btree (name, "manufacturerId");


--
-- Name: MaintenanceOrder_orderNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "MaintenanceOrder_orderNumber_key" ON public."MaintenanceOrder" USING btree ("orderNumber");


--
-- Name: MaintenanceReceiptOrder_receiptNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "MaintenanceReceiptOrder_receiptNumber_key" ON public."MaintenanceReceiptOrder" USING btree ("receiptNumber");


--
-- Name: Manufacturer_name_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Manufacturer_name_key" ON public."Manufacturer" USING btree (name);


--
-- Name: Return_roNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Return_roNumber_key" ON public."Return" USING btree ("roNumber");


--
-- Name: Sale_soNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Sale_soNumber_key" ON public."Sale" USING btree ("soNumber");


--
-- Name: SupplyOrder_supplyOrderId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "SupplyOrder_supplyOrderId_key" ON public."SupplyOrder" USING btree ("supplyOrderId");


--
-- Name: database_connections_name_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX database_connections_name_key ON public.database_connections USING btree (name);


--
-- Name: databases_name_connectionId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "databases_name_connectionId_key" ON public.databases USING btree (name, "connectionId");


--
-- Name: employee_requests_requestNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "employee_requests_requestNumber_key" ON public.employee_requests USING btree ("requestNumber");


--
-- Name: evaluation_orders_orderId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "evaluation_orders_orderId_key" ON public.evaluation_orders USING btree ("orderId");


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: users_username_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX users_username_key ON public.users USING btree (username);


--
-- Name: DeviceModel DeviceModel_manufacturerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeviceModel"
    ADD CONSTRAINT "DeviceModel_manufacturerId_fkey" FOREIGN KEY ("manufacturerId") REFERENCES public."Manufacturer"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Post Post_authorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post"
    ADD CONSTRAINT "Post_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: database_backups database_backups_connectionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups
    ADD CONSTRAINT "database_backups_connectionId_fkey" FOREIGN KEY ("connectionId") REFERENCES public.database_connections(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: databases databases_connectionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.databases
    ADD CONSTRAINT "databases_connectionId_fkey" FOREIGN KEY ("connectionId") REFERENCES public.database_connections(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: databases databases_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.databases
    ADD CONSTRAINT "databases_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: delivery_order_items delivery_order_items_deliveryOrderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.delivery_order_items
    ADD CONSTRAINT "delivery_order_items_deliveryOrderId_fkey" FOREIGN KEY ("deliveryOrderId") REFERENCES public."DeliveryOrder"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: evaluation_order_items evaluation_order_items_evaluationOrderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.evaluation_order_items
    ADD CONSTRAINT "evaluation_order_items_evaluationOrderId_fkey" FOREIGN KEY ("evaluationOrderId") REFERENCES public.evaluation_orders(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: maintenance_order_items maintenance_order_items_maintenanceOrderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_order_items
    ADD CONSTRAINT "maintenance_order_items_maintenanceOrderId_fkey" FOREIGN KEY ("maintenanceOrderId") REFERENCES public."MaintenanceOrder"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: maintenance_receipt_order_items maintenance_receipt_order_items_maintenanceReceiptOrderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_receipt_order_items
    ADD CONSTRAINT "maintenance_receipt_order_items_maintenanceReceiptOrderId_fkey" FOREIGN KEY ("maintenanceReceiptOrderId") REFERENCES public."MaintenanceReceiptOrder"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: return_items return_items_returnId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.return_items
    ADD CONSTRAINT "return_items_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES public."Return"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: sale_items sale_items_saleId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.sale_items
    ADD CONSTRAINT "sale_items_saleId_fkey" FOREIGN KEY ("saleId") REFERENCES public."Sale"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: supply_order_items supply_order_items_supplyOrderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.supply_order_items
    ADD CONSTRAINT "supply_order_items_supplyOrderId_fkey" FOREIGN KEY ("supplyOrderId") REFERENCES public."SupplyOrder"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO deviceflow_user;


--
-- PostgreSQL database dump complete
--

