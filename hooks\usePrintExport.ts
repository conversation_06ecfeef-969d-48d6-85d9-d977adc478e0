"use client";

import { useState } from 'react';
import { printDeviceData, printElement, exportDataToPDF, exportToCSV } from '@/lib/export-utils/html-to-pdf';
import { createArabicPDFWithCanvas } from '@/lib/export-utils/canvas-pdf';

// أنواع البيانات المدعومة
export interface PrintData {
  title: string;
  subtitle?: string;
  sections: PrintSection[];
  metadata?: Record<string, any>;
}

export interface PrintSection {
  title: string;
  type: 'info' | 'table' | 'timeline' | 'grid' | 'custom';
  data: any;
  columns?: string[];
  className?: string;
}

// خيارات الطباعة والتصدير
export interface PrintExportOptions {
  fileName?: string;
  title?: string;
  includeTimestamp?: boolean;
  pageSize?: 'A4' | 'A3' | 'Letter';
  orientation?: 'portrait' | 'landscape';
  template?: 'default' | 'invoice' | 'report' | 'list';
}

// Hook للطباعة والتصدير
export function usePrintExport() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // وظيفة عامة للطباعة
  const printData = async (
    data: PrintData,
    options: PrintExportOptions = {}
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const {
        fileName = 'report',
        title = data.title,
        includeTimestamp = true,
        template = 'default'
      } = options;

      // إنشاء HTML للطباعة
      const htmlContent = generatePrintHTML(data, { ...options, title });
      
      // طباعة مباشرة
      await printHTMLContent(htmlContent, title);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء الطباعة';
      setError(errorMessage);
      console.error('Print error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // وظيفة عامة لتصدير PDF
  const exportToPDF = async (
    data: PrintData,
    options: PrintExportOptions = {}
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const {
        fileName = 'report',
        title = data.title,
        template = 'default'
      } = options;

      // استخدام Canvas للحصول على أفضل نتائج مع العربية
      await exportWithCanvas(data, { ...options, fileName, title });
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء التصدير';
      setError(errorMessage);
      console.error('Export error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // وظيفة لتصدير CSV
  const exportToCSVFile = async (
    data: any[],
    headers: string[],
    fileName: string = 'data'
  ) => {
    try {
      setIsLoading(true);
      setError(null);
      
      exportToCSV(data, headers, fileName);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء تصدير CSV';
      setError(errorMessage);
      console.error('CSV export error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // وظيفة لطباعة عنصر موجود
  const printElementById = async (
    elementId: string,
    title: string = 'تقرير'
  ) => {
    try {
      setIsLoading(true);
      setError(null);
      
      printElement(elementId, title);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء الطباعة';
      setError(errorMessage);
      console.error('Element print error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    printData,
    exportToPDF,
    exportToCSVFile,
    printElementById,
    isLoading,
    error,
    clearError: () => setError(null)
  };
}

// وظائف مساعدة
function generatePrintHTML(data: PrintData, options: PrintExportOptions): string {
  const { title, includeTimestamp = true, template = 'default' } = options;
  
  let html = `
    <!DOCTYPE html>
    <html dir="rtl">
      <head>
        <meta charset="utf-8" />
        <title>${title}</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
          
          * { margin: 0; padding: 0; box-sizing: border-box; }
          
          body {
            font-family: 'Cairo', Arial, sans-serif !important;
            direction: rtl;
            background: white;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
            padding: 20px;
          }
          
          .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 15px;
          }
          
          .print-title {
            color: #1e293b;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
          }
          
          .print-subtitle {
            color: #64748b;
            font-size: 12px;
          }
          
          .section {
            margin-bottom: 25px;
            page-break-inside: avoid;
          }
          
          .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 5px;
          }
          
          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 15px;
          }
          
          .info-item {
            padding: 8px;
            background: #f8fafc;
            border-radius: 4px;
          }
          
          .info-label {
            font-weight: bold;
            color: #374151;
          }
          
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
          }
          
          th, td {
            border: 1px solid #e2e8f0;
            padding: 8px;
            text-align: right;
          }
          
          th {
            background: #f1f5f9;
            font-weight: bold;
          }
          
          .timeline-item {
            margin-bottom: 12px;
            padding: 10px;
            border-right: 3px solid #3b82f6;
            background: #f8fafc;
          }
          
          @media print {
            body { print-color-adjust: exact; -webkit-print-color-adjust: exact; }
            @page { size: A4; margin: 15mm; }
          }
        </style>
      </head>
      <body>
        <div class="print-header">
          <h1 class="print-title">${data.title}</h1>
          ${data.subtitle ? `<p class="print-subtitle">${data.subtitle}</p>` : ''}
          ${includeTimestamp ? `<p class="print-subtitle">تم الطباعة في: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</p>` : ''}
        </div>
  `;

  // إضافة الأقسام
  data.sections.forEach(section => {
    html += generateSectionHTML(section);
  });

  html += `
        <script>
          document.fonts.ready.then(() => {
            setTimeout(() => {
              window.print();
              window.onafterprint = () => window.close();
            }, 1500);
          });
        </script>
      </body>
    </html>
  `;

  return html;
}

function generateSectionHTML(section: PrintSection): string {
  let sectionHTML = `<div class="section ${section.className || ''}">`;
  sectionHTML += `<h2 class="section-title">${section.title}</h2>`;

  switch (section.type) {
    case 'info':
      sectionHTML += generateInfoHTML(section.data);
      break;
    case 'table':
      sectionHTML += generateTableHTML(section.data, section.columns || []);
      break;
    case 'timeline':
      sectionHTML += generateTimelineHTML(section.data);
      break;
    case 'grid':
      sectionHTML += generateGridHTML(section.data);
      break;
    case 'custom':
      sectionHTML += section.data;
      break;
  }

  sectionHTML += '</div>';
  return sectionHTML;
}

function generateInfoHTML(data: Record<string, any>): string {
  let html = '<div class="info-grid">';
  
  Object.entries(data).forEach(([key, value]) => {
    html += `
      <div class="info-item">
        <span class="info-label">${key}:</span> ${value || '-'}
      </div>
    `;
  });
  
  html += '</div>';
  return html;
}

function generateTableHTML(data: any[], columns: string[]): string {
  if (!data.length) return '<p>لا توجد بيانات</p>';

  let html = '<table><thead><tr>';
  columns.forEach(col => {
    html += `<th>${col}</th>`;
  });
  html += '</tr></thead><tbody>';

  data.forEach(row => {
    html += '<tr>';
    if (Array.isArray(row)) {
      row.forEach(cell => {
        html += `<td>${cell || '-'}</td>`;
      });
    } else {
      columns.forEach(col => {
        html += `<td>${row[col] || '-'}</td>`;
      });
    }
    html += '</tr>';
  });

  html += '</tbody></table>';
  return html;
}

function generateTimelineHTML(data: any[]): string {
  let html = '';
  
  data.forEach(item => {
    html += `
      <div class="timeline-item">
        <div style="font-weight: bold; margin-bottom: 4px;">${item.title || '-'}</div>
        <div style="margin-bottom: 4px;">${item.description || '-'}</div>
        <div style="font-size: 12px; color: #6b7280;">
          ${item.date || '-'} ${item.user ? `- ${item.user}` : ''}
        </div>
      </div>
    `;
  });
  
  return html;
}

function generateGridHTML(data: Record<string, any>): string {
  return generateInfoHTML(data);
}

async function printHTMLContent(htmlContent: string, title: string): Promise<void> {
  const printWindow = window.open('', '_blank', 'width=800,height=600');
  if (!printWindow) {
    throw new Error('فشل في فتح نافذة الطباعة. يرجى التأكد من عدم حظر النوافذ المنبثقة.');
  }

  printWindow.document.write(htmlContent);
  printWindow.document.close();
}

async function exportWithCanvas(data: PrintData, options: PrintExportOptions): Promise<void> {
  // تحويل البيانات لتنسيق مناسب لـ Canvas
  const deviceInfo = {
    model: data.title,
    id: options.fileName || 'report',
    status: 'تقرير',
    ...data.metadata
  };

  const timelineEvents = data.sections
    .filter(section => section.type === 'timeline')
    .flatMap(section => section.data);

  await createArabicPDFWithCanvas(
    deviceInfo,
    timelineEvents,
    options.fileName || 'report',
    false
  );
}
