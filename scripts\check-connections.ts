#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkDatabaseConnections() {
  try {
    console.log('🔍 فحص اتصالات قاعدة البيانات المسجلة...');

    const connections = await prisma.databaseConnection.findMany();
    
    console.log(`📊 عدد الاتصالات: ${connections.length}`);
    
    if (connections.length > 0) {
      console.log('\n📋 تفاصيل الاتصالات:');
      connections.forEach((conn, index) => {
        console.log(`${index + 1}. الاسم: ${conn.name}`);
        console.log(`   - ID: ${conn.id}`);
        console.log(`   - الخادم: ${conn.host}:${conn.port}`);
        console.log(`   - المستخدم: ${conn.username}`);
        console.log(`   - قاعدة البيانات: ${conn.database}`);
        console.log(`   - كلمة المرور: ${conn.password ? '***مخفية***' : 'غير محددة'}`);
        console.log(`   - نشط: ${conn.isActive ? 'نعم' : 'لا'}`);
        console.log(`   - افتراضي: ${conn.isDefault ? 'نعم' : 'لا'}`);
        console.log('');
      });
      
      // التحقق من صحة المعلومات
      const defaultConnection = connections.find(c => c.isDefault);
      if (defaultConnection) {
        console.log('✅ تم العثور على الاتصال الافتراضي:');
        console.log(`   - اسم: ${defaultConnection.name}`);
        console.log(`   - مستخدم: ${defaultConnection.username}`);
        console.log(`   - قاعدة البيانات: ${defaultConnection.database}`);
        
        // مقارنة مع متغيرات البيئة
        const envUrl = process.env.DATABASE_URL;
        if (envUrl) {
          const url = new URL(envUrl);
          console.log('\n🔍 مقارنة مع متغيرات البيئة:');
          console.log(`   - المستخدم في الاتصال: ${defaultConnection.username}`);
          console.log(`   - المستخدم في ENV: ${url.username}`);
          console.log(`   - قاعدة البيانات في الاتصال: ${defaultConnection.database}`);
          console.log(`   - قاعدة البيانات في ENV: ${url.pathname.slice(1)}`);
          
          if (defaultConnection.username !== url.username) {
            console.log('⚠️ اختلاف في أسماء المستخدمين!');
          }
          if (defaultConnection.database !== url.pathname.slice(1)) {
            console.log('⚠️ اختلاف في أسماء قواعد البيانات!');
          }
        }
      } else {
        console.log('⚠️ لم يتم العثور على اتصال افتراضي');
      }
    } else {
      console.log('⚠️ لا توجد اتصالات مسجلة');
    }

  } catch (error) {
    console.error('❌ خطأ في فحص الاتصالات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseConnections();
