import { PrismaClient } from '@prisma/client'
import { permissionPages } from '../lib/types'

const prisma = new PrismaClient()

async function main() {
  // تحقق من وجود مستخدمين
  const userCount = await prisma.user.count()
  
  if (userCount === 0) {
    console.log('لا يوجد مستخدمين. سيتم إنشاء مستخدم افتراضي...')
    
    // إنشاء صلاحيات كاملة
    const permissions: any = {}
    permissionPages.forEach(page => {
      permissions[page] = {
        view: true,
        create: true,
        edit: true,
        delete: true,
        viewAll: true,
        manage: [1, 2, 3],
        acceptWithoutWarranty: true
      }
    })
    
    // إنشاء مستخدم افتراضي
    await prisma.user.create({
      data: {
        name: 'مدير النظام',
        email: '<EMAIL>',
        username: 'admin',
        role: 'admin',
        permissions: permissions as any
      },
    })
    
    console.log('تم إنشاء المستخدم الافتراضي بنجاح')
  } else {
    console.log(`يوجد ${userCount} مستخدم في قاعدة البيانات بالفعل`)
  }
  
  // تحقق من وجود إعدادات النظام
  const settingsCount = await prisma.systemSetting.count()
  
  if (settingsCount === 0) {
    console.log('لا يوجد إعدادات نظام. سيتم إنشاء إعدادات افتراضية...')
    
    // إنشاء إعدادات افتراضية
    await prisma.systemSetting.create({
      data: {
        companyNameAr: 'شركتي',
        companyNameEn: 'My Company',
        addressAr: 'العنوان',
        addressEn: 'Address',
        phone: '1234567890',
        email: '<EMAIL>',
        website: 'www.example.com'
      }
    })
    
    console.log('تم إنشاء إعدادات النظام الافتراضية بنجاح')
  }
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
