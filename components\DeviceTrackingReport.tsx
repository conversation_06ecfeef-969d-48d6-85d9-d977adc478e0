'use client';

import React from 'react';
import DocumentHeader, { DocumentFooter, useSystemSettings } from './DocumentHeader';
import { Device } from '@/lib/types';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import './DeviceTrackingReport.css';

interface TimelineEvent {
  id: string;
  type: string;
  title: string;
  description: string;
  date: string;
  user?: string;
  details?: any;
  status?: string;
}

interface DeviceInfo {
  model: string;
  id: string;
  status: string;
  lastSale?: {
    clientName: string;
    soNumber: string;
    opNumber: string;
    date: string;
  };
  warrantyInfo?: {
    status: string;
    expiryDate: string;
    remaining: string;
  };
  originalItemInfo?: {
    model: string;
    [key: string]: any;
  };
}

interface DeviceTrackingReportProps {
  deviceInfo: DeviceInfo;
  timelineEvents: TimelineEvent[];
  isCustomerView?: boolean;
  language?: 'ar' | 'en' | 'both';
  className?: string;
}

export default function DeviceTrackingReport({
  deviceInfo,
  timelineEvents,
  isCustomerView = false,
  language = 'both',
  className = ''
}: DeviceTrackingReportProps) {
  const { settings, isLoading, error } = useSystemSettings();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-600">جاري تحميل الإعدادات...</p>
        </div>
      </div>
    );
  }

  if (error || !settings) {
    return (
      <div className="text-center p-8 text-red-600">
        <p>خطأ في تحميل الإعدادات: {error}</p>
      </div>
    );
  }

  const reportTitle = isCustomerView 
    ? (language === 'en' ? 'Device Tracking Report (Customer Copy)' : 'تقرير تتبع الجهاز (نسخة العميل)')
    : (language === 'en' ? 'Device History Log' : 'سجل تاريخ الجهاز');

  const reportSubtitle = `${deviceInfo.model} - ${deviceInfo.id}`;

  return (
    <div className={`device-tracking-report bg-white ${className}`}>
      {/* الترويسة */}
      <DocumentHeader
        settings={settings}
        title={reportTitle}
        subtitle={reportSubtitle}
        language={language}
        showLogo={true}
      />

      {/* محتوى التقرير */}
      <div className="report-content space-y-6 px-4">
        {/* معلومات الجهاز الأساسية */}
        <div className="device-basic-info">
          <h3 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">
            {language === 'en' ? 'Device Information' : 'معلومات الجهاز'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="info-item">
              <span className="font-semibold text-gray-700">
                {language === 'en' ? 'Model:' : 'الموديل:'}
              </span>
              <span className="ml-2 text-gray-900">{deviceInfo.model}</span>
            </div>
            <div className="info-item">
              <span className="font-semibold text-gray-700">
                {language === 'en' ? 'Serial Number:' : 'الرقم التسلسلي:'}
              </span>
              <span className="ml-2 text-gray-900">{deviceInfo.id}</span>
            </div>
            <div className="info-item">
              <span className="font-semibold text-gray-700">
                {language === 'en' ? 'Current Status:' : 'الحالة الحالية:'}
              </span>
              <span className="ml-2 text-gray-900">{deviceInfo.status}</span>
            </div>
            <div className="info-item">
              <span className="font-semibold text-gray-700">
                {language === 'en' ? 'Report Date:' : 'تاريخ التقرير:'}
              </span>
              <span className="ml-2 text-gray-900">
                {format(new Date(), 'yyyy/MM/dd', { locale: ar })}
              </span>
            </div>
          </div>
        </div>

        {/* معلومات البيع (للعملاء) */}
        {isCustomerView && deviceInfo.lastSale && (
          <div className="sale-info">
            <h3 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">
              {language === 'en' ? 'Sale Information' : 'معلومات البيع'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="info-item">
                <span className="font-semibold text-gray-700">
                  {language === 'en' ? 'Customer Name:' : 'اسم العميل:'}
                </span>
                <span className="ml-2 text-gray-900">{deviceInfo.lastSale.clientName}</span>
              </div>
              <div className="info-item">
                <span className="font-semibold text-gray-700">
                  {language === 'en' ? 'Sale Order:' : 'رقم أمر البيع:'}
                </span>
                <span className="ml-2 text-gray-900">{deviceInfo.lastSale.soNumber}</span>
              </div>
              <div className="info-item">
                <span className="font-semibold text-gray-700">
                  {language === 'en' ? 'Operation Number:' : 'رقم العملية:'}
                </span>
                <span className="ml-2 text-gray-900">{deviceInfo.lastSale.opNumber}</span>
              </div>
              <div className="info-item">
                <span className="font-semibold text-gray-700">
                  {language === 'en' ? 'Sale Date:' : 'تاريخ البيع:'}
                </span>
                <span className="ml-2 text-gray-900">
                  {format(new Date(deviceInfo.lastSale.date), 'yyyy/MM/dd', { locale: ar })}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* معلومات الضمان (للعملاء) */}
        {isCustomerView && deviceInfo.warrantyInfo && (
          <div className="warranty-info">
            <h3 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">
              {language === 'en' ? 'Warranty Information' : 'معلومات الضمان'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="info-item">
                <span className="font-semibold text-gray-700">
                  {language === 'en' ? 'Warranty Status:' : 'حالة الضمان:'}
                </span>
                <span className={`ml-2 font-medium ${
                  deviceInfo.warrantyInfo.status === 'ساري' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {deviceInfo.warrantyInfo.status}
                </span>
              </div>
              <div className="info-item">
                <span className="font-semibold text-gray-700">
                  {language === 'en' ? 'Expiry Date:' : 'تاريخ الانتهاء:'}
                </span>
                <span className="ml-2 text-gray-900">{deviceInfo.warrantyInfo.expiryDate}</span>
              </div>
              <div className="info-item">
                <span className="font-semibold text-gray-700">
                  {language === 'en' ? 'Remaining:' : 'المتبقي:'}
                </span>
                <span className="ml-2 text-gray-900">{deviceInfo.warrantyInfo.remaining}</span>
              </div>
            </div>
          </div>
        )}

        {/* سجل الأحداث */}
        <div className="timeline-section">
          <h3 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">
            {isCustomerView 
              ? (language === 'en' ? 'Service History' : 'سجل الخدمات')
              : (language === 'en' ? 'Complete History' : 'السجل الكامل')
            }
          </h3>
          
          {timelineEvents.length > 0 ? (
            <div className="timeline relative">
              {/* خط الزمن */}
              <div className="absolute right-4 top-0 bottom-0 w-0.5 bg-gray-300"></div>
              
              {timelineEvents.map((event, index) => (
                <div key={event.id} className="timeline-item relative mb-6 pr-12">
                  {/* نقطة الزمن */}
                  <div className="absolute right-2 w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-md"></div>
                  
                  {/* محتوى الحدث */}
                  <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold text-gray-800">{event.title}</h4>
                      <span className="text-sm text-gray-500">
                        {format(new Date(event.date), 'yyyy/MM/dd HH:mm', { locale: ar })}
                      </span>
                    </div>
                    
                    <p className="text-gray-700 mb-2">{event.description}</p>
                    
                    <div className="flex justify-between items-center text-sm text-gray-600">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {event.type}
                      </span>
                      {event.user && (
                        <span>
                          {language === 'en' ? 'By:' : 'بواسطة:'} {event.user}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>{language === 'en' ? 'No events recorded' : 'لا توجد أحداث مسجلة'}</p>
            </div>
          )}
        </div>
      </div>

      {/* التذييل */}
      <DocumentFooter
        settings={settings}
        language={language}
        showTimestamp={true}
      />
    </div>
  );
}
