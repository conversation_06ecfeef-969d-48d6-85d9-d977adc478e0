#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixWarehousesTable() {
  try {
    console.log('🔧 بدء إصلاح جدول المخازن...');

    // 1. التحقق من وجود جدول المخازن
    console.log('\n📋 فحص جدول المخازن...');
    
    try {
      const warehouses = await prisma.warehouse.findMany();
      console.log(`✅ تم العثور على ${warehouses.length} مخزن في قاعدة البيانات`);
      
      if (warehouses.length === 0) {
        console.log('📦 إنشاء مخازن افتراضية...');
        
        const defaultWarehouses = [
          {
            name: 'المخزن الرئيسي',
            type: 'رئيسي',
            location: 'المقر الرئيسي - صنعاء'
          },
          {
            name: 'مخزن الفرع الشمالي',
            type: 'فرعي',
            location: 'الفرع الشمالي - شارع الزبيري'
          },
          {
            name: 'مخزن الفرع الجنوبي',
            type: 'فرعي',
            location: 'الفرع الجنوبي - شارع الأمير محمد'
          }
        ];

        for (const warehouse of defaultWarehouses) {
          const created = await prisma.warehouse.create({
            data: warehouse
          });
          console.log(`✅ تم إنشاء مخزن: ${created.name} (ID: ${created.id})`);
        }
      } else {
        console.log('\n📋 المخازن الموجودة:');
        warehouses.forEach(w => {
          console.log(`- ID: ${w.id}, الاسم: "${w.name}", النوع: ${w.type}, الموقع: ${w.location}`);
        });
      }

    } catch (error) {
      console.error('❌ خطأ في الوصول إلى جدول المخازن:', error);
      throw error;
    }

    // 2. التحقق من الأجهزة غير المرتبطة بمخازن
    console.log('\n🔍 فحص الأجهزة غير المرتبطة بمخازن...');
    
    try {
      const devicesWithoutWarehouse = await prisma.device.findMany({
        where: {
          warehouseId: null
        }
      });

      console.log(`📱 عدد الأجهزة غير المرتبطة بمخازن: ${devicesWithoutWarehouse.length}`);

      if (devicesWithoutWarehouse.length > 0) {
        // العثور على المخزن الرئيسي أو أول مخزن متاح
        const mainWarehouse = await prisma.warehouse.findFirst({
          where: { type: 'رئيسي' }
        }) || await prisma.warehouse.findFirst();

        if (mainWarehouse) {
          console.log(`📦 ربط الأجهزة بالمخزن: ${mainWarehouse.name}...`);
          
          const updated = await prisma.device.updateMany({
            where: { warehouseId: null },
            data: { warehouseId: mainWarehouse.id }
          });

          console.log(`✅ تم ربط ${updated.count} جهاز بالمخزن الرئيسي`);
        }
      }

    } catch (error) {
      console.log('⚠️ لا يمكن الوصول إلى جدول الأجهزة، سيتم المتابعة...');
    }

    // 3. التحقق من وجود أوامر توريد غير مرتبطة
    console.log('\n🔍 فحص أوامر التوريد...');
    
    try {
      const totalSupplyOrders = await prisma.supplyOrder.count();
      console.log(`📋 إجمالي أوامر التوريد: ${totalSupplyOrders}`);

    } catch (error) {
      console.log('⚠️ لا يمكن الوصول إلى جدول أوامر التوريد، سيتم المتابعة...');
    }

    // 4. التحقق من جداول الموديلات والشركات المصنعة
    console.log('\n🏭 فحص جداول الشركات المصنعة والموديلات...');
    
    try {
      const manufacturers = await prisma.manufacturer.findMany();
      console.log(`✅ تم العثور على ${manufacturers.length} شركة مصنعة`);
      
      if (manufacturers.length === 0) {
        console.log('📱 إنشاء شركات مصنعة افتراضية...');
        
        const defaultManufacturers = [
          { name: 'Apple' },
          { name: 'Samsung' },
          { name: 'Huawei' },
          { name: 'Xiaomi' },
          { name: 'Oppo' },
          { name: 'Vivo' },
          { name: 'OnePlus' },
          { name: 'Realme' }
        ];

        for (const manufacturer of defaultManufacturers) {
          const created = await prisma.manufacturer.create({
            data: manufacturer
          });
          console.log(`✅ تم إنشاء شركة: ${created.name} (ID: ${created.id})`);
        }
      } else {
        console.log('\n📋 الشركات المصنعة الموجودة:');
        manufacturers.forEach(m => {
          console.log(`- ID: ${m.id}, الاسم: "${m.name}"`);
        });
      }

      // فحص الموديلات
      const deviceModels = await prisma.deviceModel.findMany({
        include: {
          manufacturer: true
        }
      });
      console.log(`📱 تم العثور على ${deviceModels.length} موديل جهاز`);
      
      if (deviceModels.length === 0) {
        console.log('📱 إنشاء موديلات افتراضية...');
        
        // الحصول على الشركات المصنعة لإنشاء موديلات
        const allManufacturers = await prisma.manufacturer.findMany();
        
        if (allManufacturers.length > 0) {
          const defaultModels = [
            { name: 'iPhone 14', manufacturerId: allManufacturers.find(m => m.name === 'Apple')?.id || allManufacturers[0].id, category: 'هاتف ذكي' },
            { name: 'iPhone 13', manufacturerId: allManufacturers.find(m => m.name === 'Apple')?.id || allManufacturers[0].id, category: 'هاتف ذكي' },
            { name: 'Galaxy S23', manufacturerId: allManufacturers.find(m => m.name === 'Samsung')?.id || allManufacturers[0].id, category: 'هاتف ذكي' },
            { name: 'Galaxy A54', manufacturerId: allManufacturers.find(m => m.name === 'Samsung')?.id || allManufacturers[0].id, category: 'هاتف ذكي' }
          ];

          for (const model of defaultModels) {
            if (model.manufacturerId) {
              const created = await prisma.deviceModel.create({
                data: model
              });
              console.log(`✅ تم إنشاء موديل: ${created.name} (ID: ${created.id})`);
            }
          }
        }
      } else {
        console.log('\n📱 الموديلات الموجودة:');
        deviceModels.slice(0, 5).forEach(m => {
          console.log(`- ID: ${m.id}, الاسم: "${m.name}", الشركة: ${m.manufacturer.name}, الفئة: ${m.category}`);
        });
        if (deviceModels.length > 5) {
          console.log(`... و ${deviceModels.length - 5} موديل آخر`);
        }
      }

    } catch (error) {
      console.error('❌ خطأ في فحص جداول الموديلات:', error);
    }

    // 5. اختبار API endpoint
    console.log('\n🌐 اختبار API endpoint للمخازن...');
    
    try {
      const response = await fetch('http://localhost:9005/api/warehouses-simple');
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ API يعمل بشكل صحيح، تم جلب ${data.data?.length || data.length || 0} مخزن`);
      } else {
        console.log(`⚠️ API لا يعمل، رمز الاستجابة: ${response.status}`);
      }
    } catch (error) {
      console.log('⚠️ لا يمكن الوصول إلى API، تأكد من تشغيل الخادم');
    }

    console.log('\n✅ تم الانتهاء من إصلاح جدول المخازن بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إصلاح جدول المخازن:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixWarehousesTable();
