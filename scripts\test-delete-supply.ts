import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDeleteSupplyOrder() {
  try {
    console.log('اختبار حذف أمر التوريد...\n');

    // العثور على أول أمر توريد
    const firstOrder = await prisma.supplyOrder.findFirst();
    
    if (!firstOrder) {
      console.log('لا توجد أوامر توريد للحذف');
      return;
    }

    console.log(`محاولة حذف الأمر: ${firstOrder.supplyOrderId}`);

    // محاولة حذف الأمر
    const deletedOrder = await prisma.supplyOrder.delete({
      where: { id: firstOrder.id }
    });

    console.log(`تم حذف الأمر بنجاح: ${deletedOrder.supplyOrderId}`);

    // التحقق من عدد الأوامر المتبقية
    const remainingOrders = await prisma.supplyOrder.count();
    console.log(`الأوامر المتبقية: ${remainingOrders}`);

  } catch (error) {
    console.error('خطأ في حذف أمر التوريد:', error);
    
    // فحص ما إذا كانت هناك قيود على الحذف
    if (error.code === 'P2003') {
      console.log('هناك قيود على الحذف - قد تكون هناك بيانات مرتبطة');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testDeleteSupplyOrder();
