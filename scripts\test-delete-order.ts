import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDeleteOrder() {
  console.log('🗑️ اختبار حذف أمر التوريد...\n');

  try {
    // جلب جميع أوامر التوريد الموجودة
    const existingOrders = await prisma.supplyOrder.findMany({
      select: {
        id: true,
        supplyOrderId: true,
        status: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log('📋 أوامر التوريد الموجودة:');
    existingOrders.forEach(order => {
      console.log(`   ID: ${order.id} - رقم الأمر: ${order.supplyOrderId} - الحالة: ${order.status}`);
    });

    if (existingOrders.length === 0) {
      console.log('❌ لا توجد أوامر توريد للاختبار');
      return;
    }

    // اختبار حذف الأمر الأول
    const orderToTest = existingOrders[0];
    console.log(`\n🎯 سيتم اختبار حذف الأمر: ID ${orderToTest.id} (${orderToTest.supplyOrderId})`);

    // محاكاة ما يحدث في API
    console.log('\n🔍 محاكاة عملية الحذف:');
    console.log(`1. استلام ID: ${orderToTest.id}`);
    console.log(`2. نوع ID: ${typeof orderToTest.id}`);
    
    // البحث عن الأمر
    const foundOrder = await prisma.supplyOrder.findUnique({
      where: { id: orderToTest.id }
    });

    if (foundOrder) {
      console.log('✅ تم العثور على الأمر في قاعدة البيانات');
      console.log(`   تفاصيل: ${foundOrder.supplyOrderId} - ${foundOrder.status}`);
      
      // فحص الأجهزة المرتبطة
      const relatedDevices = await prisma.device.findMany({
        where: { supplierId: foundOrder.supplierId }
      });
      
      console.log(`📱 الأجهزة المرتبطة بنفس المورد: ${relatedDevices.length}`);
      relatedDevices.forEach(device => {
        console.log(`   📱 ${device.id} - ${device.model} - ${device.status}`);
      });
      
    } else {
      console.log('❌ لم يتم العثور على الأمر في قاعدة البيانات');
    }

    console.log('\n⚠️ لن يتم الحذف الفعلي في هذا الاختبار');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDeleteOrder();
