# 🔐 دليل الصلاحيات المتقدم - الصفحات المتبقية والحالات الخاصة

## 📋 الصفحات المتبقية (3 صفحات)

### 15. صفحة قبول الأجهزة - `/accept-devices/page.tsx`

**pageKey:** `acceptDevices`

**الإصلاحات المطلوبة:**
- حماية الصفحة الكاملة
- حماية عمليات قبول الأجهزة
- حماية تعديل حالة الأجهزة

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

export default function AcceptDevicesPage() {
  const { canCreate, canEdit } = usePermission('acceptDevices');

  return (
    <PermissionGuard pageKey="acceptDevices">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="acceptDevices" action="create">
        <Button onClick={handleAcceptDevice}>
          قبول الجهاز
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="acceptDevices" action="edit">
        <Button onClick={() => handleUpdateDeviceStatus(device.id)}>
          تحديث الحالة
        </Button>
      </ActionGuard>
      
      {/* حماية عمليات المسح */}
      <ActionGuard pageKey="acceptDevices" action="create">
        <div className="scanner-section">
          {/* مكون الماسح الضوئي */}
        </div>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

### 16. صفحة أداة التسعير - `/pricing/page.tsx`

**pageKey:** `pricing`

**الإصلاحات المطلوبة:**
- حماية الصفحة الكاملة
- حماية تحديث الأسعار
- حماية عرض هوامش الربح

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

export default function PricingPage() {
  const { canEdit, canView } = usePermission('pricing');

  return (
    <PermissionGuard pageKey="pricing">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="pricing" action="edit">
        <Button onClick={handleUpdatePricing}>
          تحديث الأسعار
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="pricing" action="edit">
        <Input
          type="number"
          placeholder="السعر الجديد"
          onChange={handlePriceChange}
        />
      </ActionGuard>
      
      {/* حماية عرض هوامش الربح */}
      <ActionGuard pageKey="pricing" action="view">
        <div className="profit-margins">
          {/* عرض هوامش الربح */}
        </div>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

### 17. صفحة التحويل المخزني - `/warehouse-transfer/page.tsx`

**pageKey:** `warehouseTransfer`

**الإصلاحات المطلوبة:**
- حماية الصفحة الكاملة
- حماية إنشاء التحويلات
- التحقق من صلاحيات المخازن المحددة

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

export default function WarehouseTransferPage() {
  const { canCreate, canEdit, canManage } = usePermission('warehouseTransfer');

  // التحقق من صلاحية إدارة مخازن محددة
  const canTransferBetweenWarehouses = (fromId: number, toId: number) => {
    return canManage.includes(fromId) && canManage.includes(toId);
  };

  return (
    <PermissionGuard pageKey="warehouseTransfer">
      {/* المحتوى الحالي */}
      
      <ActionGuard pageKey="warehouseTransfer" action="create">
        <Button onClick={handleCreateTransfer}>
          إنشاء تحويل جديد
        </Button>
      </ActionGuard>
      
      <ActionGuard pageKey="warehouseTransfer" action="edit">
        <Button onClick={() => handleEditTransfer(transfer.id)}>
          تعديل التحويل
        </Button>
      </ActionGuard>
      
      {/* فلترة المخازن حسب الصلاحيات */}
      <Select>
        {warehouses
          .filter(warehouse => canManage.includes(warehouse.id))
          .map(warehouse => (
            <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
              {warehouse.name}
            </SelectItem>
          ))
        }
      </Select>
    </PermissionGuard>
  );
}
```

---

## 🔧 حالات خاصة ومتقدمة

### 1. الصلاحيات الشرطية

#### مثال: صفحة المرتجعات مع صلاحية خاصة

```typescript
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';

export default function ReturnsPage() {
  const { 
    canCreate, 
    canEdit, 
    canDelete, 
    canAcceptWithoutWarranty 
  } = usePermission('returns');

  return (
    <PermissionGuard pageKey="returns">
      {/* المحتوى العادي */}
      
      {/* صلاحية خاصة للقبول بدون ضمان */}
      {canAcceptWithoutWarranty && (
        <div className="special-actions">
          <Button 
            variant="destructive"
            onClick={handleAcceptWithoutWarranty}
          >
            قبول بدون ضمان
          </Button>
        </div>
      )}
      
      {/* حماية الإجراءات العادية */}
      <ActionGuard pageKey="returns" action="create">
        <Button onClick={handleCreateReturn}>
          إنشاء مرتجع جديد
        </Button>
      </ActionGuard>
    </PermissionGuard>
  );
}
```

### 2. صلاحيات المخازن المحددة

#### مثال: التحويل المخزني مع فلترة المخازن

```typescript
export default function WarehouseTransferPage() {
  const { canManage } = usePermission('warehouseTransfer');
  const { warehouses } = useStore();

  // فلترة المخازن المسموح بإدارتها
  const allowedWarehouses = warehouses.filter(warehouse => 
    canManage.includes(warehouse.id)
  );

  const handleCreateTransfer = (fromId: number, toId: number) => {
    // التحقق من الصلاحية قبل التنفيذ
    if (!canManage.includes(fromId) || !canManage.includes(toId)) {
      toast.error('ليس لديك صلاحية للتحويل بين هذين المخزنين');
      return;
    }
    
    // تنفيذ التحويل
    createTransfer(fromId, toId);
  };

  return (
    <PermissionGuard pageKey="warehouseTransfer">
      <Select>
        {allowedWarehouses.map(warehouse => (
          <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
            {warehouse.name}
          </SelectItem>
        ))}
      </Select>
    </PermissionGuard>
  );
}
```

### 3. صلاحيات الرسائل مع ViewAll

#### مثال: صفحة الرسائل مع صلاحية عرض الكل

```typescript
export default function MessagingPage() {
  const { canCreate, canViewAll } = usePermission('messaging');
  const { currentUser, messages } = useStore();

  // فلترة الرسائل حسب الصلاحية
  const visibleMessages = canViewAll 
    ? messages // عرض جميع الرسائل
    : messages.filter(msg => 
        msg.senderId === currentUser.id || 
        msg.recipientId === currentUser.id
      ); // عرض الرسائل الخاصة فقط

  return (
    <PermissionGuard pageKey="messaging">
      {/* عرض الرسائل المفلترة */}
      <div className="messages-list">
        {visibleMessages.map(message => (
          <MessageCard key={message.id} message={message} />
        ))}
      </div>
      
      <ActionGuard pageKey="messaging" action="create">
        <Button onClick={handleSendMessage}>
          إرسال رسالة جديدة
        </Button>
      </ActionGuard>
      
      {/* إظهار إحصائيات إضافية للمديرين */}
      {canViewAll && (
        <div className="admin-stats">
          <h3>إحصائيات الرسائل</h3>
          <p>إجمالي الرسائل: {messages.length}</p>
          <p>الرسائل غير المقروءة: {messages.filter(m => !m.isRead).length}</p>
        </div>
      )}
    </PermissionGuard>
  );
}
```

---

## 🛠️ أدوات مساعدة للتطبيق

### 1. مكون فحص الصلاحيات المتقدم

```typescript
// components/AdvancedPermissionCheck.tsx
import { usePermission } from '@/hooks/usePermission';
import { PermissionPageKey } from '@/lib/types';

interface AdvancedPermissionCheckProps {
  pageKey: PermissionPageKey;
  action?: 'view' | 'create' | 'edit' | 'delete';
  warehouseIds?: number[];
  customCheck?: (permissions: any) => boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function AdvancedPermissionCheck({
  pageKey,
  action = 'view',
  warehouseIds,
  customCheck,
  children,
  fallback = null
}: AdvancedPermissionCheckProps) {
  const permissions = usePermission(pageKey);

  // التحقق من الصلاحية الأساسية
  let hasPermission = false;
  switch (action) {
    case 'view':
      hasPermission = permissions.canView;
      break;
    case 'create':
      hasPermission = permissions.canCreate;
      break;
    case 'edit':
      hasPermission = permissions.canEdit;
      break;
    case 'delete':
      hasPermission = permissions.canDelete;
      break;
  }

  // التحقق من صلاحيات المخازن
  if (warehouseIds && permissions.canManage) {
    hasPermission = hasPermission && warehouseIds.every(id => 
      permissions.canManage.includes(id)
    );
  }

  // التحقق المخصص
  if (customCheck) {
    hasPermission = hasPermission && customCheck(permissions);
  }

  return hasPermission ? <>{children}</> : <>{fallback}</>;
}
```

### 2. Hook للتحقق من الصلاحيات المتقدم

```typescript
// hooks/useAdvancedPermissions.ts
import { usePermission } from '@/hooks/usePermission';
import { PermissionPageKey } from '@/lib/types';

export function useAdvancedPermissions(pageKey: PermissionPageKey) {
  const permissions = usePermission(pageKey);

  const canAccessWarehouse = (warehouseId: number) => {
    return permissions.canManage?.includes(warehouseId) || false;
  };

  const canTransferBetweenWarehouses = (fromId: number, toId: number) => {
    return canAccessWarehouse(fromId) && canAccessWarehouse(toId);
  };

  const hasAnyPermission = () => {
    return permissions.canView || 
           permissions.canCreate || 
           permissions.canEdit || 
           permissions.canDelete;
  };

  const getAccessibleWarehouses = (allWarehouses: any[]) => {
    if (!permissions.canManage) return [];
    return allWarehouses.filter(warehouse => 
      permissions.canManage.includes(warehouse.id)
    );
  };

  return {
    ...permissions,
    canAccessWarehouse,
    canTransferBetweenWarehouses,
    hasAnyPermission,
    getAccessibleWarehouses
  };
}
```

---

## 📋 قائمة التحقق النهائية

### ✅ للصفحات الأساسية:
- [ ] **المستخدمين**: حماية كاملة مع إدارة الصلاحيات
- [ ] **الإعدادات**: حماية تحديث الإعدادات
- [ ] **المبيعات**: حماية العمليات المالية
- [ ] **المخزون**: حماية بيانات المخزون

### ✅ للصفحات المتوسطة:
- [ ] **التوريد**: حماية أوامر التوريد
- [ ] **المرتجعات**: حماية مع صلاحيات خاصة
- [ ] **العملاء**: حماية بيانات العملاء
- [ ] **المخازن**: حماية إدارة المخازن

### ✅ للصفحات التشغيلية:
- [ ] **التتبع**: حماية عرض البيانات
- [ ] **التقييم**: حماية عمليات التقييم
- [ ] **الجرد**: حماية عمليات الجرد
- [ ] **التقارير**: حماية عرض التقارير

### ✅ للصفحات الإضافية:
- [ ] **الرسائل**: حماية مع صلاحية ViewAll
- [ ] **الطلبات**: حماية طلبات الموظفين
- [ ] **قبول الأجهزة**: حماية عمليات القبول
- [ ] **أداة التسعير**: حماية تحديث الأسعار
- [ ] **التحويل المخزني**: حماية مع صلاحيات المخازن

---

## 🚀 خطة التنفيذ المرحلية

### المرحلة 1 (يوم 1): الصفحات الحرجة
1. صفحة المستخدمين
2. صفحة الإعدادات
3. صفحة المبيعات
4. صفحة المخزون

### المرحلة 2 (يوم 2): الصفحات المتوسطة
5. صفحة التوريد
6. صفحة المرتجعات
7. صفحة العملاء
8. صفحة المخازن

### المرحلة 3 (يوم 3): الصفحات التشغيلية
9. صفحة التتبع
10. صفحة التقييم
11. صفحة الجرد
12. صفحة التقارير

### المرحلة 4 (يوم 4): الصفحات الإضافية
13. صفحة الرسائل
14. صفحة الطلبات
15. صفحة قبول الأجهزة
16. صفحة أداة التسعير
17. صفحة التحويل المخزني

### المرحلة 5 (يوم 5): الاختبار والتحسين
- اختبار شامل لجميع الصفحات
- إصلاح أي مشاكل
- توثيق التغييرات

---

## ⚡ نصائح للتطبيق السريع

### 1. استخدام أدوات التطوير:
```bash
# البحث عن جميع ملفات الصفحات
find app/(main) -name "page.tsx" -type f

# البحث عن الأزرار غير المحمية
grep -r "Button.*onClick" app/(main) --include="*.tsx"
```

### 2. قالب سريع للنسخ واللصق:
```typescript
// إضافة في بداية كل ملف
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';

// لف المحتوى
<PermissionGuard pageKey="PAGE_KEY">
  {/* المحتوى الحالي */}
</PermissionGuard>

// حماية الأزرار
<ActionGuard pageKey="PAGE_KEY" action="ACTION_TYPE">
  <Button>النص</Button>
</ActionGuard>
```

### 3. اختبار سريع:
- إنشاء مستخدم بصلاحيات محدودة
- اختبار كل صفحة بعد التطبيق
- التأكد من ظهور رسائل الخطأ

---

هذا الدليل يكمل الدليل الأساسي ويوفر حلول للحالات المتقدمة والصفحات المتبقية. اتبع الخطوات بالترتيب للحصول على أفضل النتائج.
