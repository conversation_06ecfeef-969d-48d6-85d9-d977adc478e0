'use client';

import { useState, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Check, ChevronsUpDown, Smartphone } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Manufacturer } from '@/lib/types';

interface AddModelDialogProps {
  isOpen: boolean;
  onClose: () => void;
  preselectedManufacturer?: Manufacturer | null;
}

interface ModelFormData {
  manufacturer: string;
  model: string;
  arabicName: string;
  memory: string;
}

const initialFormData: ModelFormData = {
  manufacturer: '',
  model: '',
  arabicName: '',
  memory: '128GB',
};

export default function AddModelDialog({ 
  isOpen, 
  onClose, 
  preselectedManufacturer 
}: AddModelDialogProps) {
  const { toast } = useToast();
  const { 
    manufacturers, 
    addManufacturer, 
    addDeviceModel,
    fetchManufacturersData,
    fetchDeviceModelsData 
  } = useStore();

  const [formData, setFormData] = useState<ModelFormData>(initialFormData);
  const [isManufacturerSearchOpen, setIsManufacturerSearchOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // تحديث البيانات عند فتح النافذة
  useEffect(() => {
    if (isOpen) {
      fetchManufacturersData();
      
      // إذا كانت هناك شركة محددة مسبقاً
      if (preselectedManufacturer) {
        setFormData(prev => ({
          ...prev,
          manufacturer: preselectedManufacturer.name
        }));
      }
    }
  }, [isOpen, preselectedManufacturer, fetchManufacturersData]);

  // إعادة تعيين النموذج عند الإغلاق
  useEffect(() => {
    if (!isOpen) {
      setFormData(initialFormData);
    }
  }, [isOpen]);

  const handleSubmit = async () => {
    if (!formData.manufacturer || !formData.model) {
      toast({
        title: 'خطأ في الإدخال',
        description: 'يرجى ملء اسم الشركة المصنعة واسم الموديل.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // البحث عن الشركة المصنعة أو إنشاؤها
      let manufacturer = manufacturers.find(
        (m) => m.name.toLowerCase() === formData.manufacturer.toLowerCase()
      );

      if (!manufacturer) {
        // إنشاء شركة جديدة
        manufacturer = await addManufacturer({ name: formData.manufacturer });
        if (!manufacturer) {
          throw new Error('فشل في إنشاء الشركة المصنعة');
        }
      }

      // إنشاء اسم الموديل الكامل مع حجم الذاكرة
      const fullModelName = `${formData.model} ${formData.memory}`;

      // إضافة الموديل الجديد
      const modelData = {
        manufacturerId: manufacturer.id,
        name: fullModelName,
        category: 'هاتف ذكي'
      };

      await addDeviceModel(modelData);

      toast({
        title: 'تم الحفظ',
        description: 'تمت إضافة الموديل الجديد بنجاح.',
      });

      // تحديث البيانات
      await fetchDeviceModelsData();
      
      onClose();
    } catch (error) {
      console.error('خطأ في إضافة الموديل:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء إضافة الموديل.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5 text-primary" />
            إضافة موديل جديد
          </DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* اسم الشركة المصنعة */}
          <div className="space-y-2">
            <Label htmlFor="manuf-name">اسم الشركة المصنعة</Label>
            <div className="relative">
              <Popover open={isManufacturerSearchOpen} onOpenChange={setIsManufacturerSearchOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={isManufacturerSearchOpen}
                    className="w-full justify-between"
                    disabled={!!preselectedManufacturer}
                  >
                    {formData.manufacturer || "اختر الشركة المصنعة..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
                  <Command>
                    <CommandInput placeholder="بحث عن شركات مصنعة..." />
                    <CommandList>
                      <CommandEmpty>
                        <div className="py-2 px-2 text-center">
                          <p className="mb-2 text-muted-foreground">لم يتم العثور على شركة.</p>
                        </div>
                      </CommandEmpty>
                      <CommandGroup>
                        {manufacturers.map((manuf) => (
                          <CommandItem
                            key={manuf.id}
                            value={manuf.name}
                            onSelect={() => {
                              setFormData((d) => ({ ...d, manufacturer: manuf.name }));
                              setIsManufacturerSearchOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                formData.manufacturer === manuf.name ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {manuf.name}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              
              {!preselectedManufacturer && (
                <div className="mt-2">
                  <Input
                    placeholder="أو أدخل اسم شركة جديدة..."
                    value={formData.manufacturer}
                    onChange={(e) =>
                      setFormData((d) => ({ ...d, manufacturer: e.target.value }))
                    }
                  />
                </div>
              )}
            </div>
          </div>
          
          {/* اسم الموديل */}
          <div className="space-y-2">
            <Label htmlFor="model-name">اسم الموديل</Label>
            <Input
              id="model-name"
              value={formData.model}
              onChange={(e) =>
                setFormData((d) => ({ ...d, model: e.target.value }))
              }
              placeholder="مثال: iPhone 15 Pro"
            />
          </div>
          
          {/* اسم الموديل بالعربي */}
          <div className="space-y-2">
            <Label htmlFor="arabic-name">اسم الموديل بالعربي (اختياري)</Label>
            <Input
              id="arabic-name"
              value={formData.arabicName}
              onChange={(e) =>
                setFormData((d) => ({ ...d, arabicName: e.target.value }))
              }
              placeholder="أدخل اسم الموديل بالعربية (اختياري)"
            />
          </div>
          
          {/* سعة الذاكرة */}
          <div className="space-y-2">
            <Label htmlFor="memory-size">سعة الذاكرة</Label>
            <Select 
              value={formData.memory}
              onValueChange={(value) => setFormData((d) => ({ ...d, memory: value }))}
            >
              <SelectTrigger id="memory-size">
                <SelectValue placeholder="اختر سعة الذاكرة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="4GB">4GB</SelectItem>
                <SelectItem value="8GB">8GB</SelectItem>
                <SelectItem value="16GB">16GB</SelectItem>
                <SelectItem value="32GB">32GB</SelectItem>
                <SelectItem value="64GB">64GB</SelectItem>
                <SelectItem value="128GB">128GB</SelectItem>
                <SelectItem value="256GB">256GB</SelectItem>
                <SelectItem value="512GB">512GB</SelectItem>
                <SelectItem value="1TB">1TB</SelectItem>
                <SelectItem value="2TB">2TB</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            إلغاء
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? 'جاري الحفظ...' : 'حفظ الموديل'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
