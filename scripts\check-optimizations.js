// اختبار سريع للتأكد من أن useCallback تم تطبيقه بشكل صحيح
console.log('🔍 فحص التحسينات الجديدة...');
console.log('');
console.log('✅ التحسينات المطبقة:');
console.log('   - useCallback على loadEssentialData');
console.log('   - useCallback على reloadClients');
console.log('   - useCallback على reloadSuppliers');
console.log('   - useCallback على addContact');
console.log('   - إصلاح signatures للدوال');
console.log('   - إزالة useEffect المتكرر');
console.log('');
console.log('🎯 هذا يجب أن يحل مشكلة "Maximum update depth exceeded"');
console.log('');
console.log('🧪 اختبار API سريع...');

async function quickAPITest() {
  try {
    const response = await fetch('http://localhost:9005/api/clients-simple?limit=1');
    if (response.ok) {
      console.log('✅ APIs تعمل بشكل صحيح');
      console.log('');
      console.log('🌐 يمكنك الآن فتح: http://localhost:9005/clients');
      console.log('   الصفحة يجب أن تفتح بدون مشاكل');
    } else {
      console.log('❌ تأكد من تشغيل التطبيق');
    }
  } catch (error) {
    console.log('❌ تأكد من تشغيل التطبيق على localhost:9005');
  }
}

quickAPITest();
