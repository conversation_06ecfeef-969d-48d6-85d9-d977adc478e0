# **App Name**: DeviceFlow

## Core Features:

- Dashboard Overview: Dashboard providing a snapshot of inventory status, sales metrics, and urgent alerts.
- Device Tracking: User interface for tracking device history, from sourcing to sale, using IMEI or barcode.
- Add clients: Form to add and track new clients and providers to track device origin
- Barcode Scanning: Barcode scanner integration to rapidly acquire device information (note: barcode reader must support RTL/Arabic scanning).
- AI Pricing Tool: AI-powered analysis tool to tool suggest optimal pricing and identify popular devices.

## Style Guidelines:

- Primary color: Light desaturated blue (#94C9FF), inspired by the concept of efficiency and reliability of a tracking system, will be used as the primary color to bring calm to the eye.
- Background color: Dark blue-gray (#2C3333), designed to complement a light desaturated color (#94C9FF).
- Accent color: Light orange (#FFB347) will draw attention to key interactive elements such as buttons, links, and highlighted data.
- Body and headline font: 'PT Sans' (sans-serif), for clear and modern readability across all device sizes.
- Use minimalist line icons to represent different inventory actions and device stats.
- Support for right-to-left (RTL) layouts for Arabic language support.
- Subtle transitions to reflect updates to inventory records.
