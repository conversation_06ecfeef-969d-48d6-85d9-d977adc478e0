import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkSupplyOrderAndDevice() {
  try {
    console.log('=== فحص أمر التوريد المتبقي والجهاز المرتبط ===\n');

    // فحص أمر التوريد المتبقي
    const supplyOrder = await prisma.supplyOrder.findFirst({
      select: {
        id: true,
        supplyOrderId: true,
        status: true,
        employeeName: true,
        createdAt: true
      }
    });

    if (supplyOrder) {
      console.log('أمر التوريد المتبقي:');
      console.log(`- ID: ${supplyOrder.id}`);
      console.log(`- رقم الأمر: ${supplyOrder.supplyOrderId}`);
      console.log(`- الحالة: ${supplyOrder.status}`);
      console.log(`- الموظف: ${supplyOrder.employeeName}`);
      console.log(`- تاريخ الإنشاء: ${supplyOrder.createdAt}`);
      
      // البحث عن الجهاز بالرقم التسلسلي
      const device = await prisma.device.findUnique({
        where: { id: '123456789012346' },
        select: {
          id: true,
          model: true,
          status: true,
          warehouseId: true,
          supplierId: true,
          price: true,
          condition: true,
          dateAdded: true
        }
      });

      if (device) {
        console.log('\nالجهاز المرتبط:');
        console.log(`- الرقم التسلسلي: ${device.id}`);
        console.log(`- الموديل: ${device.model}`);
        console.log(`- الحالة: ${device.status}`);
        console.log(`- معرف المخزن: ${device.warehouseId}`);
        console.log(`- معرف المورد: ${device.supplierId}`);
        console.log(`- السعر: ${device.price}`);
        console.log(`- الحالة: ${device.condition}`);
        console.log(`- تاريخ الإضافة: ${device.dateAdded}`);

        // فحص ما إذا كان الجهاز مرتبط بعمليات أخرى
        const sales = await prisma.sale.count({
          where: {
            items: {
              some: {
                deviceId: device.id
              }
            }
          }
        });

        const maintenanceOrders = await prisma.maintenanceOrder.count({
          where: {
            items: {
              some: {
                deviceId: device.id
              }
            }
          }
        });

        const returns = await prisma.return.count({
          where: {
            items: {
              some: {
                deviceId: device.id
              }
            }
          }
        });

        const evaluationOrders = await prisma.evaluationOrder.count({
          where: {
            items: {
              some: {
                deviceId: device.id
              }
            }
          }
        });

        console.log('\nالعمليات المرتبطة بالجهاز:');
        console.log(`- المبيعات: ${sales}`);
        console.log(`- أوامر الصيانة: ${maintenanceOrders}`);
        console.log(`- المرتجعات: ${returns}`);
        console.log(`- أوامر التقييم: ${evaluationOrders}`);

      } else {
        console.log('\nلم يتم العثور على الجهاز بالرقم التسلسلي: 123456789012346');
      }

      // محاولة حذف أمر التوريد
      console.log('\n=== محاولة حذف أمر التوريد ===');
      try {
        await prisma.supplyOrder.delete({
          where: { id: supplyOrder.id }
        });
        console.log('✅ تم حذف أمر التوريد بنجاح');
      } catch (deleteError) {
        console.log('❌ فشل في حذف أمر التوريد:', deleteError);
      }

    } else {
      console.log('لا توجد أوامر توريد');
    }

  } catch (error) {
    console.error('خطأ في فحص البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSupplyOrderAndDevice();
