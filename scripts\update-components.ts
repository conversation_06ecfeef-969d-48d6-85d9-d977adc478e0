#!/usr/bin/env tsx

/**
 * سكريبت تحديث المكونات للنظام الجديد
 * يقوم بتحديث جميع المكونات والصفحات للتوافق مع هيكل قاعدة البيانات الجديد
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.join(__dirname, '..')

// مسارات المكونات والصفحات
const PATHS = {
  components: path.join(projectRoot, 'components'),
  pages: path.join(projectRoot, 'app', '(main)'),
  api: path.join(projectRoot, 'app', 'api'),
  lib: path.join(projectRoot, 'lib'),
  hooks: path.join(projectRoot, 'hooks')
}

// التحديثات المطلوبة
const UPDATES = {
  imports: {
    // تحديث imports للنماذج الجديدة
    'import.*{.*Device.*}.*from.*prisma': 'import { Device, DeviceMovement, WarehouseStock } from "@prisma/client"',
    'import.*{.*Warehouse.*}.*from.*prisma': 'import { Warehouse, WarehouseStock } from "@prisma/client"',
    'import.*{.*SupplyOrder.*}.*from.*prisma': 'import { SupplyOrder, SupplyOrderItem } from "@prisma/client"',
    'import.*{.*Sale.*}.*from.*prisma': 'import { Sale, SaleItem } from "@prisma/client"',
    'import.*{.*Return.*}.*from.*prisma': 'import { Return, ReturnItem } from "@prisma/client"'
  },
  
  fieldMappings: {
    // تحديث أسماء الحقول
    'supplyOrderId': 'orderNumber',
    'soNumber': 'saleNumber',
    'roNumber': 'returnNumber',
    'orderId': 'orderNumber',
    'employeeName': 'employee.name',
    'warehouseName': 'warehouse.name',
    'clientName': 'client.name',
    'model': 'deviceModel.name',
    'manufacturer': 'manufacturer.name'
  },
  
  queries: {
    // تحديث الاستعلامات
    'findMany\\(\\)': `findMany({
      include: {
        manufacturer: true,
        deviceModel: true,
        warehouse: true,
        supplier: true
      }
    })`,
    
    'create\\(\\{\\s*data:': `create({
      data: {
        ...data,
        orderNumber: await generateOrderNumber('PREFIX')
      }`
  }
}

/**
 * قراءة ملف وتطبيق التحديثات
 */
function updateFile(filePath: string): boolean {
  try {
    if (!fs.existsSync(filePath)) {
      return false
    }

    let content = fs.readFileSync(filePath, 'utf8')
    let updated = false

    // تحديث imports
    for (const [pattern, replacement] of Object.entries(UPDATES.imports)) {
      const regex = new RegExp(pattern, 'g')
      if (regex.test(content)) {
        content = content.replace(regex, replacement)
        updated = true
        console.log(`✅ تم تحديث import في: ${filePath}`)
      }
    }

    // تحديث أسماء الحقول
    for (const [oldField, newField] of Object.entries(UPDATES.fieldMappings)) {
      const regex = new RegExp(`\\b${oldField}\\b`, 'g')
      if (regex.test(content)) {
        content = content.replace(regex, newField)
        updated = true
        console.log(`✅ تم تحديث حقل ${oldField} إلى ${newField} في: ${filePath}`)
      }
    }

    // تحديث الاستعلامات
    for (const [pattern, replacement] of Object.entries(UPDATES.queries)) {
      const regex = new RegExp(pattern, 'g')
      if (regex.test(content)) {
        content = content.replace(regex, replacement)
        updated = true
        console.log(`✅ تم تحديث استعلام في: ${filePath}`)
      }
    }

    // إضافة التحديثات الخاصة بكل نوع ملف
    if (filePath.includes('.tsx') || filePath.includes('.ts')) {
      content = updateTypeScriptFile(content, filePath)
      updated = true
    }

    if (updated) {
      // إنشاء نسخة احتياطية
      const backupPath = filePath + '.backup'
      if (!fs.existsSync(backupPath)) {
        fs.copyFileSync(filePath, backupPath)
      }
      
      // كتابة المحتوى المحدث
      fs.writeFileSync(filePath, content, 'utf8')
      console.log(`📝 تم تحديث الملف: ${filePath}`)
    }

    return updated
  } catch (error) {
    console.error(`❌ خطأ في تحديث الملف ${filePath}:`, error)
    return false
  }
}

/**
 * تحديثات خاصة بملفات TypeScript
 */
function updateTypeScriptFile(content: string, filePath: string): string {
  // إضافة import للـ utility functions
  if (!content.includes('generateOrderNumber') && content.includes('prisma')) {
    content = `import { generateOrderNumber } from '@/lib/utils/order-utils'\n${content}`
  }

  // تحديث استعلامات Device
  if (content.includes('prisma.device.')) {
    content = content.replace(
      /prisma\.device\.findMany\(\)/g,
      `prisma.device.findMany({
        include: {
          manufacturer: true,
          deviceModel: true,
          warehouse: true,
          supplier: true
        }
      })`
    )
  }

  // تحديث استعلامات SupplyOrder
  if (content.includes('prisma.supplyOrder.')) {
    content = content.replace(
      /prisma\.supplyOrder\.create\(/g,
      `prisma.supplyOrder.create({
        data: {
          ...data,
          orderNumber: await generateOrderNumber('SO')
        }`
    )
  }

  // تحديث استعلامات Sale
  if (content.includes('prisma.sale.')) {
    content = content.replace(
      /prisma\.sale\.create\(/g,
      `prisma.sale.create({
        data: {
          ...data,
          saleNumber: await generateOrderNumber('SL')
        }`
    )
  }

  return content
}

/**
 * مسح مجلد بشكل تكراري
 */
function scanDirectory(dirPath: string, extensions: string[] = ['.tsx', '.ts', '.js', '.jsx']): string[] {
  const files: string[] = []
  
  if (!fs.existsSync(dirPath)) {
    return files
  }

  const items = fs.readdirSync(dirPath)
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      // تجاهل مجلدات معينة
      if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(item)) {
        files.push(...scanDirectory(fullPath, extensions))
      }
    } else {
      // إضافة الملف إذا كان له امتداد مطلوب
      if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath)
      }
    }
  }
  
  return files
}

/**
 * إنشاء utility functions جديدة
 */
function createUtilityFiles() {
  const utilsDir = path.join(PATHS.lib, 'utils')
  
  // إنشاء مجلد utils إذا لم يكن موجوداً
  if (!fs.existsSync(utilsDir)) {
    fs.mkdirSync(utilsDir, { recursive: true })
  }

  // إنشاء order-utils.ts
  const orderUtilsContent = `
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

/**
 * مولد أرقام الأوامر الموحد
 */
export async function generateOrderNumber(prefix: string): Promise<string> {
  const year = new Date().getFullYear()
  const month = (new Date().getMonth() + 1).toString().padStart(2, '0')
  
  // البحث عن آخر رقم مستخدم
  const lastOrder = await prisma.$queryRaw\`
    SELECT order_number FROM (
      SELECT order_number FROM supply_orders WHERE order_number LIKE \${prefix + '-' + year + '-' + month + '-%'}
      UNION ALL
      SELECT sale_number as order_number FROM sales WHERE sale_number LIKE \${prefix + '-' + year + '-' + month + '-%'}
      UNION ALL
      SELECT return_number as order_number FROM returns WHERE return_number LIKE \${prefix + '-' + year + '-' + month + '-%'}
      UNION ALL
      SELECT order_number FROM evaluation_orders WHERE order_number LIKE \${prefix + '-' + year + '-' + month + '-%'}
    ) orders 
    ORDER BY order_number DESC 
    LIMIT 1
  \`
  
  let nextNumber = 1
  if (Array.isArray(lastOrder) && lastOrder.length > 0) {
    const lastOrderNumber = lastOrder[0].order_number
    const numberPart = lastOrderNumber.split('-').pop()
    if (numberPart) {
      nextNumber = parseInt(numberPart) + 1
    }
  }
  
  return \`\${prefix}-\${year}-\${month}-\${nextNumber.toString().padStart(4, '0')}\`
}

/**
 * تحديث مخزون المخزن
 */
export async function updateWarehouseStock(
  warehouseId: number,
  manufacturerId: number,
  deviceModelId: number,
  condition: string,
  quantityChange: number
) {
  return await prisma.warehouseStock.upsert({
    where: {
      warehouseId_manufacturerId_deviceModelId_condition: {
        warehouseId,
        manufacturerId,
        deviceModelId,
        condition
      }
    },
    update: {
      totalQuantity: { increment: quantityChange },
      availableQuantity: { increment: quantityChange }
    },
    create: {
      warehouseId,
      manufacturerId,
      deviceModelId,
      condition,
      totalQuantity: Math.max(0, quantityChange),
      availableQuantity: Math.max(0, quantityChange)
    }
  })
}

/**
 * تسجيل حركة الجهاز
 */
export async function logDeviceMovement(data: {
  deviceId: string
  movementType: string
  fromWarehouseId?: number
  toWarehouseId?: number
  fromStatus?: string
  toStatus: string
  referenceType?: string
  referenceId?: string
  referenceNumber?: string
  employeeId: number
  employeeName: string
  notes?: string
}) {
  return await prisma.deviceMovement.create({
    data: {
      ...data,
      movementDate: new Date()
    }
  })
}

/**
 * التحقق من توفر الجهاز
 */
export async function isDeviceAvailable(deviceId: string): Promise<boolean> {
  const device = await prisma.device.findUnique({
    where: { id: deviceId },
    select: { status: true, isActive: true }
  })
  
  return device?.isActive === true && device?.status === 'في المخزن'
}
`

  fs.writeFileSync(path.join(utilsDir, 'order-utils.ts'), orderUtilsContent.trim())
  console.log('✅ تم إنشاء order-utils.ts')

  // إنشاء device-utils.ts
  const deviceUtilsContent = `
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

/**
 * الحصول على تاريخ الجهاز الكامل
 */
export async function getDeviceHistory(deviceId: string) {
  return await prisma.deviceMovement.findMany({
    where: { deviceId },
    include: {
      employee: { select: { name: true, username: true } }
    },
    orderBy: { movementDate: 'desc' }
  })
}

/**
 * البحث عن الأجهزة المتاحة
 */
export async function getAvailableDevices(filters: {
  warehouseId?: number
  manufacturerId?: number
  deviceModelId?: number
  condition?: string
  status?: string
}) {
  return await prisma.device.findMany({
    where: {
      ...filters,
      status: filters.status || 'في المخزن',
      isActive: true
    },
    include: {
      manufacturer: true,
      deviceModel: true,
      warehouse: true,
      supplier: true
    },
    orderBy: {
      createdAt: 'desc'
    }
  })
}

/**
 * حساب قيمة المخزون
 */
export async function calculateWarehouseValue(warehouseId: number) {
  const devices = await prisma.device.findMany({
    where: {
      warehouseId,
      status: 'في المخزن',
      isActive: true
    },
    select: {
      costPrice: true,
      price: true
    }
  })

  return {
    totalCostValue: devices.reduce((sum, d) => sum + (d.costPrice || 0), 0),
    totalSellingValue: devices.reduce((sum, d) => sum + (d.price || 0), 0),
    deviceCount: devices.length
  }
}
`

  fs.writeFileSync(path.join(utilsDir, 'device-utils.ts'), deviceUtilsContent.trim())
  console.log('✅ تم إنشاء device-utils.ts')
}

/**
 * الدالة الرئيسية
 */
async function main() {
  console.log('🚀 بدء تحديث المكونات للنظام الجديد...')
  
  try {
    // إنشاء utility files
    console.log('📁 إنشاء ملفات الـ utilities...')
    createUtilityFiles()

    let totalFiles = 0
    let updatedFiles = 0

    // مسح وتحديث جميع الملفات
    for (const [pathName, pathValue] of Object.entries(PATHS)) {
      if (!fs.existsSync(pathValue)) {
        console.log(`⚠️  المسار غير موجود: ${pathValue}`)
        continue
      }

      console.log(`\n📂 مسح ${pathName}...`)
      const files = scanDirectory(pathValue)
      totalFiles += files.length

      for (const filePath of files) {
        if (updateFile(filePath)) {
          updatedFiles++
        }
      }
    }

    console.log(`\n🎉 تم الانتهاء من التحديث!`)
    console.log(`📊 الإحصائيات:`)
    console.log(`   - إجمالي الملفات المفحوصة: ${totalFiles}`)
    console.log(`   - الملفات المحدثة: ${updatedFiles}`)
    console.log(`   - الملفات غير المحدثة: ${totalFiles - updatedFiles}`)

    console.log(`\n📝 الخطوات التالية:`)
    console.log(`   1. تحقق من الملفات المحدثة`)
    console.log(`   2. اختبر التطبيق`)
    console.log(`   3. أصلح أي أخطاء تايب`)
    console.log(`   4. احذف ملفات النسخ الاحتياطية بعد التأكد`)

  } catch (error) {
    console.error('❌ خطأ في تحديث المكونات:', error)
    process.exit(1)
  }
}

// تشغيل البرنامج
main()
`
