// اختبار APIs البسيطة
async function testSimpleAPIs() {
  try {
    console.log('🧪 اختبار APIs البسيطة...\n');
    
    // اختبار 1: تحميل العملاء من API البسيط
    console.log('1️⃣ اختبار /api/clients-simple...');
    const clientsResponse = await fetch('http://localhost:9005/api/clients-simple?limit=100');
    
    if (clientsResponse.ok) {
      const clientsData = await clientsResponse.json();
      console.log(`✅ العملاء البسيط: ${clientsData.data?.length || 0} عميل`);
    } else {
      console.log(`❌ خطأ في العملاء البسيط: ${clientsResponse.status}`);
    }
    
    // اختبار 2: تحميل الموردين من API البسيط
    console.log('\n2️⃣ اختبار /api/suppliers-simple...');
    const suppliersResponse = await fetch('http://localhost:9005/api/suppliers-simple?limit=100');
    
    if (suppliersResponse.ok) {
      const suppliersData = await suppliersResponse.json();
      console.log(`✅ الموردين البسيط: ${suppliersData.data?.length || 0} مورد`);
    } else {
      console.log(`❌ خطأ في الموردين البسيط: ${suppliersResponse.status}`);
    }
    
    // اختبار 3: إضافة عميل جديد عبر API البسيط
    console.log('\n3️⃣ اختبار POST /api/clients-simple...');
    const newClientResponse = await fetch('http://localhost:9005/api/clients-simple', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: `عميل بسيط ${Date.now()}`,
        phone: '0551234567',
        email: `simple${Date.now()}@example.com`
      })
    });
    
    if (newClientResponse.ok) {
      const newClient = await newClientResponse.json();
      console.log(`✅ تم إنشاء عميل جديد عبر API البسيط: ${newClient.name} (ID: ${newClient.id})`);
    } else {
      const errorText = await newClientResponse.text();
      console.log(`❌ خطأ في إنشاء العميل البسيط: ${newClientResponse.status} - ${errorText}`);
    }
    
    // اختبار 4: إضافة مورد جديد عبر API البسيط
    console.log('\n4️⃣ اختبار POST /api/suppliers-simple...');
    const newSupplierResponse = await fetch('http://localhost:9005/api/suppliers-simple', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: `مورد بسيط ${Date.now()}`,
        phone: '0559876543',
        email: `supplier${Date.now()}@example.com`
      })
    });
    
    if (newSupplierResponse.ok) {
      const newSupplier = await newSupplierResponse.json();
      console.log(`✅ تم إنشاء مورد جديد عبر API البسيط: ${newSupplier.name} (ID: ${newSupplier.id})`);
    } else {
      const errorText = await newSupplierResponse.text();
      console.log(`❌ خطأ في إنشاء المورد البسيط: ${newSupplierResponse.status} - ${errorText}`);
    }
    
    console.log('\n🎯 ملخص: إذا نجحت جميع الاختبارات أعلاه، فإن APIs البسيطة تعمل بشكل صحيح');
    
  } catch (error) {
    console.error('❌ خطأ عام في الاختبار:', error.message);
  }
}

testSimpleAPIs();
