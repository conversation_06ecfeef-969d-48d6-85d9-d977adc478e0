import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanSupplyOrders() {
  try {
    console.log('بدء تنظيف أوامر التوريد...');

    // البحث عن جميع أوامر التوريد
    const allOrders = await prisma.supplyOrder.findMany({
      select: { id: true, supplyOrderId: true }
    });

    console.log(`تم العثور على ${allOrders.length} أمر توريد:`);
    allOrders.forEach(order => {
      console.log(`- ID: ${order.id}, Number: ${order.supplyOrderId}`);
    });

    // فلترة الأوامر ذات الأرقام غير الصحيحة
    const invalidOrders = allOrders.filter(order => {
      const isValid = /^SUP-\d+$/.test(order.supplyOrderId);
      return !isValid;
    });

    console.log(`\nالأوامر ذات الأرقام غير الصحيحة (${invalidOrders.length}):`);
    invalidOrders.forEach(order => {
      console.log(`- ID: ${order.id}, Number: ${order.supplyOrderId}`);
    });

    if (invalidOrders.length > 0) {
      // حذف الأوامر غير الصحيحة
      for (const order of invalidOrders) {
        await prisma.supplyOrder.delete({
          where: { id: order.id }
        });
        console.log(`تم حذف الأمر: ${order.supplyOrderId}`);
      }
    }

    // إعادة ترقيم الأوامر المتبقية
    const validOrders = await prisma.supplyOrder.findMany({
      orderBy: { createdAt: 'asc' }
    });

    console.log(`\nإعادة ترقيم ${validOrders.length} أمر متبقي...`);

    for (let i = 0; i < validOrders.length; i++) {
      const newNumber = `SUP-${i + 1}`;
      
      if (validOrders[i].supplyOrderId !== newNumber) {
        await prisma.supplyOrder.update({
          where: { id: validOrders[i].id },
          data: { supplyOrderId: newNumber }
        });
        console.log(`تم تحديث ${validOrders[i].supplyOrderId} إلى ${newNumber}`);
      }
    }

    console.log('\nتم تنظيف وإعادة ترقيم أوامر التوريد بنجاح!');

    // عرض النتيجة النهائية
    const finalOrders = await prisma.supplyOrder.findMany({
      select: { id: true, supplyOrderId: true },
      orderBy: { supplyOrderId: 'asc' }
    });

    console.log('\nالأوامر النهائية:');
    finalOrders.forEach(order => {
      console.log(`- ${order.supplyOrderId}`);
    });

  } catch (error) {
    console.error('خطأ في تنظيف أوامر التوريد:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanSupplyOrders();
