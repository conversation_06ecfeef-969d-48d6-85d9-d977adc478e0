import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';
import {
  extractApiQueryParams,
  paginationToPrisma,
  sortToPrisma,
  searchToPrisma,
  filtersToPrisma,
  createPaginatedResponse,
  validatePaginationParams,
  validateSortParams
} from '@/lib/api-helpers';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استخراج معاملات API
    const allowedFilters = ['clientName', 'employeeName', 'deviceModel', 'status', 'paymentMethod', 'dateFrom', 'dateTo', 'totalMin', 'totalMax'];
    const queryParams = extractApiQueryParams(request, allowedFilters);

    // التحقق من صحة المعاملات
    if (queryParams.pagination) {
      const paginationErrors = validatePaginationParams(queryParams.pagination);
      if (paginationErrors.length > 0) {
        return NextResponse.json({ error: paginationErrors.join(', ') }, { status: 400 });
      }
    }

    const allowedSortFields = ['id', 'clientName', 'employeeName', 'date', 'total', 'createdAt'];
    if (queryParams.sort) {
      const sortErrors = validateSortParams(queryParams.sort, allowedSortFields);
      if (sortErrors.length > 0) {
        return NextResponse.json({ error: sortErrors.join(', ') }, { status: 400 });
      }
    }

    // بناء شروط Prisma
    const paginationPrisma = paginationToPrisma(queryParams.pagination || {});
    const sortPrisma = sortToPrisma(queryParams.sort, allowedSortFields);
    const searchPrisma = searchToPrisma(queryParams.search, ['clientName', 'employeeName', 'notes']);

    // معالجة الفلاتر المخصصة
    const filtersPrisma = filtersToPrisma(queryParams.filters || {});

    // معالجة فلاتر التاريخ
    if (queryParams.filters?.dateFrom || queryParams.filters?.dateTo) {
      filtersPrisma.date = {};
      if (queryParams.filters.dateFrom) {
        filtersPrisma.date.gte = queryParams.filters.dateFrom;
      }
      if (queryParams.filters.dateTo) {
        filtersPrisma.date.lte = queryParams.filters.dateTo;
      }
      delete filtersPrisma.dateFrom;
      delete filtersPrisma.dateTo;
    }

    // معالجة فلاتر المجموع
    if (queryParams.filters?.totalMin || queryParams.filters?.totalMax) {
      filtersPrisma.total = {};
      if (queryParams.filters.totalMin) {
        filtersPrisma.total.gte = Number(queryParams.filters.totalMin);
      }
      if (queryParams.filters.totalMax) {
        filtersPrisma.total.lte = Number(queryParams.filters.totalMax);
      }
      delete filtersPrisma.totalMin;
      delete filtersPrisma.totalMax;
    }

    // معالجة فلتر موديل الجهاز
    if (queryParams.filters?.deviceModel) {
      filtersPrisma.items = {
        some: {
          model: {
            contains: queryParams.filters.deviceModel,
            mode: 'insensitive'
          }
        }
      };
    }

    // دمج شروط البحث والتصفية
    const whereClause = {
      ...filtersPrisma,
      ...(searchPrisma && { ...searchPrisma })
    };

    // جلب العدد الإجمالي
    const total = await prisma.sale.count({ where: whereClause });

    // جلب البيانات مع العناصر
    const sales = await prisma.sale.findMany({
      where: whereClause,
      include: {
        items: true
      },
      ...paginationPrisma,
      orderBy: sortPrisma || { id: 'desc' }
    });

    // إنشاء الاستجابة المرقمة
    const response = createPaginatedResponse(sales, total, queryParams.pagination || {}, queryParams);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Failed to fetch sales:', error);
    return NextResponse.json({ error: 'Failed to fetch sales' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newSale = await request.json();

    // Basic validation
    if (!newSale.clientName || !newSale.items || !Array.isArray(newSale.items) || newSale.items.length === 0) {
      return NextResponse.json(
        { error: 'Client name and items are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Generate unique SO number
      const soNumber = await generateUniqueId(tx, 'sale', 'SO-');

      // ✅ إنشاء opNumber إذا لم يكن موجوداً
      const opNumber = newSale.opNumber || soNumber; // استخدام soNumber كقيمة افتراضية

      // Create the sale in the database
      const sale = await tx.sale.create({
        data: {
          soNumber,
          opNumber, // ✅ استخدام القيمة المولدة
          date: newSale.date,
          clientName: newSale.clientName,
          warehouseName: newSale.warehouseName || 'المخزن الرئيسي',
          notes: newSale.notes || '',
          warrantyPeriod: newSale.warrantyPeriod || 'none',
          employeeName: newSale.employeeName || authResult.user!.username,
          attachments: newSale.attachments ? JSON.stringify(newSale.attachments) : '[]'
        }
      });

      // Create sale items
      if (newSale.items && Array.isArray(newSale.items)) {
        for (const item of newSale.items) {
          await tx.saleItem.create({
            data: {
              saleId: sale.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              price: parseFloat(item.price) || 0,
              condition: item.condition || 'جديد'
            }
          });
        }
      }

      // Update device statuses
      if (newSale.items && Array.isArray(newSale.items)) {
        for (const item of newSale.items) {
          if (item.deviceId) {
            // التحقق من وجود الجهاز أولاً
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: 'مباع' }
              });
            } else {
              console.warn(`Device ${item.deviceId} not found for sale ${sale.soNumber}`);
            }
          }
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created sale: ${sale.soNumber} for client ${sale.clientName}`,
        tableName: 'sale',
        recordId: sale.id.toString()
      });

      // Return sale with items
      const saleWithItems = await tx.sale.findUnique({
        where: { id: sale.id },
        include: { items: true }
      });

      return saleWithItems;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create sale:', error);
    return NextResponse.json({ error: 'Failed to create sale' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedSale = await request.json();

    if (!updatedSale.id) {
      return NextResponse.json(
        { error: 'Sale ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if sale exists
      const existingSale = await tx.sale.findUnique({
        where: { id: updatedSale.id },
        include: { items: true }
      });

      if (!existingSale) {
        throw new Error('Sale not found');
      }

      // Get the old items to check for removed devices
      const oldItems = existingSale.items || [];
      const newItems = updatedSale.items || [];
      const newItemIds = newItems.map((item: any) => item.deviceId).filter(Boolean);
      const oldItemIds = oldItems.map((item: any) => item.deviceId).filter(Boolean);

      // Find devices that were removed from the sale
      const removedDeviceIds = oldItemIds.filter((deviceId: string) => !newItemIds.includes(deviceId));

      // Reset status for removed devices
      for (const deviceId of removedDeviceIds) {
        const device = await tx.device.findUnique({
          where: { id: deviceId }
        });

        if (device) {
          await tx.device.update({
            where: { id: deviceId },
            data: { status: 'متاح للبيع' }
          });
        }
      }

      // Set status for new devices
      for (const item of newItems) {
        if (item.deviceId) {
          const device = await tx.device.findUnique({
            where: { id: item.deviceId }
          });

          if (device) {
            await tx.device.update({
              where: { id: item.deviceId },
              data: { status: 'مباع' }
            });
          }
        }
      }

      // Update the sale
      const sale = await tx.sale.update({
        where: { id: updatedSale.id },
        data: {
          opNumber: updatedSale.opNumber || existingSale.opNumber,
          date: updatedSale.date || existingSale.date,
          clientName: updatedSale.clientName || existingSale.clientName,
          warehouseName: updatedSale.warehouseName || existingSale.warehouseName,
          notes: updatedSale.notes !== undefined ? updatedSale.notes : existingSale.notes,
          warrantyPeriod: updatedSale.warrantyPeriod !== undefined ? updatedSale.warrantyPeriod : existingSale.warrantyPeriod,
          employeeName: updatedSale.employeeName || existingSale.employeeName,
          attachments: updatedSale.attachments ? JSON.stringify(updatedSale.attachments) : existingSale.attachments
        }
      });

      // Delete existing items
      await tx.saleItem.deleteMany({
        where: { saleId: updatedSale.id }
      });

      // Create new items
      if (updatedSale.items && Array.isArray(updatedSale.items)) {
        for (const item of updatedSale.items) {
          await tx.saleItem.create({
            data: {
              saleId: sale.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              price: parseFloat(item.price) || 0,
              condition: item.condition || 'جديد'
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated sale: ${sale.soNumber} for client ${sale.clientName}`,
        tableName: 'sale',
        recordId: sale.id.toString()
      });

      // Return sale with items
      const saleWithItems = await tx.sale.findUnique({
        where: { id: sale.id },
        include: { items: true }
      });

      return saleWithItems;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update sale:', error);

    if (error instanceof Error && error.message === 'Sale not found') {
      return NextResponse.json({ error: 'Sale not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update sale' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Sale ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if sale exists
      const existingSale = await tx.sale.findUnique({
        where: { id }
      });

      if (!existingSale) {
        throw new Error('Sale not found');
      }

      // Reset device statuses
      let items = [];
      try {
        items = typeof existingSale.items === 'string' ?
          JSON.parse(existingSale.items) : existingSale.items || [];
      } catch (error) {
        console.warn('Failed to parse items for device status reset:', error);
      }

      if (Array.isArray(items)) {
        for (const item of items) {
          if (item.deviceId) {
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: 'متاح للبيع' }
              });
            }
          }
        }
      }

      // Delete the sale
      await tx.sale.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted sale: ${existingSale.soNumber} for client ${existingSale.clientName}`,
        tableName: 'sale',
        recordId: id.toString()
      });

      return { message: 'Sale deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete sale:', error);

    if (error instanceof Error && error.message === 'Sale not found') {
      return NextResponse.json({ error: 'Sale not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete sale' }, { status: 500 });
  }
}
