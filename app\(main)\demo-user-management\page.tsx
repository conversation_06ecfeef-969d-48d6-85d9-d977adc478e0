'use client';

import { useState } from 'react';
import { useStore } from '@/context/store';
import { UserSwitcher } from '@/components/UserSwitcher';
import { PermissionGuard } from '@/components/PermissionGuard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { UserForm } from '@/components/user-form';
import { User, Plus, Users, Shield, Info } from 'lucide-react';

/**
 * صفحة تجريبية لاختبار نظام إدارة المستخدمين المحدث
 */
export default function DemoUserManagementPage() {
  const { currentUser, users, addUser } = useStore();
  const [isUserFormOpen, setIsUserFormOpen] = useState(false);

  const handleAddUser = (newUser: any, password?: string) => {
    addUser(newUser);
    setIsUserFormOpen(false);
    console.log('تم إضافة مستخدم جديد:', newUser);
    if (password) {
      console.log('كلمة المرور:', password);
    }
  };

  return (
    <PermissionGuard pageKey="dashboard">
      <div className="space-y-6 p-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">تجربة نظام إدارة المستخدمين المحدث</h1>
          <p className="text-muted-foreground">
            اختبار نظام الصلاحيات المرن في إضافة المستخدمين
          </p>
        </div>

        {/* تبديل المستخدم */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <UserSwitcher />
          
          {/* معلومات المستخدم الحالي */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                المستخدم الحالي
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div>
                  <p className="text-sm font-medium">الاسم:</p>
                  <p className="text-lg">{currentUser?.name || 'غير محدد'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">اسم المستخدم:</p>
                  <p className="text-lg">{currentUser?.username || 'غير محدد'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* شرح النظام الجديد */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">النظام الجديد:</p>
              <ul className="text-sm space-y-1 list-disc list-inside">
                <li>عند إضافة مستخدم جديد، ستظهر فقط الأقسام التي لديك صلاحية الوصول إليها</li>
                <li>لا يمكنك منح صلاحيات لأقسام لا تملك صلاحية الوصول إليها</li>
                <li>النظام مرن ويعتمد على صلاحيات المستخدم الحالي وليس على أدوار ثابتة</li>
                <li>جرب تبديل المستخدم ولاحظ الاختلاف في الأقسام المتاحة</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>

        {/* صلاحيات المستخدم الحالي */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              صلاحياتك الحالية
            </CardTitle>
            <CardDescription>
              الأقسام التي يمكنك منح صلاحيات لها عند إضافة مستخدم جديد
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {currentUser && Object.entries(currentUser.permissions).map(([key, permission]) => {
                if (permission.view || permission.create || permission.edit || permission.delete) {
                  const sectionNames: Record<string, string> = {
                    dashboard: 'لوحة التحكم',
                    supply: 'التوريد',
                    sales: 'المبيعات',
                    maintenance: 'الصيانة',
                    maintenanceTransfer: 'نقل الصيانة',
                    inventory: 'المخزون',
                    clients: 'العملاء',
                    warehouses: 'المستودعات',
                    returns: 'المرتجعات',
                    grading: 'التقييم',
                    track: 'التتبع',
                    stocktaking: 'الجرد',
                    acceptDevices: 'قبول الأجهزة',
                    messaging: 'الرسائل',
                    reports: 'التقارير',
                    users: 'المستخدمين',
                    settings: 'الإعدادات',
                    pricing: 'التسعير',
                    requests: 'الطلبات',
                    warehouseTransfer: 'نقل المستودعات',
                  };

                  const permissions = [];
                  if (permission.view) permissions.push('عرض');
                  if (permission.create) permissions.push('إنشاء');
                  if (permission.edit) permissions.push('تعديل');
                  if (permission.delete) permissions.push('حذف');

                  return (
                    <Badge key={key} variant="outline" className="flex items-center gap-1">
                      <span className="font-medium">{sectionNames[key] || key}</span>
                      <span className="text-xs text-muted-foreground">
                        ({permissions.join(', ')})
                      </span>
                    </Badge>
                  );
                }
                return null;
              })}
            </div>
          </CardContent>
        </Card>

        {/* قائمة المستخدمين الحاليين */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              المستخدمون الحاليون
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {users.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-muted-foreground">@{user.username}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={user.id === currentUser?.id ? "default" : "secondary"}>
                      {user.id === currentUser?.id ? "المستخدم الحالي" : "مستخدم"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* زر إضافة مستخدم جديد */}
        <Card>
          <CardHeader>
            <CardTitle>اختبار إضافة مستخدم جديد</CardTitle>
            <CardDescription>
              جرب إضافة مستخدم جديد ولاحظ الأقسام المتاحة بناءً على صلاحياتك
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Dialog open={isUserFormOpen} onOpenChange={setIsUserFormOpen}>
              <DialogTrigger asChild>
                <Button className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مستخدم جديد
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>إضافة مستخدم جديد</DialogTitle>
                </DialogHeader>
                <UserForm
                  isOpen={isUserFormOpen}
                  onClose={() => setIsUserFormOpen(false)}
                  onSave={handleAddUser}
                />
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>

        {/* تعليمات الاختبار */}
        <Card>
          <CardHeader>
            <CardTitle>كيفية اختبار النظام</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">1. جرب مع مدير النظام:</h4>
              <p className="text-sm text-muted-foreground">
                اختر "مدير النظام" من القائمة أعلاه، ثم اضغط "إضافة مستخدم جديد" - ستجد جميع الأقسام متاحة
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">2. جرب مع موظف مبيعات:</h4>
              <p className="text-sm text-muted-foreground">
                اختر "موظف مبيعات" ثم جرب إضافة مستخدم - ستجد فقط أقسام المبيعات والمخزون متاحة
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">3. جرب مع فني صيانة:</h4>
              <p className="text-sm text-muted-foreground">
                اختر "فني صيانة" ثم جرب إضافة مستخدم - ستجد فقط أقسام الصيانة متاحة
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">4. جرب مع أمين مخزن:</h4>
              <p className="text-sm text-muted-foreground">
                اختر "أمين مخزن" ثم جرب إضافة مستخدم - ستجد أقسام التوريد والمخزون متاحة
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </PermissionGuard>
  );
}
