#!/usr/bin/env tsx

// Script لاختبار API المواد والموديلات
async function testMaterialsAPI() {
  console.log('🧪 اختبار API إدارة المواد...');

  const baseUrl = 'http://localhost:9005';
  const token = Buffer.from('user:admin:admin').toString('base64');
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  try {
    // 1. اختبار API الشركات المصنعة
    console.log('\n1️⃣ اختبار API الشركات المصنعة...');
    
    const manufacturersResponse = await fetch(`${baseUrl}/api/manufacturers`, {
      method: 'GET',
      headers
    });

    console.log(`📊 حالة الاستجابة: ${manufacturersResponse.status}`);
    
    if (manufacturersResponse.ok) {
      const manufacturersData = await manufacturersResponse.json();
      console.log(`✅ تم جلب ${manufacturersData.data?.length || manufacturersData.length || 0} شركة مصنعة`);
      
      if (manufacturersData.data && manufacturersData.data.length > 0) {
        console.log('\n📋 أول 3 شركات:');
        manufacturersData.data.slice(0, 3).forEach((m: any) => {
          console.log(`- ID: ${m.id}, الاسم: "${m.name}"`);
        });
      }
    } else {
      const error = await manufacturersResponse.text();
      console.log(`❌ خطأ: ${error}`);
    }

    // 2. اختبار API موديلات الأجهزة
    console.log('\n2️⃣ اختبار API موديلات الأجهزة...');
    
    const modelsResponse = await fetch(`${baseUrl}/api/device-models`, {
      method: 'GET',
      headers
    });

    console.log(`📊 حالة الاستجابة: ${modelsResponse.status}`);
    
    if (modelsResponse.ok) {
      const modelsData = await modelsResponse.json();
      console.log(`✅ تم جلب ${modelsData.data?.length || modelsData.length || 0} موديل`);
      
      if (modelsData.data && modelsData.data.length > 0) {
        console.log('\n📱 أول 3 موديلات:');
        modelsData.data.slice(0, 3).forEach((m: any) => {
          console.log(`- ID: ${m.id}, الاسم: "${m.name}", الشركة: ${m.manufacturerId}, الفئة: ${m.category}`);
        });
      }
    } else {
      const error = await modelsResponse.text();
      console.log(`❌ خطأ: ${error}`);
    }

    // 3. اختبار إنشاء شركة جديدة
    console.log('\n3️⃣ اختبار إنشاء شركة جديدة...');
    
    const newManufacturer = {
      name: `شركة اختبار ${Date.now()}`
    };

    const createManufacturerResponse = await fetch(`${baseUrl}/api/manufacturers`, {
      method: 'POST',
      headers,
      body: JSON.stringify(newManufacturer)
    });

    console.log(`📊 حالة الاستجابة: ${createManufacturerResponse.status}`);
    
    if (createManufacturerResponse.ok) {
      const createdManufacturer = await createManufacturerResponse.json();
      console.log(`✅ تم إنشاء شركة: ${createdManufacturer.name} (ID: ${createdManufacturer.id})`);

      // 4. اختبار إنشاء موديل جديد
      console.log('\n4️⃣ اختبار إنشاء موديل جديد...');
      
      const newModel = {
        name: `موديل اختبار ${Date.now()}`,
        manufacturerId: createdManufacturer.id,
        category: 'هاتف ذكي'
      };

      const createModelResponse = await fetch(`${baseUrl}/api/device-models`, {
        method: 'POST',
        headers,
        body: JSON.stringify(newModel)
      });

      console.log(`📊 حالة الاستجابة: ${createModelResponse.status}`);
      
      if (createModelResponse.ok) {
        const createdModel = await createModelResponse.json();
        console.log(`✅ تم إنشاء موديل: ${createdModel.name} (ID: ${createdModel.id})`);
      } else {
        const error = await createModelResponse.text();
        console.log(`❌ خطأ في إنشاء الموديل: ${error}`);
      }
    } else {
      const error = await createManufacturerResponse.text();
      console.log(`❌ خطأ في إنشاء الشركة: ${error}`);
    }

    console.log('\n✅ تم الانتهاء من اختبار API المواد!');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  }
}

testMaterialsAPI();
