import React from 'react';
import { <PERSON>, <PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useDarkMode } from './useDarkMode';

interface DarkModeToggleProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  className?: string;
}

export const DarkModeToggle: React.FC<DarkModeToggleProps> = ({
  size = 'md',
  variant = 'outline',
  className = '',
}) => {
  const { isDarkMode, toggleDarkMode } = useDarkMode();

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24,
  };

  return (
    <Button
      variant={variant}
      size="icon"
      onClick={toggleDarkMode}
      className={`
        ${sizeClasses[size]} 
        transition-all duration-300 ease-in-out
        hover:scale-110 hover:rotate-12
        active:scale-95
        ${isDarkMode 
          ? 'bg-yellow-500/20 border-yellow-500/30 text-yellow-500 hover:bg-yellow-500/30' 
          : 'bg-slate-100 border-slate-300 text-slate-700 hover:bg-slate-200'
        }
        ${className}
      `}
      title={isDarkMode ? 'تبديل للوضع النهاري' : 'تبديل للوضع الليلي'}
    >
      <div className="relative">
        {isDarkMode ? (
          <Sun 
            size={iconSizes[size]} 
            className="animate-in spin-in-180 duration-300" 
          />
        ) : (
          <Moon 
            size={iconSizes[size]} 
            className="animate-in spin-in-180 duration-300" 
          />
        )}
      </div>
    </Button>
  );
};
