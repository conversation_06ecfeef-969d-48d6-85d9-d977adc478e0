/* تحسينات مظهر صفحة التوريد */

/* ===== التحسينات العامة للصفحة ===== */
.supply-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding: 1rem;
}

/* ===== تحسينات بطاقة الرأس ===== */
.header-card {
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6, #ec4899);
  animation: gradient-shift 4s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* ===== تحسينات البطاقات الرئيسية ===== */
.enhanced-supply-card,
.enhanced-stocktake-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.25rem;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  color: #1f2937 !important;
}

.enhanced-supply-card::before,
.enhanced-stocktake-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--card-accent, #3b82f6), var(--card-accent-end, #6366f1));
}

.enhanced-supply-card:hover,
.enhanced-stocktake-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.95);
}

/* ألوان مختلفة للبطاقات */
.card-new-stocktake { --card-accent: #3b82f6; --card-accent-end: #1d4ed8; }
.card-inventory-list { --card-accent: #10b981; --card-accent-end: #059669; }
.card-results { --card-accent: #f59e0b; --card-accent-end: #d97706; }

/* ===== أقسام البطاقات المختلفة ===== */
.info-section {
  --card-accent: #3b82f6;
  --card-accent-end: #1d4ed8;
}

.supplier-section {
  --card-accent: #10b981;
  --card-accent-end: #059669;
}

.items-section {
  --card-accent: #f59e0b;
  --card-accent-end: #d97706;
}

.summary-section {
  --card-accent: #8b5cf6;
  --card-accent-end: #7c3aed;
}

.actions-section {
  --card-accent: #06b6d4;
  --card-accent-end: #0891b2;
}

/* إصلاحات التباين والوضوح */
.enhanced-supply-card,
.enhanced-stocktake-card,
.info-section,
.supplier-section,
.items-section,
.summary-section,
.actions-section {
  color: #1f2937 !important;
}

.enhanced-supply-card h1,
.enhanced-supply-card h2,
.enhanced-supply-card h3,
.enhanced-supply-card h4,
.enhanced-supply-card h5,
.enhanced-supply-card h6,
.enhanced-stocktake-card h1,
.enhanced-stocktake-card h2,
.enhanced-stocktake-card h3,
.enhanced-stocktake-card h4,
.enhanced-stocktake-card h5,
.enhanced-stocktake-card h6,
.info-section h1,
.info-section h2,
.info-section h3,
.info-section h4,
.info-section h5,
.info-section h6 {
  color: #1f2937 !important;
}

.enhanced-supply-card p,
.enhanced-stocktake-card p,
.info-section p,
.supplier-section p,
.items-section p,
.summary-section p,
.actions-section p {
  color: #4b5563 !important;
}

.enhanced-supply-card label,
.enhanced-stocktake-card label,
.info-section label,
.supplier-section label,
.items-section label,
.summary-section label,
.actions-section label {
  color: #374151 !important;
  font-weight: 600;
}

/* ===== تحسينات الحقول والمدخلات ===== */
.enhanced-input {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.5);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  color: #1f2937 !important;
  font-weight: 500;
}

.enhanced-input:focus {
  background: rgba(255, 255, 255, 1);
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
  color: #1f2937 !important;
}

.enhanced-input:hover {
  border-color: #94a3b8;
  background: rgba(255, 255, 255, 0.95);
  color: #1f2937 !important;
}

.enhanced-input::placeholder {
  color: #6b7280 !important;
  font-weight: 400;
}

/* تحسين القوائم المنسدلة */
select,
.select-trigger {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(203, 213, 225, 0.5) !important;
  border-radius: 0.75rem !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
}

select:focus,
.select-trigger:focus {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  color: #1f2937 !important;
}

select option {
  color: #1f2937 !important;
  background: white !important;
}

/* تحسين منطقة النص */
textarea {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(203, 213, 225, 0.5) !important;
  border-radius: 0.75rem !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
}

textarea:focus {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  color: #1f2937 !important;
}

textarea::placeholder {
  color: #6b7280 !important;
  font-weight: 400;
}

/* ===== تحسينات الأزرار ===== */
.enhanced-button {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.enhanced-button:hover::before {
  left: 100%;
}

.enhanced-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* ===== تحسينات الجداول ===== */
.enhanced-table {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.enhanced-table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  backdrop-filter: blur(15px);
}

.enhanced-table-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.enhanced-table-row:hover {
  background: rgba(59, 130, 246, 0.05);
  transform: translateX(4px);
}

.enhanced-table-cell {
  padding: 0.75rem 1rem;
  border-right: 1px solid rgba(226, 232, 240, 0.3);
}

/* ===== تحسينات الشارات ===== */
.enhanced-badge {
  border-radius: 0.5rem;
  padding: 0.25rem 0.75rem;
  font-weight: 600;
  font-size: 0.75rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.enhanced-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* ===== شارات الحالات ===== */
.badge-pending {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.badge-completed {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.badge-draft {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

.badge-cancelled {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

/* ===== تحسينات النوافذ المنبثقة ===== */
.enhanced-dialog {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.enhanced-dialog-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 1.5rem 1.5rem 0 0;
  position: relative;
  overflow: hidden;
}

.enhanced-dialog-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6);
}

/* ===== تحسينات منطقة التمرير ===== */
.enhanced-scroll-area {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.enhanced-scroll-area::-webkit-scrollbar {
  width: 8px;
}

.enhanced-scroll-area::-webkit-scrollbar-track {
  background: rgba(226, 232, 240, 0.3);
  border-radius: 4px;
}

.enhanced-scroll-area::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.enhanced-scroll-area::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
}

/* ===== تحسينات الرسائل التوضيحية ===== */
.info-message {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

.success-message {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

.warning-message {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.05));
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

/* ===== تحسينات الأيقونات ===== */
.enhanced-icon {
  transition: all 0.3s ease;
}

.enhanced-icon:hover {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* ===== تحسينات الرسوم المتحركة ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* ===== تحسينات الحالة الفارغة ===== */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(226, 232, 240, 0.4));
  border-radius: 1.5rem;
  border: 2px dashed rgba(203, 213, 225, 0.5);
  backdrop-filter: blur(10px);
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
  filter: grayscale(0.3);
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.empty-state-description {
  color: #94a3b8;
  font-size: 0.875rem;
}

/* ===== الوضع الليلي (Dark Mode) ===== */

/* متغيرات الألوان للوضع الليلي */
.dark-mode {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
  --accent-primary: #3b82f6;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
}

/* خلفية الصفحة في الوضع الليلي */
.dark-mode .supply-page {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: var(--text-primary);
}

/* البطاقات في الوضع الليلي */
.dark-mode .enhanced-supply-card,
.dark-mode .enhanced-stocktake-card,
.dark-mode .header-card,
.dark-mode .enhanced-dialog,
.dark-mode .info-section,
.dark-mode .supplier-section,
.dark-mode .items-section,
.dark-mode .summary-section,
.dark-mode .actions-section {
  background: rgba(30, 41, 59, 0.9) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-supply-card::before,
.dark-mode .enhanced-stocktake-card::before,
.dark-mode .header-card::before,
.dark-mode .enhanced-dialog-header::before,
.dark-mode .info-section::before,
.dark-mode .supplier-section::before,
.dark-mode .items-section::before,
.dark-mode .summary-section::before,
.dark-mode .actions-section::before {
  background: linear-gradient(90deg, var(--accent-primary), #6366f1, #8b5cf6);
}

/* الحقول والمدخلات في الوضع الليلي */
.dark-mode .enhanced-input {
  background: rgba(51, 65, 85, 0.8) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode .enhanced-input:focus {
  background: rgba(51, 65, 85, 1) !important;
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-input::placeholder {
  color: var(--text-muted) !important;
  font-weight: 400;
}

/* القوائم المنسدلة في الوضع الليلي */
.dark-mode select,
.dark-mode .select-trigger {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode select:focus,
.dark-mode .select-trigger:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode select option {
  background: rgba(30, 41, 59, 0.95) !important;
  color: var(--text-primary) !important;
}

/* منطقة النص في الوضع الليلي */
.dark-mode textarea {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.dark-mode textarea:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  color: var(--text-primary) !important;
}

.dark-mode textarea::placeholder {
  color: var(--text-muted) !important;
  font-weight: 400;
}

/* الأزرار في الوضع الليلي */
.dark-mode .enhanced-button {
  background: rgba(51, 65, 85, 0.8) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button:hover {
  background: rgba(71, 85, 105, 0.9) !important;
  border-color: var(--accent-primary) !important;
}

/* الأزرار البيضاء في الوضع الليلي */
.dark-mode .enhanced-button.bg-white {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.6) !important;
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-button.bg-white:hover {
  background: rgba(71, 85, 105, 1) !important;
  border-color: var(--accent-primary) !important;
  color: var(--text-primary) !important;
}

/* الجداول في الوضع الليلي */
.dark-mode .enhanced-table {
  background: rgba(30, 41, 59, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .enhanced-table-header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
}

.dark-mode .enhanced-table-row:hover {
  background: rgba(59, 130, 246, 0.1) !important;
}

.dark-mode .enhanced-table-cell {
  border-color: rgba(71, 85, 105, 0.3) !important;
  color: var(--text-primary) !important;
}

/* الشارات في الوضع الليلي */
.dark-mode .enhanced-badge {
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
}

.dark-mode .badge-pending {
  background: linear-gradient(135deg, #d97706, #b45309) !important;
}

.dark-mode .badge-completed {
  background: linear-gradient(135deg, #059669, #047857) !important;
}

.dark-mode .badge-draft {
  background: linear-gradient(135deg, #4b5563, #374151) !important;
}

.dark-mode .badge-cancelled {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
}

/* الرسائل في الوضع الليلي */
.dark-mode .info-message {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(99, 102, 241, 0.1)) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  color: var(--text-primary) !important;
}

.dark-mode .success-message {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1)) !important;
  border: 1px solid rgba(16, 185, 129, 0.3) !important;
  color: var(--text-primary) !important;
}

.dark-mode .warning-message {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.1)) !important;
  border: 1px solid rgba(245, 158, 11, 0.3) !important;
  color: var(--text-primary) !important;
}

/* الحالة الفارغة في الوضع الليلي */
.dark-mode .empty-state {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.4)) !important;
  border: 2px dashed rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode .empty-state-title {
  color: var(--text-secondary) !important;
}

.dark-mode .empty-state-description {
  color: var(--text-muted) !important;
}

/* شريط التمرير في الوضع الليلي */
.dark-mode .enhanced-scroll-area {
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(51, 65, 85, 0.3);
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3) !important;
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #6366f1) !important;
}

.dark-mode .enhanced-scroll-area::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #4f46e5) !important;
}

/* ===== تحسينات خاصة لعناصر الصفحة ===== */

/* تحسين النصوص والتسميات */
.dark-mode .enhanced-supply-card h1,
.dark-mode .enhanced-supply-card h2,
.dark-mode .enhanced-supply-card h3,
.dark-mode .enhanced-supply-card h4,
.dark-mode .enhanced-supply-card h5,
.dark-mode .enhanced-supply-card h6,
.dark-mode .enhanced-stocktake-card h1,
.dark-mode .enhanced-stocktake-card h2,
.dark-mode .enhanced-stocktake-card h3,
.dark-mode .enhanced-stocktake-card h4,
.dark-mode .enhanced-stocktake-card h5,
.dark-mode .enhanced-stocktake-card h6,
.dark-mode .info-section h1,
.dark-mode .info-section h2,
.dark-mode .info-section h3,
.dark-mode .info-section h4,
.dark-mode .info-section h5,
.dark-mode .info-section h6 {
  color: var(--text-primary) !important;
}

.dark-mode .enhanced-supply-card p,
.dark-mode .enhanced-supply-card span,
.dark-mode .enhanced-supply-card div,
.dark-mode .enhanced-stocktake-card p,
.dark-mode .enhanced-stocktake-card span,
.dark-mode .enhanced-stocktake-card div,
.dark-mode .info-section p,
.dark-mode .supplier-section p,
.dark-mode .items-section p,
.dark-mode .summary-section p,
.dark-mode .actions-section p {
  color: var(--text-secondary) !important;
}

.dark-mode .enhanced-supply-card label,
.dark-mode .enhanced-stocktake-card label,
.dark-mode .info-section label,
.dark-mode .supplier-section label,
.dark-mode .items-section label,
.dark-mode .summary-section label,
.dark-mode .actions-section label {
  color: var(--text-primary) !important;
  font-weight: 500;
}

/* تحسين القوائم المنسدلة */
.dark-mode select,
.dark-mode .select-trigger {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode select:focus,
.dark-mode .select-trigger:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
}

/* تحسين منطقة النص */
.dark-mode textarea {
  background: rgba(51, 65, 85, 0.9) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  color: var(--text-primary) !important;
}

.dark-mode textarea:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
}

.dark-mode textarea::placeholder {
  color: var(--text-muted) !important;
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
  .supply-page {
    padding: 0.5rem;
  }

  .enhanced-supply-card {
    border-radius: 1rem;
    margin-bottom: 1rem;
  }

  .header-card {
    border-radius: 1rem;
  }

  .enhanced-table {
    font-size: 0.875rem;
  }

  .enhanced-table-cell {
    padding: 0.5rem;
  }

  .enhanced-dialog {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    border-radius: 1rem;
  }

  .enhanced-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .enhanced-input {
    font-size: 0.875rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state-icon {
    font-size: 2rem;
  }

  .empty-state-title {
    font-size: 1rem;
  }

  .empty-state-description {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .supply-page {
    padding: 0.25rem;
  }

  .enhanced-supply-card {
    border-radius: 0.75rem;
  }

  .enhanced-table {
    font-size: 0.75rem;
  }

  .enhanced-table-cell {
    padding: 0.375rem;
  }

  .enhanced-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }

  .enhanced-input {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}

/* ===== تحسينات الطباعة ===== */
@media print {
  .supply-page {
    background: white !important;
    padding: 0 !important;
  }

  .enhanced-supply-card,
  .header-card,
  .enhanced-dialog,
  .enhanced-table {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    color: #000 !important;
    backdrop-filter: none !important;
  }

  .enhanced-supply-card::before,
  .header-card::before,
  .enhanced-dialog-header::before {
    display: none !important;
  }

  .enhanced-input,
  .enhanced-button,
  .enhanced-badge {
    color: #000 !important;
    background: white !important;
    border: 1px solid #d1d5db !important;
  }

  .enhanced-table-row:hover {
    background: white !important;
    transform: none !important;
  }

  .enhanced-button:hover {
    transform: none !important;
    box-shadow: none !important;
  }

  .animate-fade-in-up,
  .animate-slide-in-right,
  .animate-pulse {
    animation: none !important;
  }

  .empty-state {
    background: white !important;
    border: 1px solid #d1d5db !important;
  }

  /* إخفاء عناصر غير ضرورية للطباعة */
  .enhanced-button,
  button,
  .dark-mode-toggle {
    display: none !important;
  }
}
