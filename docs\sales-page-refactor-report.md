# تقرير التغييرات الأخيرة على صفحة المبيعات
## Sales Page Refactor Report

### 📋 نظرة عامة
تم تطبيق نظام جديد لجلب البيانات عند الطلب (On-Demand Data Fetching) على صفحة المبيعات مع إعادة تصميم واجهة المستخدم لتكون أكثر بساطة وفعالية.

---

## 🔄 التغييرات الرئيسية

### 1. **نظام جلب البيانات الجديد (On-Demand Data Fetching)**

#### **قبل التغيير:**
- الاعتماد على بيانات محملة مسبقاً من `context/store.tsx`
- جلب جميع البيانات عند تحميل الصفحة
- عدم وجود نظام تصفية متقدم

#### **بعد التغيير:**
- نظام جلب البيانات عند الطلب باستخدام `lib/data-fetcher.ts`
- تحميل 15 عنصر في كل مرة مع إمكانية "تحميل المزيد"
- نظام تخزين مؤقت (Cache) مع TTL
- فلترة وبحث متقدم

#### **الكود المضاف:**
```typescript
// إدارة بيانات المبيعات الرئيسية (ON_DEMAND_DATA_SYSTEM)
const [salesData, setSalesData] = useState<{
  data: Sale[];
  total: number;
  hasMore: boolean;
  page: number;
}>({
  data: [],
  total: 0,
  hasMore: true,
  page: 1
});

// دالة جلب بيانات المبيعات الرئيسية
const loadSales = async (page: number = 1, append: boolean = false, customFilters = filters) => {
  setIsLoadingSales(true);
  try {
    const response = await fetchSales({
      page,
      limit: 15,
      sort: { field: 'createdAt', direction: 'desc' },
      filters: filterParams,
      search: searchTerm ? { query: searchTerm, fields: ['soNumber', 'opNumber', 'clientName'] } : undefined
    });

    if (append) {
      setSalesData(prev => ({
        ...prev,
        data: [...prev.data, ...response.data],
        hasMore: response.hasMore,
        page
      }));
    } else {
      setSalesData({
        data: response.data,
        total: response.total,
        hasMore: response.hasMore,
        page
      });
    }
  } catch (error) {
    // معالجة الأخطاء
  } finally {
    setIsLoadingSales(false);
  }
};
```

---

### 2. **تبسيط واجهة المستخدم**

#### **التغييرات:**
- **حذف كرت "فواتير المبيعات (0)"** من الصفحة الرئيسية
- **حذف خانة البحث والفلاتر** من الصفحة الرئيسية
- **إضافة زر "عرض الفواتير السابقة"** في شريط الأزرار

#### **الكود المحذوف:**
```typescript
// تم حذف هذا القسم بالكامل
{!isCreateMode && (
  <Card className="mb-6">
    <CardContent className="p-4">
      {/* واجهة البحث والفلاتر */}
      {/* جدول عرض الفواتير */}
    </CardContent>
  </Card>
)}
```

#### **الكود المضاف:**
```typescript
{/* زر عرض الفواتير السابقة */}
<Button
  onClick={() => setIsViewSalesDialogOpen(true)}
  variant="outline"
  className="enhanced-button bg-white border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400 px-6 h-12"
>
  <FileText className="ml-2 h-5 w-5" />
  📋 عرض الفواتير السابقة
</Button>
```

---

### 3. **النافذة المنبثقة للفواتير السابقة**

#### **الميزات الجديدة:**
- **نافذة منبثقة كاملة** مع خيارات البحث والفلترة
- **عرض آخر 15 فاتورة** مع زر "تحميل المزيد"
- **بحث متقدم** بالنص والفلاتر
- **فلترة بالعميل والموظف والتاريخ**

#### **إدارة الحالة:**
```typescript
// إدارة نافذة عرض الفواتير السابقة
const [isViewSalesDialogOpen, setIsViewSalesDialogOpen] = useState(false);
const [previousSales, setPreviousSales] = useState<Sale[]>([]);
const [salesPage, setSalesPage] = useState(1);
const [salesLoading, setSalesLoading] = useState(false);
const [hasMoreSales, setHasMoreSales] = useState(true);
const [salesSearchTerm, setSalesSearchTerm] = useState('');
const [salesFilters, setSalesFilters] = useState({
  clientName: 'all',
  employeeName: 'all',
  deviceModel: '',
  dateFrom: '',
  dateTo: '',
  dateRange: 'all' as 'all' | 'today' | 'week' | 'month' | 'custom'
});
```

#### **دالة جلب البيانات للنافذة:**
```typescript
const loadPreviousSales = async (page: number = 1, append: boolean = false, filters = salesFilters) => {
  setSalesLoading(true);
  try {
    const response = await fetchSales({
      page,
      limit: 15,
      sort: { field: 'createdAt', direction: 'desc' },
      filters: filterParams
    });

    if (append) {
      setPreviousSales(prev => [...prev, ...response.data]);
    } else {
      setPreviousSales(response.data);
    }

    setHasMoreSales(response.hasMore);
    setSalesPage(page);
  } catch (error) {
    // معالجة الأخطاء
  } finally {
    setSalesLoading(false);
  }
};
```

---

### 4. **توليد رقم الأمر التلقائي**

#### **API Endpoint جديد:**
```typescript
// app/api/sales/generate-so-number/route.ts
export async function POST(request: NextRequest) {
  try {
    const soNumber = await prisma.$transaction(async (tx) => {
      return await generateUniqueId(tx, 'sale', 'SO-');
    });

    return NextResponse.json({ soNumber });
  } catch (error) {
    return NextResponse.json(
      { error: 'فشل في توليد رقم الأمر' },
      { status: 500 }
    );
  }
}
```

#### **تعديل دالة إنشاء فاتورة جديدة:**
```typescript
const proceedWithNewSale = async () => {
  try {
    // توليد رقم أمر جديد من قاعدة البيانات
    const response = await fetch('/api/sales/generate-so-number', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    const { soNumber } = await response.json();
    
    // إعادة تعيين الصفحة مع رقم الأمر الجديد
    resetPage(false);
    setFormState(prev => ({ ...prev, soNumber: soNumber }));
    setIsCreateMode(true);
    
    toast({ 
      title: 'جاهز للبدء', 
      description: `تم تجهيز فاتورة جديدة برقم: ${soNumber}` 
    });
  } catch (error) {
    // معالجة الأخطاء
  }
};
```

---

## 🔧 التعديلات المطلوبة على context/store.tsx

### **المشكلة الحالية:**
- الاعتماد على بيانات محملة مسبقاً
- عدم وجود نظام تخزين مؤقت فعال
- جلب جميع البيانات مرة واحدة

### **التعديلات المقترحة:**

#### **1. إزالة البيانات المحملة مسبقاً:**
```typescript
// قبل التعديل
const [sales, setSales] = useState<Sale[]>([]);

// بعد التعديل - إزالة أو تقليل الاعتماد عليها
// const [sales, setSales] = useState<Sale[]>([]); // للاستخدام المحدود فقط
```

#### **2. إضافة نظام إدارة Cache:**
```typescript
// إضافة إدارة التخزين المؤقت
const [dataCache, setDataCache] = useState<{
  [key: string]: {
    data: any[];
    timestamp: number;
    ttl: number;
  }
}>({});

// دالة تنظيف Cache
const clearExpiredCache = () => {
  const now = Date.now();
  setDataCache(prev => {
    const newCache = { ...prev };
    Object.keys(newCache).forEach(key => {
      if (now - newCache[key].timestamp > newCache[key].ttl) {
        delete newCache[key];
      }
    });
    return newCache;
  });
};
```

#### **3. إضافة دوال مساعدة للبيانات عند الطلب:**
```typescript
// دالة عامة لجلب البيانات عند الطلب
const fetchDataOnDemand = async (
  endpoint: string, 
  params: any = {}, 
  useCache: boolean = true
) => {
  const cacheKey = `${endpoint}_${JSON.stringify(params)}`;
  
  // فحص Cache أولاً
  if (useCache && dataCache[cacheKey]) {
    const cached = dataCache[cacheKey];
    if (Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
  }

  // جلب البيانات من API
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params)
  });

  const data = await response.json();

  // حفظ في Cache
  if (useCache) {
    setDataCache(prev => ({
      ...prev,
      [cacheKey]: {
        data,
        timestamp: Date.now(),
        ttl: 2 * 60 * 1000 // 2 دقيقة
      }
    }));
  }

  return data;
};
```

---

## 📁 الملفات المتأثرة

### **الملفات المعدلة:**
1. **`app/(main)/sales/page.tsx`** - التعديل الرئيسي
2. **`app/api/sales/generate-so-number/route.ts`** - ملف جديد
3. **`lib/data-fetcher.ts`** - استخدام موجود
4. **`context/store.tsx`** - تعديلات مقترحة

### **الملفات الجديدة:**
- **`app/api/sales/generate-so-number/route.ts`**
- **`docs/sales-page-refactor-report.md`** (هذا الملف)

---

## 🎯 خطة التطبيق على الأقسام الأخرى

### **الأقسام المرشحة للتطبيق:**
1. **صفحة الطلبات (Requests)**
2. **صفحة المخزون (Inventory)**
3. **صفحة العملاء (Clients)**
4. **صفحة الموظفين (Employees)**

### **خطوات التطبيق لكل قسم:**

#### **المرحلة 1: تحليل القسم الحالي**
- فحص طريقة جلب البيانات الحالية
- تحديد نقاط الاعتماد على Store
- تحليل واجهة المستخدم الحالية

#### **المرحلة 2: إنشاء API Endpoints**
- إنشاء endpoints للبيانات عند الطلب
- إضافة دعم الفلترة والبحث
- إضافة دعم الترقيم (Pagination)

#### **المرحلة 3: تعديل واجهة المستخدم**
- إزالة جداول البيانات من الصفحة الرئيسية
- إضافة أزرار "عرض السجلات السابقة"
- إنشاء نوافذ منبثقة للعرض والبحث

#### **المرحلة 4: تحديث إدارة الحالة**
- إضافة state للنوافذ المنبثقة
- إضافة دوال جلب البيانات المنفصلة
- تحديث useEffect hooks

#### **المرحلة 5: الاختبار والتحسين**
- اختبار الأداء
- اختبار واجهة المستخدم
- تحسين التخزين المؤقت

---

## ✅ الفوائد المحققة

### **الأداء:**
- تحسين سرعة تحميل الصفحة
- تقليل استهلاك الذاكرة
- تحميل البيانات عند الحاجة فقط

### **تجربة المستخدم:**
- واجهة أكثر بساطة ووضوحاً
- بحث وفلترة متقدمة
- تحميل تدريجي للبيانات

### **الصيانة:**
- كود أكثر تنظيماً
- فصل منطق البيانات عن العرض
- سهولة إضافة ميزات جديدة

---

## 📝 ملاحظات للتطبيق المستقبلي

1. **استخدام نفس نمط State Management** لجميع الأقسام
2. **توحيد أسلوب النوافذ المنبثقة** عبر التطبيق
3. **تطبيق نفس نظام التخزين المؤقت** لجميع البيانات
4. **استخدام نفس نمط API Endpoints** للاتساق
5. **الحفاظ على نفس تصميم واجهة المستخدم** للتناسق

---

## 🛠️ أمثلة عملية للتطبيق

### **مثال: تطبيق النظام على صفحة الطلبات**

#### **1. إنشاء API Endpoint:**
```typescript
// app/api/requests/route.ts
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '15');

  const requests = await prisma.employeeRequest.findMany({
    skip: (page - 1) * limit,
    take: limit,
    orderBy: { createdAt: 'desc' },
    include: {
      employee: true,
      client: true
    }
  });

  return NextResponse.json({
    data: requests,
    hasMore: requests.length === limit,
    total: await prisma.employeeRequest.count()
  });
}
```

#### **2. تعديل صفحة الطلبات:**
```typescript
// app/(main)/requests/page.tsx
const [isViewRequestsDialogOpen, setIsViewRequestsDialogOpen] = useState(false);
const [previousRequests, setPreviousRequests] = useState<EmployeeRequest[]>([]);
const [requestsPage, setRequestsPage] = useState(1);
const [requestsLoading, setRequestsLoading] = useState(false);
const [hasMoreRequests, setHasMoreRequests] = useState(true);

const loadPreviousRequests = async (page: number = 1, append: boolean = false) => {
  setRequestsLoading(true);
  try {
    const response = await fetch(`/api/requests?page=${page}&limit=15`);
    const data = await response.json();

    if (append) {
      setPreviousRequests(prev => [...prev, ...data.data]);
    } else {
      setPreviousRequests(data.data);
    }

    setHasMoreRequests(data.hasMore);
    setRequestsPage(page);
  } catch (error) {
    console.error('خطأ في جلب الطلبات:', error);
  } finally {
    setRequestsLoading(false);
  }
};
```

#### **3. النافذة المنبثقة للطلبات:**
```typescript
<Dialog open={isViewRequestsDialogOpen} onOpenChange={setIsViewRequestsDialogOpen}>
  <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
    <DialogHeader>
      <DialogTitle>الطلبات السابقة</DialogTitle>
    </DialogHeader>

    {/* خيارات البحث والفلترة */}
    <Card>
      <CardContent className="p-4">
        {/* نفس نمط البحث والفلترة المستخدم في المبيعات */}
      </CardContent>
    </Card>

    {/* جدول الطلبات */}
    <div className="border rounded-lg overflow-hidden max-h-96 overflow-y-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>رقم الطلب</TableHead>
            <TableHead>الموظف</TableHead>
            <TableHead>العميل</TableHead>
            <TableHead>النوع</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead>التاريخ</TableHead>
            <TableHead>الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {previousRequests.map((request) => (
            <TableRow key={request.id}>
              <TableCell>{request.requestNumber}</TableCell>
              <TableCell>{request.employee?.name}</TableCell>
              <TableCell>{request.client?.name}</TableCell>
              <TableCell>{request.type}</TableCell>
              <TableCell>{request.status}</TableCell>
              <TableCell>{new Date(request.date).toLocaleDateString('ar-SA')}</TableCell>
              <TableCell>
                <Button size="sm" variant="outline">تحميل</Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>

    {/* زر تحميل المزيد */}
    {hasMoreRequests && (
      <Button
        onClick={() => loadPreviousRequests(requestsPage + 1, true)}
        disabled={requestsLoading}
        variant="outline"
      >
        تحميل المزيد (15 طلب)
      </Button>
    )}
  </DialogContent>
</Dialog>
```

---

## 🔄 تعديلات context/store.tsx المفصلة

### **الهيكل الحالي:**
```typescript
// context/store.tsx - الهيكل القديم
export const StoreProvider = ({ children }: { children: React.ReactNode }) => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [requests, setRequests] = useState<EmployeeRequest[]>([]);
  const [clients, setClients] = useState<Client[]>([]);

  // جلب جميع البيانات عند التحميل
  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    // جلب جميع البيانات مرة واحدة
    const [salesData, requestsData, clientsData] = await Promise.all([
      fetch('/api/sales').then(r => r.json()),
      fetch('/api/requests').then(r => r.json()),
      fetch('/api/clients').then(r => r.json())
    ]);

    setSales(salesData);
    setRequests(requestsData);
    setClients(clientsData);
  };
};
```

### **الهيكل الجديد المقترح:**
```typescript
// context/store.tsx - الهيكل الجديد
export const StoreProvider = ({ children }: { children: React.ReactNode }) => {
  // بيانات أساسية فقط (للقوائم المنسدلة وما شابه)
  const [clients, setClients] = useState<Client[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);

  // نظام التخزين المؤقت
  const [dataCache, setDataCache] = useState<{
    [key: string]: {
      data: any[];
      timestamp: number;
      ttl: number;
      total?: number;
      hasMore?: boolean;
    }
  }>({});

  // جلب البيانات الأساسية فقط عند التحميل
  useEffect(() => {
    loadEssentialData();
  }, []);

  const loadEssentialData = async () => {
    // جلب البيانات الأساسية فقط (للقوائم المنسدلة)
    const [clientsData, employeesData, warehousesData] = await Promise.all([
      fetch('/api/clients/list').then(r => r.json()), // endpoint مبسط
      fetch('/api/employees/list').then(r => r.json()),
      fetch('/api/warehouses/list').then(r => r.json())
    ]);

    setClients(clientsData);
    setEmployees(employeesData);
    setWarehouses(warehousesData);
  };

  // دالة عامة لجلب البيانات عند الطلب مع Cache
  const fetchDataOnDemand = async (
    endpoint: string,
    params: {
      page?: number;
      limit?: number;
      filters?: any;
      search?: any;
      sort?: any;
    } = {},
    options: {
      useCache?: boolean;
      cacheTtl?: number;
    } = {}
  ) => {
    const { useCache = true, cacheTtl = 2 * 60 * 1000 } = options;
    const cacheKey = `${endpoint}_${JSON.stringify(params)}`;

    // فحص Cache
    if (useCache && dataCache[cacheKey]) {
      const cached = dataCache[cacheKey];
      if (Date.now() - cached.timestamp < cached.ttl) {
        return {
          data: cached.data,
          total: cached.total || 0,
          hasMore: cached.hasMore || false
        };
      }
    }

    // جلب البيانات من API
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, typeof value === 'object' ? JSON.stringify(value) : String(value));
      }
    });

    const response = await fetch(`${endpoint}?${queryParams}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // حفظ في Cache
    if (useCache) {
      setDataCache(prev => ({
        ...prev,
        [cacheKey]: {
          data: result.data || [],
          total: result.total || 0,
          hasMore: result.hasMore || false,
          timestamp: Date.now(),
          ttl: cacheTtl
        }
      }));
    }

    return result;
  };

  // دالة تنظيف Cache المنتهي الصلاحية
  const clearExpiredCache = useCallback(() => {
    const now = Date.now();
    setDataCache(prev => {
      const newCache = { ...prev };
      Object.keys(newCache).forEach(key => {
        if (now - newCache[key].timestamp > newCache[key].ttl) {
          delete newCache[key];
        }
      });
      return newCache;
    });
  }, []);

  // تنظيف Cache كل 5 دقائق
  useEffect(() => {
    const interval = setInterval(clearExpiredCache, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [clearExpiredCache]);

  // دالة إعادة تعيين Cache لنوع معين من البيانات
  const invalidateCache = (pattern: string) => {
    setDataCache(prev => {
      const newCache = { ...prev };
      Object.keys(newCache).forEach(key => {
        if (key.includes(pattern)) {
          delete newCache[key];
        }
      });
      return newCache;
    });
  };

  const value = {
    // البيانات الأساسية
    clients,
    employees,
    warehouses,

    // دوال جلب البيانات
    fetchDataOnDemand,
    invalidateCache,
    clearExpiredCache,

    // معلومات Cache
    cacheInfo: Object.keys(dataCache).reduce((acc, key) => {
      acc[key] = {
        size: dataCache[key].data.length,
        age: Date.now() - dataCache[key].timestamp,
        ttl: dataCache[key].ttl
      };
      return acc;
    }, {} as any)
  };

  return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  );
};
```

---

## 📊 مقارنة الأداء

### **قبل التحديث:**
- **وقت التحميل الأولي:** 3-5 ثواني
- **استهلاك الذاكرة:** عالي (جميع البيانات محملة)
- **استجابة الواجهة:** بطيئة مع البيانات الكثيرة
- **حجم البيانات المنقولة:** كبير (جميع السجلات)

### **بعد التحديث:**
- **وقت التحميل الأولي:** 0.5-1 ثانية
- **استهلاك الذاكرة:** منخفض (15 سجل فقط)
- **استجابة الواجهة:** سريعة ومتجاوبة
- **حجم البيانات المنقولة:** صغير (حسب الحاجة)

---

## 🎯 خارطة الطريق للتطبيق

### **الأسبوع الأول:**
- [ ] تطبيق النظام على صفحة الطلبات
- [ ] إنشاء API endpoints للطلبات
- [ ] تحديث واجهة صفحة الطلبات

### **الأسبوع الثاني:**
- [ ] تطبيق النظام على صفحة المخزون
- [ ] تحديث context/store.tsx
- [ ] اختبار الأداء والاستقرار

### **الأسبوع الثالث:**
- [ ] تطبيق النظام على صفحة العملاء
- [ ] تطبيق النظام على صفحة الموظفين
- [ ] تحسينات إضافية

### **الأسبوع الرابع:**
- [ ] اختبار شامل لجميع الأقسام
- [ ] تحسين الأداء والتخزين المؤقت
- [ ] توثيق النظام الجديد

---

---

## 🔧 تفاصيل تقنية متقدمة

### **نمط التصميم المستخدم:**
- **Repository Pattern:** فصل منطق البيانات عن العرض
- **Observer Pattern:** مراقبة تغييرات البيانات
- **Lazy Loading:** تحميل البيانات عند الحاجة
- **Caching Strategy:** تخزين مؤقت ذكي مع TTL

### **هيكل API Endpoints الموحد:**
```typescript
// نمط موحد لجميع endpoints
interface ApiResponse<T> {
  data: T[];
  total: number;
  hasMore: boolean;
  page: number;
  limit: number;
  filters?: any;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}

interface ApiQueryParams {
  page?: number;
  limit?: number;
  search?: {
    query: string;
    fields: string[];
  };
  filters?: {
    [key: string]: any;
  };
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}
```

### **نمط إدارة الحالة الموحد:**
```typescript
// نمط موحد لجميع الصفحات
interface PageState<T> {
  // البيانات الرئيسية
  data: T[];
  total: number;
  hasMore: boolean;
  page: number;
  isLoading: boolean;

  // النافذة المنبثقة
  isDialogOpen: boolean;
  previousData: T[];
  dialogPage: number;
  dialogLoading: boolean;
  hasMoreDialog: boolean;

  // البحث والفلترة
  searchTerm: string;
  filters: {
    [key: string]: any;
  };
}

// Hook مخصص لإدارة الحالة
const usePageData = <T>(endpoint: string) => {
  const [state, setState] = useState<PageState<T>>({
    data: [],
    total: 0,
    hasMore: true,
    page: 1,
    isLoading: false,
    isDialogOpen: false,
    previousData: [],
    dialogPage: 1,
    dialogLoading: false,
    hasMoreDialog: true,
    searchTerm: '',
    filters: {}
  });

  const loadData = useCallback(async (
    page: number = 1,
    append: boolean = false,
    customFilters = state.filters
  ) => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await fetchDataOnDemand(endpoint, {
        page,
        limit: 15,
        filters: customFilters,
        search: state.searchTerm ? {
          query: state.searchTerm,
          fields: ['name', 'number', 'description'] // حسب نوع البيانات
        } : undefined
      });

      setState(prev => ({
        ...prev,
        data: append ? [...prev.data, ...response.data] : response.data,
        total: response.total,
        hasMore: response.hasMore,
        page,
        isLoading: false
      }));
    } catch (error) {
      console.error(`خطأ في جلب البيانات من ${endpoint}:`, error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [endpoint, state.filters, state.searchTerm]);

  return { state, setState, loadData };
};
```

### **مكونات واجهة المستخدم القابلة لإعادة الاستخدام:**

#### **1. مكون النافذة المنبثقة العام:**
```typescript
// components/ui/data-dialog.tsx
interface DataDialogProps<T> {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  data: T[];
  loading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  onItemSelect: (item: T) => void;
  columns: {
    key: keyof T;
    label: string;
    render?: (value: any, item: T) => React.ReactNode;
  }[];
  searchComponent?: React.ReactNode;
  filterComponent?: React.ReactNode;
}

export function DataDialog<T>({
  isOpen,
  onOpenChange,
  title,
  data,
  loading,
  hasMore,
  onLoadMore,
  onItemSelect,
  columns,
  searchComponent,
  filterComponent
}: DataDialogProps<T>) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        {/* خيارات البحث والفلترة */}
        {(searchComponent || filterComponent) && (
          <Card>
            <CardContent className="p-4">
              {searchComponent}
              {filterComponent}
            </CardContent>
          </Card>
        )}

        {/* جدول البيانات */}
        <div className="border rounded-lg overflow-hidden max-h-96 overflow-y-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={String(column.key)}>{column.label}</TableHead>
                ))}
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading && data.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={columns.length + 1} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="mr-2">جاري التحميل...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : data.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={columns.length + 1} className="text-center py-8 text-gray-500">
                    لا توجد بيانات
                  </TableCell>
                </TableRow>
              ) : (
                data.map((item, index) => (
                  <TableRow key={index}>
                    {columns.map((column) => (
                      <TableCell key={String(column.key)}>
                        {column.render
                          ? column.render(item[column.key], item)
                          : String(item[column.key] || '')
                        }
                      </TableCell>
                    ))}
                    <TableCell>
                      <Button
                        onClick={() => onItemSelect(item)}
                        size="sm"
                        variant="outline"
                      >
                        تحميل
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* زر تحميل المزيد */}
        {hasMore && (
          <div className="flex justify-center">
            <Button
              onClick={onLoadMore}
              disabled={loading}
              variant="outline"
              className="px-6"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  جاري التحميل...
                </>
              ) : (
                <>
                  <Plus className="ml-2 h-4 w-4" />
                  تحميل المزيد (15 عنصر)
                </>
              )}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
```

#### **2. مكون البحث والفلترة العام:**
```typescript
// components/ui/search-filter.tsx
interface SearchFilterProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filters: { [key: string]: any };
  onFiltersChange: (filters: { [key: string]: any }) => void;
  filterOptions: {
    key: string;
    label: string;
    type: 'select' | 'date' | 'dateRange';
    options?: { value: string; label: string }[];
  }[];
  onSearch: () => void;
  onReset: () => void;
}

export function SearchFilter({
  searchTerm,
  onSearchChange,
  filters,
  onFiltersChange,
  filterOptions,
  onSearch,
  onReset
}: SearchFilterProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* البحث */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">البحث</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="البحث..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* الفلاتر الديناميكية */}
        {filterOptions.map((option) => (
          <div key={option.key} className="space-y-2">
            <Label className="text-sm font-medium">{option.label}</Label>
            {option.type === 'select' && (
              <Select
                value={filters[option.key] || 'all'}
                onValueChange={(value) =>
                  onFiltersChange({ ...filters, [option.key]: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder={`جميع ${option.label}`} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع {option.label}</SelectItem>
                  {option.options?.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            {option.type === 'date' && (
              <Input
                type="date"
                value={filters[option.key] || ''}
                onChange={(e) =>
                  onFiltersChange({ ...filters, [option.key]: e.target.value })
                }
              />
            )}
          </div>
        ))}
      </div>

      {/* أزرار التحكم */}
      <div className="flex gap-2">
        <Button onClick={onSearch} className="bg-blue-600 hover:bg-blue-700 text-white">
          <Search className="ml-2 h-4 w-4" />
          بحث
        </Button>
        <Button onClick={onReset} variant="outline">
          <X className="ml-2 h-4 w-4" />
          إعادة تعيين
        </Button>
      </div>
    </div>
  );
}
```

---

## 📋 قائمة مراجعة التطبيق (Checklist)

### **لكل قسم جديد:**

#### **المرحلة الأولى: التحضير**
- [ ] تحليل الهيكل الحالي للقسم
- [ ] تحديد نقاط الاعتماد على Store القديم
- [ ] تحديد البيانات المطلوبة للقوائم المنسدلة
- [ ] تصميم هيكل API الجديد

#### **المرحلة الثانية: Backend**
- [ ] إنشاء API endpoint للبيانات الأساسية
- [ ] إضافة دعم الترقيم (Pagination)
- [ ] إضافة دعم البحث والفلترة
- [ ] إضافة دعم الترتيب (Sorting)
- [ ] اختبار API endpoints

#### **المرحلة الثالثة: Frontend**
- [ ] إضافة state management للنافذة المنبثقة
- [ ] إنشاء دالة جلب البيانات المنفصلة
- [ ] تحديث واجهة المستخدم الرئيسية
- [ ] إنشاء النافذة المنبثقة
- [ ] إضافة مكونات البحث والفلترة

#### **المرحلة الرابعة: التحسين**
- [ ] تحسين الأداء
- [ ] إضافة معالجة الأخطاء
- [ ] إضافة Loading states
- [ ] تحسين تجربة المستخدم
- [ ] اختبار شامل

#### **المرحلة الخامسة: التوثيق**
- [ ] توثيق API endpoints الجديدة
- [ ] توثيق التغييرات في الكود
- [ ] تحديث دليل المستخدم
- [ ] إنشاء أمثلة للاستخدام

---

*تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-SA')}*
*آخر تحديث: ${new Date().toLocaleString('ar-SA')}*
