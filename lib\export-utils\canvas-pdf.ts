"use client";

import { jsPDF } from "jspdf";

// وظيفة لإنشاء PDF باستخدام Canvas لرسم النصوص العربية
export async function createArabicPDFWithCanvas(
  deviceInfo: any,
  timelineEvents: any[],
  fileName: string,
  isCustomerView: boolean = false,
  action: 'print' | 'download' = 'download',
  language: 'ar' | 'en' | 'both' = 'both'
): Promise<void> {
  try {
    // جلب الإعدادات أولاً
    let settings: any = {};
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        settings = await response.json();
      }
    } catch (error) {
      console.warn('فشل في جلب الإعدادات، سيتم استخدام القيم الافتراضية');
      settings = {
        logoUrl: '',
        companyNameAr: 'DeviceFlow',
        companyNameEn: 'DeviceFlow',
        addressAr: 'الشارع الرئيسي، المدينة، الدولة',
        addressEn: 'Main Street, City, Country',
        phone: '+************',
        email: '<EMAIL>',
        website: 'www.deviceflow.com',
        footerTextAr: 'شكرًا لتعاملكم معنا.',
        footerTextEn: 'Thank you for your business.'
      };
    }

    // إنشاء PDF
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // إنشاء Canvas مؤقت لرسم النصوص
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('فشل في إنشاء Canvas context');

    // إعداد Canvas
    canvas.width = 794; // عرض A4 بالبكسل
    canvas.height = 1123; // ارتفاع A4 بالبكسل
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    let currentY = 40;

    // رسم الترويسة
    currentY = await drawHeader(ctx, canvas, settings, currentY, language);

    // رسم عنوان التقرير
    const title = isCustomerView
      ? (language === 'en' ? 'Device Tracking Report (Customer Copy)' : 'تقرير تتبع الجهاز (نسخة العميل)')
      : (language === 'en' ? 'Device History Log' : 'سجل تاريخ الجهاز');

    ctx.font = 'bold 24px Arial, sans-serif';
    ctx.fillStyle = '#2c3e50';
    ctx.textAlign = 'center';
    ctx.fillText(title, canvas.width / 2, currentY);
    currentY += 40;

    // رسم معلومات الجهاز
    ctx.font = '16px Arial, sans-serif';
    ctx.fillStyle = '#7f8c8d';
    ctx.fillText(`${deviceInfo.model} - ${deviceInfo.id}`, canvas.width / 2, currentY);
    currentY += 60;
    
    // رسم معلومات الجهاز
    ctx.font = '18px Arial, sans-serif';
    ctx.fillStyle = '#34495e';
    ctx.textAlign = 'right';
    
    // رسم مربع معلومات الجهاز
    ctx.strokeStyle = '#bdc3c7';
    ctx.lineWidth = 2;
    ctx.strokeRect(50, currentY - 20, canvas.width - 100, 120);
    
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 20px Arial, sans-serif';
    ctx.fillText('معلومات الجهاز', canvas.width - 70, currentY);
    currentY += 40;
    
    ctx.font = '16px Arial, sans-serif';
    ctx.fillText(`الموديل: ${deviceInfo.model || '-'}`, canvas.width - 70, currentY);
    currentY += 30;
    ctx.fillText(`الرقم التسلسلي: ${deviceInfo.id || '-'}`, canvas.width - 70, currentY);
    currentY += 30;
    ctx.fillText(`الحالة: ${deviceInfo.status || '-'}`, canvas.width - 70, currentY);
    currentY += 60;
    
    // إضافة معلومات البيع للعميل
    if (isCustomerView && deviceInfo.lastSale) {
      // رسم مربع معلومات البيع
      ctx.strokeStyle = '#27ae60';
      ctx.strokeRect(50, currentY - 20, canvas.width - 100, 160);
      
      ctx.fillStyle = '#27ae60';
      ctx.font = 'bold 20px Arial, sans-serif';
      ctx.fillText('تفاصيل البيع', canvas.width - 70, currentY);
      currentY += 40;
      
      ctx.fillStyle = '#2c3e50';
      ctx.font = '16px Arial, sans-serif';
      ctx.fillText(`العميل: ${deviceInfo.lastSale.clientName || '-'}`, canvas.width - 70, currentY);
      currentY += 30;
      ctx.fillText(`فاتورة البيع: ${deviceInfo.lastSale.soNumber || '-'}`, canvas.width - 70, currentY);
      currentY += 30;
      ctx.fillText(`الفاتورة الرسمية: ${deviceInfo.lastSale.opNumber || 'لا يوجد'}`, canvas.width - 70, currentY);
      currentY += 30;
      
      const saleDate = deviceInfo.lastSale.date ? new Date(deviceInfo.lastSale.date).toLocaleDateString('ar-EG') : '-';
      ctx.fillText(`تاريخ البيع: ${saleDate}`, canvas.width - 70, currentY);
      currentY += 60;
      
      // معلومات الضمان
      if (deviceInfo.warrantyInfo) {
        ctx.strokeStyle = '#f39c12';
        ctx.strokeRect(50, currentY - 20, canvas.width - 100, 120);
        
        ctx.fillStyle = '#f39c12';
        ctx.font = 'bold 20px Arial, sans-serif';
        ctx.fillText('حالة الضمان', canvas.width - 70, currentY);
        currentY += 40;
        
        ctx.fillStyle = '#2c3e50';
        ctx.font = '16px Arial, sans-serif';
        ctx.fillText(`الحالة: ${deviceInfo.warrantyInfo.status || '-'}`, canvas.width - 70, currentY);
        currentY += 30;
        ctx.fillText(`تاريخ الانتهاء: ${deviceInfo.warrantyInfo.expiryDate || '-'}`, canvas.width - 70, currentY);
        currentY += 30;
        ctx.fillText(`الوقت المتبقي: ${deviceInfo.warrantyInfo.remaining || '-'}`, canvas.width - 70, currentY);
        currentY += 60;
      }
    }
    
    // إضافة سجل الأحداث
    if (timelineEvents && timelineEvents.length > 0) {
      // التحقق من المساحة المتبقية
      if (currentY > canvas.height - 200) {
        // إضافة الصفحة الحالية
        const imgData = canvas.toDataURL('image/png');
        pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);
        pdf.addPage();
        
        // إعادة تهيئة Canvas للصفحة الجديدة
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        currentY = 60;
      }
      
      ctx.fillStyle = '#3498db';
      ctx.font = 'bold 20px Arial, sans-serif';
      ctx.fillText('سجل الأحداث', canvas.width - 70, currentY);
      currentY += 50;
      
      // رسم جدول الأحداث
      const tableStartY = currentY;
      const rowHeight = 40;
      const colWidths = [150, 200, 300, 144]; // عرض الأعمدة
      const colPositions = [canvas.width - 70, canvas.width - 220, canvas.width - 420, canvas.width - 720];
      
      // رسم رؤوس الجدول
      ctx.fillStyle = '#34495e';
      ctx.fillRect(50, currentY - 15, canvas.width - 100, rowHeight);
      
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial, sans-serif';
      ctx.fillText('التاريخ', colPositions[0], currentY + 10);
      ctx.fillText('الحدث', colPositions[1], currentY + 10);
      ctx.fillText('الوصف', colPositions[2], currentY + 10);
      ctx.fillText('الموظف', colPositions[3], currentY + 10);
      currentY += rowHeight;
      
      // رسم صفوف البيانات
      timelineEvents.forEach((event, index) => {
        const bgColor = index % 2 === 0 ? '#f8f9fa' : '#ffffff';
        ctx.fillStyle = bgColor;
        ctx.fillRect(50, currentY - 15, canvas.width - 100, rowHeight);
        
        // رسم حدود الخلايا
        ctx.strokeStyle = '#dee2e6';
        ctx.lineWidth = 1;
        ctx.strokeRect(50, currentY - 15, canvas.width - 100, rowHeight);
        
        ctx.fillStyle = '#2c3e50';
        ctx.font = '14px Arial, sans-serif';
        
        const eventDate = event.formattedDate || new Date(event.date).toLocaleDateString('ar-EG');
        ctx.fillText(eventDate, colPositions[0], currentY + 10);
        ctx.fillText(event.title || '-', colPositions[1], currentY + 10);
        
        // تقسيم النص الطويل
        const description = event.description || '-';
        if (description.length > 30) {
          ctx.fillText(description.substring(0, 30) + '...', colPositions[2], currentY + 10);
        } else {
          ctx.fillText(description, colPositions[2], currentY + 10);
        }
        
        ctx.fillText(event.user || '-', colPositions[3], currentY + 10);
        currentY += rowHeight;
        
        // التحقق من الحاجة لصفحة جديدة
        if (currentY > canvas.height - 100 && index < timelineEvents.length - 1) {
          const imgData = canvas.toDataURL('image/png');
          pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);
          pdf.addPage();
          
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          currentY = 60;
        }
      });
    }
    
    // رسم التذييل
    drawFooter(ctx, canvas, settings, language);

    // إضافة الصفحة الأخيرة
    const finalImgData = canvas.toDataURL('image/png');
    pdf.addImage(finalImgData, 'PNG', 0, 0, pageWidth, pageHeight);
    
    // حفظ أو طباعة الملف
    if (action === 'print') {
      pdf.output('dataurlnewwindow');
    } else {
      pdf.save(`${fileName}.pdf`);
    }

    // تنظيف
    canvas.remove();

    // عرض رسالة نجاح
    showNotification('تم إنشاء التقرير بنجاح باستخدام Canvas!', 'success');
    
  } catch (error) {
    console.error('Error creating PDF with Canvas:', error);
    showNotification('حدث خطأ أثناء التصدير: ' + (error as Error).message, 'error');
  }
}

// وظيفة مساعدة لعرض الإشعارات (نسخة من الملف الآخر)
function showNotification(message: string, type: 'success' | 'error' = 'success') {
  const notification = document.createElement("div");
  notification.textContent = message;
  notification.style.position = "fixed";
  notification.style.top = "20px";
  notification.style.right = "20px";
  notification.style.padding = "12px 20px";
  notification.style.background = type === 'success' ? "#10B981" : "#EF4444";
  notification.style.color = "white";
  notification.style.borderRadius = "8px";
  notification.style.zIndex = "9999";
  notification.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
  notification.style.fontSize = "14px";
  notification.style.fontWeight = "500";
  notification.setAttribute('data-notification-id', 'canvas-pdf-notification');
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  }, 3000);
}

// دالة رسم الترويسة مع الإعدادات الجديدة
async function drawHeader(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  settings: any,
  startY: number,
  language: string
): Promise<number> {
  let currentY = startY;

  // رسم خط فاصل علوي
  ctx.strokeStyle = '#3498db';
  ctx.lineWidth = 3;
  ctx.beginPath();
  ctx.moveTo(50, currentY);
  ctx.lineTo(canvas.width - 50, currentY);
  ctx.stroke();
  currentY += 20;

  // رسم أسماء الشركة
  if (language === 'ar' || language === 'both') {
    ctx.font = 'bold 20px Arial, sans-serif';
    ctx.fillStyle = '#2c3e50';
    ctx.textAlign = 'center';
    ctx.fillText(settings.companyNameAr || 'DeviceFlow', canvas.width / 2, currentY);
    currentY += 25;
  }

  if (language === 'en' || language === 'both') {
    ctx.font = language === 'both' ? '16px Arial, sans-serif' : 'bold 20px Arial, sans-serif';
    ctx.fillStyle = language === 'both' ? '#7f8c8d' : '#2c3e50';
    ctx.textAlign = 'center';
    ctx.fillText(settings.companyNameEn || 'DeviceFlow', canvas.width / 2, currentY);
    currentY += language === 'both' ? 20 : 25;
  }

  // رسم العناوين
  ctx.font = '14px Arial, sans-serif';
  ctx.fillStyle = '#7f8c8d';
  ctx.textAlign = 'center';

  if (language === 'ar' || language === 'both') {
    ctx.fillText(settings.addressAr || 'الشارع الرئيسي، المدينة، الدولة', canvas.width / 2, currentY);
    currentY += 18;
  }

  if (language === 'en' || language === 'both') {
    ctx.fillText(settings.addressEn || 'Main Street, City, Country', canvas.width / 2, currentY);
    currentY += 18;
  }

  // رسم معلومات الاتصال
  const contactInfo = [];
  if (settings.phone) contactInfo.push(`📞 ${settings.phone}`);
  if (settings.email) contactInfo.push(`✉️ ${settings.email}`);
  if (settings.website) contactInfo.push(`🌐 ${settings.website}`);

  if (contactInfo.length > 0) {
    ctx.fillText(contactInfo.join(' | '), canvas.width / 2, currentY);
    currentY += 20;
  }

  // رسم خط فاصل سفلي
  ctx.strokeStyle = '#bdc3c7';
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.moveTo(50, currentY);
  ctx.lineTo(canvas.width - 50, currentY);
  ctx.stroke();
  currentY += 30;

  return currentY;
}

// دالة رسم التذييل
function drawFooter(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  settings: any,
  language: string
): void {
  const footerY = canvas.height - 80;

  // رسم خط فاصل
  ctx.strokeStyle = '#bdc3c7';
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.moveTo(50, footerY);
  ctx.lineTo(canvas.width - 50, footerY);
  ctx.stroke();

  // رسم نصوص التذييل
  ctx.font = '12px Arial, sans-serif';
  ctx.fillStyle = '#7f8c8d';
  ctx.textAlign = 'center';

  let currentY = footerY + 20;

  if (language === 'ar' || language === 'both') {
    if (settings.footerTextAr) {
      ctx.fillText(settings.footerTextAr, canvas.width / 2, currentY);
      currentY += 15;
    }
  }

  if (language === 'en' || language === 'both') {
    if (settings.footerTextEn) {
      ctx.fillText(settings.footerTextEn, canvas.width / 2, currentY);
      currentY += 15;
    }
  }

  // رسم الطابع الزمني
  const currentDate = new Date();
  const arabicDate = currentDate.toLocaleDateString('ar-EG');
  const englishDate = currentDate.toLocaleDateString('en-US');
  const time = currentDate.toLocaleTimeString('ar-EG', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  ctx.font = '10px Arial, sans-serif';
  ctx.fillStyle = '#95a5a6';

  if (language === 'ar' || language === 'both') {
    const timestampAr = `تاريخ الطباعة: ${arabicDate} - ${time}`;
    ctx.textAlign = language === 'both' ? 'right' : 'center';
    const xPos = language === 'both' ? canvas.width - 60 : canvas.width / 2;
    ctx.fillText(timestampAr, xPos, currentY);
  }

  if (language === 'en' || language === 'both') {
    const timestampEn = `Print Date: ${englishDate} - ${time}`;
    ctx.textAlign = language === 'both' ? 'left' : 'center';
    const xPos = language === 'both' ? 60 : canvas.width / 2;
    ctx.fillText(timestampEn, xPos, currentY);
  }
}
