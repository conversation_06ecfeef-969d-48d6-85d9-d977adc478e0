// اختبار سريع بعد حذف APIs المعقدة وإضافة initialization flag
console.log('🧹 تنظيف النظام من APIs المعقدة...');
console.log('');
console.log('✅ تم حذف:');
console.log('   - /api/clients (المعقد مع auth)');
console.log('   - /api/suppliers (المعقد مع auth)');
console.log('   - /api/warehouses (المعقد مع auth)');
console.log('');
console.log('✅ باقي:');
console.log('   - /api/clients-simple (بسيط بدون auth)');
console.log('   - /api/suppliers-simple (بسيط بدون auth)');
console.log('   - /api/warehouses-simple (بسيط بدون auth)');
console.log('');
console.log('🔧 تحسينات إضافية:');
console.log('   - إضافة isInitialized flag لمنع التحميل المتكرر');
console.log('   - loadEssentialData يتحقق من الflag قبل التحميل');
console.log('   - useEffect dependency على isInitialized');
console.log('');
console.log('🎯 هذا يجب أن يحل مشكلة "Maximum update depth exceeded" نهائياً');
console.log('');
console.log('🧪 اختبار APIs البسيطة...');

async function testSimpleAPIsOnly() {
  try {
    // اختبار أن APIs البسيطة تعمل
    const clientsResponse = await fetch('http://localhost:9005/api/clients-simple?limit=1');
    if (clientsResponse.ok) {
      console.log('✅ clients-simple يعمل');
    } else {
      console.log('❌ مشكلة في clients-simple');
    }

    const suppliersResponse = await fetch('http://localhost:9005/api/suppliers-simple?limit=1');
    if (suppliersResponse.ok) {
      console.log('✅ suppliers-simple يعمل');
    } else {
      console.log('❌ مشكلة في suppliers-simple');
    }

    const warehousesResponse = await fetch('http://localhost:9005/api/warehouses-simple?limit=1');
    if (warehousesResponse.ok) {
      console.log('✅ warehouses-simple يعمل');
    } else {
      console.log('❌ مشكلة في warehouses-simple');
    }

    console.log('');
    console.log('🌐 جرب الآن: http://localhost:9005/clients');
    console.log('   يجب أن تفتح الصفحة بدون مشاكل');
    
  } catch (error) {
    console.log('❌ تأكد من تشغيل التطبيق');
  }
}

testSimpleAPIsOnly();
