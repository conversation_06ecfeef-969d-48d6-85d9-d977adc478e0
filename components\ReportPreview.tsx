'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { 
  Printer, 
  FileDown, 
  Eye, 
  Settings,
  Languages,
  User,
  FileText
} from 'lucide-react';
import DeviceTrackingReport from './DeviceTrackingReport';
import { printDeviceTrackingReport } from '@/lib/device-tracking-utils';

interface ReportPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  deviceData: {
    model: string;
    id: string;
    status: string;
    lastSale?: {
      clientName: string;
      soNumber: string;
      opNumber: string;
      date: string;
    };
    warrantyInfo?: {
      status: string;
      expiryDate: string;
      remaining: string;
    };
    originalItemInfo?: {
      model: string;
      [key: string]: any;
    };
  };
  timelineEvents: Array<{
    id: string;
    type: string;
    title: string;
    description: string;
    date: string;
    user?: string;
    details?: any;
    status?: string;
  }>;
}

export default function ReportPreview({
  isOpen,
  onClose,
  deviceData,
  timelineEvents
}: ReportPreviewProps) {
  const [language, setLanguage] = useState<'ar' | 'en' | 'both'>('both');
  const [isCustomerView, setIsCustomerView] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handlePrint = async () => {
    setIsProcessing(true);
    try {
      await printDeviceTrackingReport(deviceData, timelineEvents, {
        language,
        isCustomerView,
        action: 'print'
      });
    } catch (error) {
      console.error('Error printing report:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = async () => {
    setIsProcessing(true);
    try {
      await printDeviceTrackingReport(deviceData, timelineEvents, {
        language,
        isCustomerView,
        action: 'download',
        filename: `${isCustomerView ? 'customer_' : ''}device_report_${deviceData.id}.pdf`
      });
    } catch (error) {
      console.error('Error downloading report:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const reportTitle = isCustomerView 
    ? (language === 'en' ? 'Device Tracking Report (Customer Copy)' : 'تقرير تتبع الجهاز (نسخة العميل)')
    : (language === 'en' ? 'Device History Log' : 'سجل تاريخ الجهاز');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            معاينة التقرير
          </DialogTitle>
        </DialogHeader>

        <div className="flex gap-6 h-[70vh]">
          {/* لوحة الإعدادات */}
          <div className="w-80 flex-shrink-0 space-y-6 overflow-y-auto">
            {/* إعدادات اللغة */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2 text-sm font-semibold">
                <Languages className="h-4 w-4" />
                لغة التقرير
              </Label>
              <RadioGroup value={language} onValueChange={(value: any) => setLanguage(value)}>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <RadioGroupItem value="both" id="both" />
                  <Label htmlFor="both" className="text-sm">ثنائي اللغة (عربي + إنجليزي)</Label>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <RadioGroupItem value="ar" id="ar" />
                  <Label htmlFor="ar" className="text-sm">العربية فقط</Label>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <RadioGroupItem value="en" id="en" />
                  <Label htmlFor="en" className="text-sm">English Only</Label>
                </div>
              </RadioGroup>
            </div>

            <Separator />

            {/* نوع التقرير */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2 text-sm font-semibold">
                <User className="h-4 w-4" />
                نوع التقرير
              </Label>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Checkbox 
                  id="customer-view" 
                  checked={isCustomerView}
                  onCheckedChange={(checked) => setIsCustomerView(checked as boolean)}
                />
                <Label htmlFor="customer-view" className="text-sm">
                  نسخة العميل (معلومات محدودة)
                </Label>
              </div>
              <p className="text-xs text-gray-500">
                {isCustomerView 
                  ? 'سيتم إخفاء المعلومات الحساسة وإظهار معلومات البيع والضمان فقط'
                  : 'سيتم إظهار جميع المعلومات والسجل الكامل للجهاز'
                }
              </p>
            </div>

            <Separator />

            {/* معاينة التقرير */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2 text-sm font-semibold">
                <Eye className="h-4 w-4" />
                المعاينة
              </Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
                className="w-full"
              >
                <Eye className="h-4 w-4 ml-2" />
                {showPreview ? 'إخفاء المعاينة' : 'إظهار المعاينة'}
              </Button>
            </div>

            <Separator />

            {/* معلومات التقرير */}
            <div className="space-y-2 text-xs text-gray-600">
              <div><strong>العنوان:</strong> {reportTitle}</div>
              <div><strong>الجهاز:</strong> {deviceData.model} - {deviceData.id}</div>
              <div><strong>عدد الأحداث:</strong> {timelineEvents.length}</div>
              <div><strong>اللغة:</strong> {
                language === 'both' ? 'ثنائي اللغة' : 
                language === 'ar' ? 'العربية' : 'الإنجليزية'
              }</div>
            </div>
          </div>

          {/* منطقة المعاينة */}
          <div className="flex-1 border rounded-lg overflow-hidden">
            {showPreview ? (
              <div className="h-full overflow-y-auto bg-gray-50 p-4">
                <div className="bg-white rounded-lg shadow-sm">
                  <DeviceTrackingReport
                    deviceInfo={deviceData}
                    timelineEvents={timelineEvents}
                    isCustomerView={isCustomerView}
                    language={language}
                    className="scale-75 origin-top-left transform"
                  />
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>انقر على "إظهار المعاينة" لرؤية التقرير</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose}>
            إلغاء
          </Button>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handlePrint}
              disabled={isProcessing}
            >
              <Printer className="h-4 w-4 ml-2" />
              {isProcessing ? 'جاري الطباعة...' : 'طباعة'}
            </Button>
            <Button
              onClick={handleDownload}
              disabled={isProcessing}
            >
              <FileDown className="h-4 w-4 ml-2" />
              {isProcessing ? 'جاري التحميل...' : 'تحميل PDF'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
