/**
 * سكريبت لإصلاح مشكلة المصادقة والصلاحيات بعد تحديث بيانات المستخدم
 * يحل المشكلة التي تحدث عند تغيير اسم المستخدم مدير النظام إلى مدير النظام 2
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(process.cwd(), 'prisma', 'dev.db');

console.log('🔧 بدء إصلاح مشكلة المصادقة والصلاحيات...');

// فتح قاعدة البيانات
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ خطأ في فتح قاعدة البيانات:', err.message);
    return;
  }
  console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
});

// التحقق من وجود المستخدمين الإداريين
db.serialize(() => {
  console.log('\n📋 فحص المستخدمين الإداريين الحاليين...');
  
  db.all("SELECT id, name, username, email, role, status FROM User WHERE role = 'admin' OR username = 'admin' OR name LIKE '%مدير%'", (err, rows) => {
    if (err) {
      console.error('❌ خطأ في استعلام المستخدمين:', err.message);
      return;
    }

    console.log(`\n📊 تم العثور على ${rows.length} مستخدم إداري:`);
    rows.forEach((row, index) => {
      console.log(`${index + 1}. ID: ${row.id}, الاسم: "${row.name}", اسم المستخدم: "${row.username}", الإيميل: "${row.email}", الصلاحية: "${row.role}", الحالة: "${row.status}"`);
    });

    if (rows.length === 0) {
      console.log('\n⚠️  لم يتم العثور على أي مستخدم إداري. سيتم إنشاء مستخدم إداري افتراضي...');
      createDefaultAdmin();
    } else {
      console.log('\n🔍 فحص المستخدمين لإصلاح أي مشاكل...');
      fixUsersIfNeeded(rows);
    }
  });
});

function createDefaultAdmin() {
  const adminData = {
    username: 'admin',
    email: '<EMAIL>',
    name: 'مدير النظام',
    role: 'admin',
    status: 'Active'
  };

  const insertQuery = `
    INSERT OR REPLACE INTO User (username, email, name, role, status, createdAt, updatedAt)
    VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
  `;

  db.run(insertQuery, [adminData.username, adminData.email, adminData.name, adminData.role, adminData.status], function(err) {
    if (err) {
      console.error('❌ خطأ في إنشاء المستخدم الإداري:', err.message);
    } else {
      console.log('✅ تم إنشاء مستخدم إداري افتراضي بنجاح (ID:', this.lastID, ')');
    }
    closeDatabase();
  });
}

function fixUsersIfNeeded(users) {
  let fixesNeeded = 0;
  let fixesCompleted = 0;

  users.forEach(user => {
    let needsUpdate = false;
    const updates = [];
    const values = [];

    // التأكد من أن المستخدم الإداري له username صحيح
    if (user.role === 'admin' && (!user.username || user.username === '')) {
      updates.push('username = ?');
      values.push('admin');
      needsUpdate = true;
      console.log(`🔧 سيتم تحديث username للمستخدم "${user.name}" (ID: ${user.id})`);
    }

    // التأكد من أن المستخدم الإداري له email صحيح
    if (user.role === 'admin' && (!user.email || user.email === '')) {
      updates.push('email = ?');
      values.push('<EMAIL>');
      needsUpdate = true;
      console.log(`🔧 سيتم تحديث email للمستخدم "${user.name}" (ID: ${user.id})`);
    }

    // التأكد من أن الحالة نشطة
    if (user.status !== 'Active') {
      updates.push('status = ?');
      values.push('Active');
      needsUpdate = true;
      console.log(`🔧 سيتم تحديث الحالة للمستخدم "${user.name}" (ID: ${user.id})`);
    }

    if (needsUpdate) {
      fixesNeeded++;
      updates.push('updatedAt = datetime(\'now\')');
      values.push(user.id);

      const updateQuery = `UPDATE User SET ${updates.join(', ')} WHERE id = ?`;
      
      db.run(updateQuery, values, function(err) {
        if (err) {
          console.error(`❌ خطأ في تحديث المستخدم "${user.name}":`, err.message);
        } else {
          console.log(`✅ تم تحديث المستخدم "${user.name}" بنجاح`);
        }
        
        fixesCompleted++;
        if (fixesCompleted === fixesNeeded) {
          console.log('\n🎉 تم الانتهاء من جميع الإصلاحات!');
          verifyFixes();
        }
      });
    }
  });

  if (fixesNeeded === 0) {
    console.log('✅ جميع المستخدمين الإداريين في حالة جيدة، لا توجد حاجة لإصلاحات');
    verifyFixes();
  }
}

function verifyFixes() {
  console.log('\n🔍 التحقق من النتائج النهائية...');
  
  db.all("SELECT id, name, username, email, role, status FROM User WHERE role = 'admin' OR username = 'admin' OR name LIKE '%مدير%'", (err, rows) => {
    if (err) {
      console.error('❌ خطأ في التحقق النهائي:', err.message);
    } else {
      console.log(`\n📊 المستخدمون الإداريون بعد الإصلاح (${rows.length}):`);
      rows.forEach((row, index) => {
        console.log(`${index + 1}. ID: ${row.id}, الاسم: "${row.name}", اسم المستخدم: "${row.username}", الإيميل: "${row.email}", الصلاحية: "${row.role}", الحالة: "${row.status}"`);
      });
      
      // إعطاء توصيات
      console.log('\n💡 توصيات:');
      console.log('1. تأكد من أن التطبيق يستخدم بيانات المستخدم المحدثة');
      console.log('2. قم بإعادة تشغيل الخادم للتأكد من تطبيق التغييرات');
      console.log('3. إذا استمرت المشكلة، تحقق من آلية إنشاء التوكن في النظام');
    }
    closeDatabase();
  });
}

function closeDatabase() {
  db.close((err) => {
    if (err) {
      console.error('❌ خطأ في إغلاق قاعدة البيانات:', err.message);
    } else {
      console.log('\n✅ تم إغلاق الاتصال بقاعدة البيانات بنجاح');
      console.log('🎯 انتهى سكريبت الإصلاح');
    }
  });
}

// التعامل مع إشارات الإنهاء
process.on('SIGINT', () => {
  console.log('\n⚠️ تم إيقاف السكريبت بواسطة المستخدم');
  closeDatabase();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️ تم إنهاء السكريبت');
  closeDatabase();
  process.exit(0);
});
