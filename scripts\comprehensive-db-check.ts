import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function comprehensiveDbCheck() {
  try {
    console.log('=== فحص شامل لقاعدة البيانات ===\n');

    // 1. فحص أوامر التوريد
    console.log('1. أوامر التوريد:');
    const supplyOrders = await prisma.supplyOrder.findMany({
      select: { 
        id: true, 
        supplyOrderId: true, 
        status: true,
        warehouseId: true,
        supplierId: true,
        employeeName: true
      }
    });
    console.log(`   - العدد: ${supplyOrders.length}`);
    supplyOrders.forEach(order => {
      console.log(`   - ${order.supplyOrderId} (ID: ${order.id}) - ${order.status}`);
      console.log(`     المخزن: ${order.warehouseId}, المورد: ${order.supplierId}`);
    });

    // 2. فحص الأجهزة
    console.log('\n2. الأجهزة:');
    const devices = await prisma.device.findMany({
      select: {
        id: true,
        model: true,
        status: true,
        warehouseId: true,
        supplierId: true,
        price: true,
        condition: true,
        dateAdded: true
      },
      orderBy: { dateAdded: 'desc' }
    });
    console.log(`   - العدد: ${devices.length}`);
    devices.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device.id} - ${device.model}`);
      console.log(`      الحالة: ${device.status}`);
      console.log(`      المخزن: ${device.warehouseId || 'غير محدد'}`);
      console.log(`      المورد: ${device.supplierId || 'غير محدد'}`);
      console.log(`      السعر: ${device.price}`);
      console.log(`      تاريخ الإضافة: ${device.dateAdded}`);
    });

    // 3. فحص المبيعات
    console.log('\n3. المبيعات:');
    const sales = await prisma.sale.findMany({
      select: {
        id: true,
        soNumber: true,
        clientName: true,
        employeeName: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });
    console.log(`   - العدد: ${sales.length}`);
    sales.forEach((sale, index) => {
      console.log(`   ${index + 1}. ${sale.soNumber} - ${sale.clientName}`);
      console.log(`      الموظف: ${sale.employeeName}`);
    });

    // 4. فحص المرتجعات
    console.log('\n4. المرتجعات:');
    const returns = await prisma.return.findMany({
      select: {
        id: true,
        roNumber: true,
        clientName: true,
        employeeName: true,
        createdAt: true
      }
    });
    console.log(`   - العدد: ${returns.length}`);
    returns.forEach((returnItem, index) => {
      console.log(`   ${index + 1}. ${returnItem.roNumber} - ${returnItem.clientName}`);
      console.log(`      الموظف: ${returnItem.employeeName}`);
    });

    // 5. فحص المخازن
    console.log('\n5. المخازن:');
    const warehouses = await prisma.warehouse.findMany({
      select: { id: true, name: true, location: true }
    });
    console.log(`   - العدد: ${warehouses.length}`);
    warehouses.forEach(warehouse => {
      console.log(`   - ${warehouse.id}: ${warehouse.name} (${warehouse.location})`);
    });

    // 6. فحص الموردين
    console.log('\n6. الموردين:');
    const suppliers = await prisma.supplier.findMany({
      select: { id: true, name: true, phone: true }
    });
    console.log(`   - العدد: ${suppliers.length}`);
    suppliers.forEach(supplier => {
      console.log(`   - ${supplier.id}: ${supplier.name} (${supplier.phone})`);
    });

    // 7. فحص الشركات المصنعة
    console.log('\n7. الشركات المصنعة:');
    const manufacturers = await prisma.manufacturer.findMany({
      select: { id: true, name: true }
    });
    console.log(`   - العدد: ${manufacturers.length}`);
    manufacturers.forEach(manufacturer => {
      console.log(`   - ${manufacturer.id}: ${manufacturer.name}`);
    });

    // 8. فحص موديلات الأجهزة
    console.log('\n8. موديلات الأجهزة:');
    const deviceModels = await prisma.deviceModel.findMany({
      select: { id: true, name: true, manufacturerId: true }
    });
    console.log(`   - العدد: ${deviceModels.length}`);
    deviceModels.forEach(model => {
      console.log(`   - ${model.id}: ${model.name} (شركة: ${model.manufacturerId})`);
    });

    // 9. فحص الزبائن
    console.log('\n9. الزبائن:');
    const clients = await prisma.client.findMany({
      select: { id: true, name: true, phone: true }
    });
    console.log(`   - العدد: ${clients.length}`);
    clients.forEach(client => {
      console.log(`   - ${client.id}: ${client.name} (${client.phone})`);
    });

    // 10. فحص تكامل البيانات
    console.log('\n=== فحص تكامل البيانات ===');
    
    // الأجهزة المتاحة للبيع
    const availableForSale = await prisma.device.count({
      where: { 
        AND: [
          { status: 'متاح للبيع' },
          { warehouseId: { not: null } }
        ]
      }
    });
    console.log(`الأجهزة المتاحة للبيع (مع مخزن): ${availableForSale}`);

    // الأجهزة بدون مخزن
    const noWarehouse = await prisma.device.count({
      where: { warehouseId: null }
    });
    console.log(`الأجهزة بدون مخزن: ${noWarehouse}`);

    // الأجهزة بدون مورد
    const noSupplier = await prisma.device.count({
      where: { supplierId: null }
    });
    console.log(`الأجهزة بدون مورد: ${noSupplier}`);

    // فحص الاتصال بقاعدة البيانات
    const connectionInfo = await prisma.$queryRaw`SELECT current_database(), current_user, version()`;
    console.log('\n=== معلومات الاتصال ===');
    console.log('معلومات قاعدة البيانات:', connectionInfo);

    // فحص الجداول الموجودة
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    console.log('\nالجداول الموجودة:');
    console.log(tables);

  } catch (error) {
    console.error('خطأ في فحص قاعدة البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

comprehensiveDbCheck();
