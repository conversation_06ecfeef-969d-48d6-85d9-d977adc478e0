const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    console.log('Creating admin user...');
    
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: { 
        role: 'admin', 
        username: 'admin',
        status: 'Active'
      },
      create: {
        username: 'admin',
        email: '<EMAIL>',
        name: 'System Administrator',
        role: 'admin',
        status: 'Active'
      }
    });
    
    console.log('Admin user created/updated:', adminUser);
    
    // Create some test devices
    console.log('Creating test devices...');
    
    const testDevices = [
      {
        id: 'DEV001',
        model: 'iPhone 14 Pro',
        status: 'متاح للبيع',
        storage: '128GB',
        price: 1200,
        condition: 'جديد',
        dateAdded: new Date().toISOString()
      },
      {
        id: 'DEV002', 
        model: 'Samsung Galaxy S23',
        status: 'متاح للبيع',
        storage: '256GB',
        price: 1000,
        condition: 'جديد',
        dateAdded: new Date().toISOString()
      },
      {
        id: 'DEV003',
        model: 'iPad Air',
        status: 'متاح للبيع', 
        storage: '64GB',
        price: 600,
        condition: 'مستعمل',
        dateAdded: new Date().toISOString()
      }
    ];
    
    for (const device of testDevices) {
      await prisma.device.upsert({
        where: { id: device.id },
        update: device,
        create: device
      });
    }
    
    console.log('Test devices created successfully');
    
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
