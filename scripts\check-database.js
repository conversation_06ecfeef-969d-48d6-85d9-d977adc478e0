const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('فحص نوع قاعدة البيانات...');
    
    // محاولة جلب معلومات قاعدة البيانات
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ قاعدة البيانات متصلة:', result);
    
    // فحص الجداول الموجودة
    console.log('\nفحص الجداول الموجودة...');
    
    // جلب العملاء
    const clients = await prisma.client.findMany();
    console.log(`📋 العملاء: ${clients.length}`);
    clients.slice(0, 3).forEach((client, i) => {
      console.log(`   ${i+1}. ${client.name} (ID: ${client.id})`);
    });
    
    // جلب الموردين  
    const suppliers = await prisma.supplier.findMany();
    console.log(`📋 الموردين: ${suppliers.length}`);
    suppliers.slice(0, 3).forEach((supplier, i) => {
      console.log(`   ${i+1}. ${supplier.name} (ID: ${supplier.id})`);
    });
    
    // جلب المخازن
    const warehouses = await prisma.warehouse.findMany();
    console.log(`📋 المخازن: ${warehouses.length}`);
    warehouses.forEach((warehouse, i) => {
      console.log(`   ${i+1}. ${warehouse.name} (ID: ${warehouse.id})`);
    });
    
    // فحص متغير البيئة
    console.log('\n🔧 معلومات الاتصال:');
    console.log('DATABASE_URL:', process.env.DATABASE_URL || 'غير محدد');
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    
    // إذا فشل الاتصال، تحقق من وجود ملفات SQLite
    const fs = require('fs');
    const path = require('path');
    
    console.log('\n🔍 فحص ملفات SQLite...');
    const dbFiles = ['data.db', 'dev.db'];
    
    dbFiles.forEach(file => {
      const filePath = path.join(__dirname, file);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        console.log(`   ✅ ${file} موجود (${stats.size} bytes) - آخر تعديل: ${stats.mtime.toLocaleString()}`);
      } else {
        console.log(`   ❌ ${file} غير موجود`);
      }
    });
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
