import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDevicesDatabase() {
  try {
    console.log('=== فحص الأجهزة في قاعدة البيانات ===\n');

    // عدد الأجهزة الإجمالي
    const totalDevices = await prisma.device.count();
    console.log(`إجمالي الأجهزة: ${totalDevices}`);

    // الأجهزة حسب الحالة
    const statusGroups = await prisma.device.groupBy({
      by: ['status'],
      _count: { status: true }
    });
    
    console.log('\nالأجهزة حسب الحالة:');
    statusGroups.forEach(group => {
      console.log(`- ${group.status}: ${group._count.status}`);
    });

    // الأجهزة المتاحة للبيع
    const availableDevices = await prisma.device.findMany({
      where: { status: 'متاح للبيع' },
      select: {
        id: true,
        model: true,
        warehouseId: true,
        supplierId: true,
        price: true,
        condition: true
      }
    });
    
    console.log(`\nالأجهزة المتاحة للبيع (${availableDevices.length}):`);
    availableDevices.forEach((device, index) => {
      console.log(`${index + 1}. ${device.id} - ${device.model}`);
      console.log(`   المخزن: ${device.warehouseId || 'غير محدد'}`);
      console.log(`   المورد: ${device.supplierId || 'غير محدد'}`);
      console.log(`   السعر: ${device.price}`);
      console.log(`   الحالة: ${device.condition}`);
    });

    // فحص الأجهزة بدون مخزن
    const devicesWithoutWarehouse = await prisma.device.findMany({
      where: { warehouseId: null },
      select: { id: true, model: true, status: true }
    });
    
    console.log(`\nالأجهزة بدون مخزن (${devicesWithoutWarehouse.length}):`);
    devicesWithoutWarehouse.forEach(device => {
      console.log(`- ${device.id} - ${device.model} (${device.status})`);
    });

    // فحص المخازن المتاحة
    const warehouses = await prisma.warehouse.findMany({
      select: { id: true, name: true, location: true }
    });
    
    console.log(`\nالمخازن المتاحة (${warehouses.length}):`);
    warehouses.forEach(warehouse => {
      console.log(`- المعرف: ${warehouse.id}, الاسم: ${warehouse.name}, الموقع: ${warehouse.location}`);
    });

    // التحقق من مشكلة "الجهاز غير متوفر"
    console.log('\n=== تحليل مشكلة توفر الأجهزة ===');
    
    // الأجهزة التي يجب أن تظهر في المبيعات
    const salesReadyDevices = await prisma.device.findMany({
      where: {
        AND: [
          { status: 'متاح للبيع' },
          { warehouseId: { not: null } }
        ]
      }
    });
    
    console.log(`الأجهزة الجاهزة للبيع (متاحة + لها مخزن): ${salesReadyDevices.length}`);
    
    if (salesReadyDevices.length > 0) {
      console.log('أمثلة على الأجهزة الجاهزة للبيع:');
      salesReadyDevices.slice(0, 3).forEach(device => {
        console.log(`- ${device.id} - ${device.model} (مخزن: ${device.warehouseId})`);
      });
    }

  } catch (error) {
    console.error('خطأ في فحص قاعدة البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDevicesDatabase();
