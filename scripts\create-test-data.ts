import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestSupplyOrder() {
  try {
    console.log('إنشاء أمر توريد تجريبي للاختبار...\n');

    // إنشاء أمر توريد تجريبي
    const testOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: 'SUP-TEST-1',
        supplierId: 1,
        warehouseId: 1,
        employeeName: 'مستخدم تجريبي',
        supplyDate: new Date().toISOString(),
        status: 'pending',
        notes: 'أمر تجريبي للاختبار'
      }
    });

    console.log(`✅ تم إنشاء أمر التوريد:`);
    console.log(`   - المعرف: ${testOrder.id}`);
    console.log(`   - رقم الأمر: ${testOrder.supplyOrderId}`);
    console.log(`   - الحالة: ${testOrder.status}`);

    // إنشاء جهاز مرتبط بأمر التوريد
    const testDevice = await prisma.device.create({
      data: {
        id: '123456789012346', // الرقم الذي يراه المستخدم
        model: 'جهاز تجريبي للاختبار',
        status: 'متاح للبيع',
        storage: '128GB',
        price: 1000,
        condition: 'جديد',
        warehouseId: 1,
        supplierId: 1,
        dateAdded: new Date().toISOString()
      }
    });

    console.log(`\n✅ تم إنشاء الجهاز:`);
    console.log(`   - المعرف: ${testDevice.id}`);
    console.log(`   - الموديل: ${testDevice.model}`);
    console.log(`   - الحالة: ${testDevice.status}`);
    console.log(`   - المخزن: ${testDevice.warehouseId}`);

    console.log('\n🎯 يمكنك الآن اختبار:');
    console.log('   1. حذف أمر التوريد من الواجهة');
    console.log('   2. رؤية الجهاز في المخزون');
    console.log('   3. إضافة الجهاز في المبيعات');

  } catch (error) {
    console.error('خطأ في إنشاء البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestSupplyOrder();
