import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { SystemSettings } from '@/lib/types';

export type ReportType = 'sales' | 'supply' | 'stocktake' | 'maintenance' | 'custom';

export interface UniversalReportData {
  type: ReportType;
  title: string;
  subtitle?: string;
  headers: string[];
  data: any[][];
  totals?: {
    label: string;
    value: string;
    count?: number;
  };
  customInfo?: {
    label: string;
    value: string;
  }[];
}

export interface ReportOptions {
  orientation?: 'portrait' | 'landscape';
  customLayout?: SystemSettings['reportLayout'];
  showLogo?: boolean;
  showCompanyInfo?: boolean;
  showFooter?: boolean;
}

/**
 * دالة موحدة لإنشاء التقارير بالتصميم المخصص
 */
export function generateUniversalReport(
  settings: SystemSettings,
  reportData: UniversalReportData,
  options: ReportOptions = {}
): jsPDF {
  const layout = options.customLayout || settings.reportLayout;
  
  if (!layout) {
    throw new Error('لا توجد إعدادات تخطيط متاحة');
  }

  // إنشاء PDF جديد
  const doc = new jsPDF({
    orientation: options.orientation || layout.page.orientation,
    unit: 'pt',
    format: 'a4'
  });

  // تمكين RTL
  doc.setR2L(true);

  let currentY = layout.page.marginTop;

  // إضافة الشعار
  if (options.showLogo !== false && settings.logoUrl && layout.logo) {
    try {
      const logoImg = new Image();
      logoImg.src = settings.logoUrl;
      
      let logoX = layout.page.marginLeft;
      if (layout.logo.position === 'center') {
        logoX = (doc.internal.pageSize.width - layout.logo.size) / 2;
      } else if (layout.logo.position === 'right') {
        logoX = doc.internal.pageSize.width - layout.page.marginRight - layout.logo.size;
      }

      doc.addImage(
        logoImg,
        'PNG',
        logoX,
        currentY + layout.logo.marginTop,
        layout.logo.size,
        layout.logo.size
      );
      
      currentY += layout.logo.marginTop + layout.logo.size + layout.logo.marginBottom;
    } catch (error) {
      console.warn('تعذر إضافة الشعار:', error);
    }
  }

  // إضافة العنوان الرئيسي
  if (layout.title) {
    doc.setFontSize(layout.title.fontSize);
    doc.setFont('helvetica', layout.title.fontWeight === 'bold' ? 'bold' : 'normal');
    
    const titleWidth = doc.getTextWidth(reportData.title);
    let titleX = layout.page.marginLeft;
    
    if (layout.title.position === 'center') {
      titleX = (doc.internal.pageSize.width - titleWidth) / 2;
    } else if (layout.title.position === 'right') {
      titleX = doc.internal.pageSize.width - layout.page.marginRight - titleWidth;
    }

    currentY += layout.title.marginTop;
    doc.text(reportData.title, titleX, currentY);
    currentY += layout.title.marginBottom;

    // إضافة العنوان الفرعي
    if (reportData.subtitle) {
      doc.setFontSize(layout.title.fontSize * 0.7);
      doc.setFont('helvetica', 'normal');
      
      const subtitleWidth = doc.getTextWidth(reportData.subtitle);
      let subtitleX = layout.page.marginLeft;
      
      if (layout.title.position === 'center') {
        subtitleX = (doc.internal.pageSize.width - subtitleWidth) / 2;
      } else if (layout.title.position === 'right') {
        subtitleX = doc.internal.pageSize.width - layout.page.marginRight - subtitleWidth;
      }

      currentY += 5;
      doc.text(reportData.subtitle, subtitleX, currentY);
      currentY += layout.title.marginBottom;
    }
  }

  // إضافة معلومات الشركة
  if (options.showCompanyInfo !== false && layout.companyInfo) {
    doc.setFontSize(layout.companyInfo.fontSize);
    doc.setFont('helvetica', layout.companyInfo.fontWeight === 'bold' ? 'bold' : 'normal');
    
    currentY += layout.companyInfo.marginTop;
    
    const companyLines: string[] = [];
    
    if (layout.companyInfo.showArabic) {
      companyLines.push(settings.companyNameAr);
      companyLines.push(settings.addressAr);
      companyLines.push(`${settings.phone} | ${settings.email}`);
    }
    
    if (layout.companyInfo.showEnglish) {
      companyLines.push(settings.companyNameEn);
      companyLines.push(settings.addressEn);
      if (!layout.companyInfo.showArabic) {
        companyLines.push(`${settings.phone} | ${settings.email}`);
      }
    }

    // إضافة معلومات مخصصة إضافية
    if (reportData.customInfo) {
      reportData.customInfo.forEach(info => {
        companyLines.push(`${info.label}: ${info.value}`);
      });
    }

    companyLines.forEach((line, index) => {
      if (line) {
        const lineWidth = doc.getTextWidth(line);
        let lineX = layout.page.marginLeft;
        
        if (layout.companyInfo.position === 'center') {
          lineX = (doc.internal.pageSize.width - lineWidth) / 2;
        } else if (layout.companyInfo.position === 'right') {
          lineX = doc.internal.pageSize.width - layout.page.marginRight - lineWidth;
        }

        doc.text(line, lineX, currentY + (index * (layout.companyInfo.fontSize + 2)));
      }
    });
    
    currentY += (companyLines.length * (layout.companyInfo.fontSize + 2)) + layout.companyInfo.marginBottom;
  }

  // إضافة مسافة قبل الجدول
  currentY += 20;

  // إضافة الجدول
  if (layout.table && reportData.headers.length > 0 && reportData.data.length > 0) {
    // تحضير بيانات الجدول مع صف المجموع
    const tableData = [...reportData.data];
    
    if (reportData.totals) {
      const totalRow = Array(reportData.headers.length).fill('');
      totalRow[totalRow.length - 2] = reportData.totals.label + (reportData.totals.count ? ` (${reportData.totals.count})` : '');
      totalRow[totalRow.length - 1] = reportData.totals.value;
      tableData.push(totalRow);
    }

    autoTable(doc, {
      head: [reportData.headers],
      body: tableData,
      startY: currentY,
      theme: 'grid',
      styles: {
        fontSize: layout.table.fontSize,
        cellPadding: layout.table.cellPadding,
        halign: 'right',
        font: 'helvetica'
      },
      headStyles: {
        fillColor: layout.table.headerBackgroundColor,
        textColor: layout.table.headerTextColor,
        halign: 'center',
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: layout.table.rowAlternateColor
      },
      tableLineColor: layout.table.borderColor,
      tableLineWidth: 0.5,
      // تمييز صف المجموع إذا كان موجوداً
      didParseCell: function (data) {
        if (reportData.totals && data.row.index === tableData.length - 1) {
          data.cell.styles.fillColor = layout.table.headerBackgroundColor;
          data.cell.styles.textColor = layout.table.headerTextColor;
          data.cell.styles.fontStyle = 'bold';
        }
      },
      didDrawPage: (data) => {
        // إضافة التذييل في كل صفحة
        if (options.showFooter !== false && layout.footer) {
          const pageNumber = doc.internal.pages.length - 1;
          const pageCount = doc.internal.pages.length - 1;
          
          doc.setFontSize(layout.footer.fontSize);
          doc.setFont('helvetica', 'normal');
          
          const footerY = doc.internal.pageSize.height - layout.page.marginBottom - 10;
          
          const footerElements: string[] = [];
          
          if (layout.footer.showDate) {
            footerElements.push(`تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}`);
          }
          
          if (layout.footer.showPageNumbers) {
            footerElements.push(`صفحة ${pageNumber} من ${pageCount}`);
          }
          
          if (settings.footerTextAr) {
            footerElements.push(settings.footerTextAr);
          }

          const footerText = footerElements.join(' | ');
          const footerWidth = doc.getTextWidth(footerText);
          
          let footerX = layout.page.marginLeft;
          if (layout.footer.position === 'center') {
            footerX = (doc.internal.pageSize.width - footerWidth) / 2;
          } else if (layout.footer.position === 'right') {
            footerX = doc.internal.pageSize.width - layout.page.marginRight - footerWidth;
          }

          doc.text(footerText, footerX, footerY);
        }
      }
    });
  }

  return doc;
}

/**
 * دالة لتحويل بيانات من صيغة مختلفة إلى صيغة التقرير الموحد
 */
export function convertToUniversalReport(
  type: ReportType,
  data: any,
  customTitle?: string
): UniversalReportData {
  switch (type) {
    case 'sales':
      return {
        type: 'sales',
        title: customTitle || 'فاتورة بيع',
        subtitle: `رقم الفاتورة: ${data.soNumber || 'غير محدد'}`,
        headers: ['الرقم التسلسلي', 'الموديل', 'الحالة', 'السعر'],
        data: (data.items || []).map((item: any) => [
          item.deviceId,
          item.model,
          item.condition,
          item.price ? `${item.price.toLocaleString()} ر.س` : '-'
        ]),
        totals: {
          label: 'إجمالي المبلغ',
          value: data.total ? `${data.total.toLocaleString()} ر.س` : '-',
          count: data.items?.length || 0
        },
        customInfo: [
          { label: 'العميل', value: data.clientName || '-' },
          { label: 'المخزن', value: data.warehouseName || '-' },
          { label: 'التاريخ', value: data.date ? new Date(data.date).toLocaleDateString('ar-SA') : '-' }
        ]
      };

    case 'supply':
      return {
        type: 'supply',
        title: customTitle || 'أمر توريد',
        subtitle: `رقم الأمر: ${data.poNumber || 'غير محدد'}`,
        headers: ['الرقم التسلسلي', 'الموديل', 'المورد', 'التكلفة'],
        data: (data.items || []).map((item: any) => [
          item.deviceId,
          item.model,
          item.supplier || '-',
          item.cost ? `${item.cost.toLocaleString()} ر.س` : '-'
        ]),
        totals: {
          label: 'إجمالي التكلفة',
          value: data.total ? `${data.total.toLocaleString()} ر.س` : '-',
          count: data.items?.length || 0
        },
        customInfo: [
          { label: 'المورد', value: data.supplierName || '-' },
          { label: 'المخزن', value: data.warehouseName || '-' },
          { label: 'التاريخ', value: data.date ? new Date(data.date).toLocaleDateString('ar-SA') : '-' }
        ]
      };

    case 'stocktake':
      return {
        type: 'stocktake',
        title: customTitle || 'تقرير جرد المخزون',
        subtitle: `تاريخ الجرد: ${new Date().toLocaleDateString('ar-SA')}`,
        headers: ['الموديل', 'المتوقع', 'الفعلي', 'الفرق', 'الحالة'],
        data: (data.items || []).map((item: any) => [
          item.model,
          item.expected?.toString() || '0',
          item.actual?.toString() || '0',
          item.difference?.toString() || '0',
          item.status || '-'
        ]),
        totals: {
          label: 'إجمالي الأجهزة',
          value: data.totalCount?.toString() || '0',
          count: data.items?.length || 0
        },
        customInfo: [
          { label: 'المخزن', value: data.warehouseName || '-' },
          { label: 'الموظف المسؤول', value: data.employeeName || '-' }
        ]
      };

    default:
      return {
        type: 'custom',
        title: customTitle || 'تقرير مخصص',
        headers: data.headers || [],
        data: data.data || [],
        totals: data.totals,
        customInfo: data.customInfo
      };
  }
}

/**
 * Hook للحصول على إعدادات التقرير من المتجر
 */
export function useReportLayout() {
  // سيتم تنفيذ هذا لاحقاً عند دمج المكونات
  return null;
}
