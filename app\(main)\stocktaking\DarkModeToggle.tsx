import React from 'react';
import { <PERSON>, <PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useDarkMode } from './useDarkMode';

interface DarkModeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
}

export function DarkModeToggle({ 
  className = '', 
  size = 'md',
  variant = 'outline' 
}: DarkModeToggleProps) {
  const { isDarkMode, toggleDarkMode } = useDarkMode();

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <Button
      variant={variant}
      size="icon"
      onClick={toggleDarkMode}
      className={`
        ${sizeClasses[size]} 
        enhanced-button 
        transition-all 
        duration-300 
        hover:scale-105 
        ${className}
      `}
      title={isDarkMode ? 'تفعيل الوضع النهاري' : 'تفعيل الوضع الليلي'}
    >
      <div className="relative">
        {/* أيقونة الشمس */}
        <Sun 
          className={`
            ${iconSizes[size]} 
            absolute 
            transition-all 
            duration-300 
            ${isDarkMode 
              ? 'rotate-90 scale-0 opacity-0' 
              : 'rotate-0 scale-100 opacity-100'
            }
          `} 
        />
        
        {/* أيقونة القمر */}
        <Moon 
          className={`
            ${iconSizes[size]} 
            absolute 
            transition-all 
            duration-300 
            ${isDarkMode 
              ? 'rotate-0 scale-100 opacity-100' 
              : '-rotate-90 scale-0 opacity-0'
            }
          `} 
        />
      </div>
    </Button>
  );
}

// مكون مبسط للاستخدام في الأماكن الضيقة
export function DarkModeToggleCompact({ className = '' }: { className?: string }) {
  const { isDarkMode, toggleDarkMode } = useDarkMode();

  return (
    <button
      onClick={toggleDarkMode}
      className={`
        p-2 
        rounded-lg 
        transition-all 
        duration-300 
        hover:bg-gray-100 
        dark:hover:bg-gray-800
        ${className}
      `}
      title={isDarkMode ? 'تفعيل الوضع النهاري' : 'تفعيل الوضع الليلي'}
    >
      {isDarkMode ? (
        <Sun className="h-5 w-5 text-yellow-500" />
      ) : (
        <Moon className="h-5 w-5 text-gray-600" />
      )}
    </button>
  );
}

// مكون مع نص توضيحي
export function DarkModeToggleWithLabel({ className = '' }: { className?: string }) {
  const { isDarkMode, toggleDarkMode } = useDarkMode();

  return (
    <div className={`flex items-center space-x-3 space-x-reverse ${className}`}>
      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {isDarkMode ? 'الوضع الليلي' : 'الوضع النهاري'}
      </span>
      
      <button
        onClick={toggleDarkMode}
        className={`
          relative 
          inline-flex 
          h-6 
          w-11 
          items-center 
          rounded-full 
          transition-colors 
          duration-300
          ${isDarkMode 
            ? 'bg-blue-600' 
            : 'bg-gray-200'
          }
        `}
        title={isDarkMode ? 'تفعيل الوضع النهاري' : 'تفعيل الوضع الليلي'}
      >
        <span
          className={`
            inline-block 
            h-4 
            w-4 
            transform 
            rounded-full 
            bg-white 
            transition-transform 
            duration-300
            ${isDarkMode 
              ? 'translate-x-6' 
              : 'translate-x-1'
            }
          `}
        />
        
        {/* أيقونات صغيرة داخل المفتاح */}
        <Sun 
          className={`
            absolute 
            left-1 
            h-3 
            w-3 
            text-yellow-400 
            transition-opacity 
            duration-300
            ${isDarkMode ? 'opacity-0' : 'opacity-100'}
          `} 
        />
        <Moon 
          className={`
            absolute 
            right-1 
            h-3 
            w-3 
            text-blue-200 
            transition-opacity 
            duration-300
            ${isDarkMode ? 'opacity-100' : 'opacity-0'}
          `} 
        />
      </button>
    </div>
  );
}
