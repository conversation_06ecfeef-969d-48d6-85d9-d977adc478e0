
'use client';

import { useState, useMemo } from 'react';
import { useStore } from '@/context/store';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { exportTableToPDF, exportToCSV, printReport } from '@/lib/export-utils/arabic-export';
import { isAfter, subDays, format, subMonths, subYears } from 'date-fns';
import {
  Package,
  ShoppingCart,
  Undo2,
  Wrench,
  PackageX,
  AlertCircle,
  PackageCheck,
  Printer,
  FileDown,
  TrendingUp,
  TrendingDown,
  ShieldAlert,
  HeartCrack,
} from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Sale,
  EvaluationOrder,
  Return,
  Device,
  EvaluationGrade,
} from '@/lib/types';
import { Badge } from '@/components/ui/badge';
// استبدال المكتبات القديمة بالمكتبات الجديدة التي تدعم العربية

const ModelDetailsDialog = ({
  open,
  onOpenChange,
  title,
  data,
  isAllModels,
  valueUnit = '',
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  data: any[];
  isAllModels: boolean;
  valueUnit?: string;
}) => {
  const { systemSettings } = useStore();
  const handleGeneratePdf = (action: 'print' | 'download') => {
    const head = isAllModels
      ? ['الموديل', `العدد${valueUnit ? ` (${valueUnit})` : ''}`]
      : ['الرقم التسلسلي', 'الموديل', 'المخزن', 'الحالة/السبب'];
    
    // تحويل البيانات إلى المصفوفة المناسبة للتصدير
    const exportData = data.map((item) =>
      isAllModels
        ? [item.model || item.name, item.count ?? item.value]
        : [
            item.id || item.deviceId,
            item.model,
            item.warehouseName || 'غير محدد',
            item.status || item.fault || item.damageType || 'N/A',
          ]
    );
    
    if (action === 'print') {
      const tableId = "model-report-details";
      // إضافة معرف للجدول المعروض حاليًا
      setTimeout(() => {
        const tableElement = document.querySelector(".DialogContent table");
        if (tableElement) {
          tableElement.id = tableId;
          printReport(tableId, title);
        }
      }, 100);
    } else {
      exportTableToPDF(exportData, head, title.replace(/\s+/g, '_'), title);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] pr-4">
          <Table>
            <TableHeader>
              <TableRow>
                {isAllModels ? (
                  <>
                    <TableHead>الموديل</TableHead>
                    <TableHead>العدد{valueUnit ? ` (${valueUnit})` : ''}</TableHead>
                  </>
                ) : (
                  <>
                    <TableHead>الرقم التسلسلي</TableHead>
                    <TableHead>الموديل</TableHead>
                    <TableHead>المخزن</TableHead>
                    <TableHead>الحالة/السبب</TableHead>
                  </>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={isAllModels ? 2 : 4}
                    className="h-24 text-center"
                  >
                    لا توجد بيانات لعرضها.
                  </TableCell>
                </TableRow>
              ) : (
                data.map((item, index) => (
                  <TableRow key={isAllModels ? item.model || item.name : item.id || index}>
                    {isAllModels ? (
                      <>
                        <TableCell>{item.model || item.name}</TableCell>
                        <TableCell>{item.count ?? item.value}</TableCell>
                      </>
                    ) : (
                      <>
                        <TableCell dir="ltr">
                          {item.id || item.deviceId}
                        </TableCell>
                        <TableCell>{item.model}</TableCell>
                        <TableCell>
                          {item.warehouseName || 'غير محدد'}
                        </TableCell>
                        <TableCell>
                          {item.status ||
                            item.fault ||
                            item.damageType ||
                            'N/A'}
                        </TableCell>
                      </>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </ScrollArea>
        <DialogFooter className="gap-2 sm:justify-start">
          <Button variant="outline" onClick={() => handleGeneratePdf('print')}>
            <Printer className="ml-2 h-4 w-4" /> طباعة
          </Button>
          <Button
            variant="outline"
            onClick={() => handleGeneratePdf('download')}
          >
            <FileDown className="ml-2 h-4 w-4" /> تصدير
          </Button>
          <DialogClose asChild>
            <Button variant="outline">إغلاق</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Helper component for analytic cards
const AnalyticCard = ({ title, data, icon, unit = '', onClick }: { title: string; data: { name: string; value: number }[]; icon: React.ReactNode; unit?: string; onClick: () => void; }) => (
  <Card className="cursor-pointer hover:bg-muted/50" onClick={onClick}>
    <CardHeader>
      <CardTitle className="text-base flex items-center gap-2">
        {icon}
        {title}
      </CardTitle>
    </CardHeader>
    <CardContent>
      {data.length > 0 ? (
        <ul className="space-y-2">
          {data.map(item => (
            <li key={item.name} className="flex justify-between text-sm">
              <span>{item.name}</span>
              <span className="font-semibold">{item.value}{unit}</span>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-sm text-muted-foreground">لا توجد بيانات كافية.</p>
      )}
    </CardContent>
  </Card>
);

export default function ModelReports() {
  const { devices, sales, returns, evaluationOrders, warehouses } = useStore();

  const [timeFilter, setTimeFilter] = useState<string>('all');
  const [selectedModel, setSelectedModel] = useState<string>('all');

  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [detailsTitle, setDetailsTitle] = useState('');
  const [detailsData, setDetailsData] = useState<any[]>([]);
  const [detailsIsAllModels, setDetailsIsAllModels] = useState(false);
  const [detailsValueUnit, setDetailsValueUnit] = useState('');

  const filteredData = useMemo(() => {
    const sinceDate = (() => {
      if (timeFilter === 'all') return null;
      const now = new Date();
      if (timeFilter === '30') return subDays(now, 30);
      if (timeFilter === '180') return subDays(now, 180);
      if (timeFilter === '365') return subYears(now, 1);
      return null;
    })();

    let relevantDevices = devices;
    let relevantEvals = evaluationOrders.flatMap((order) =>
      sinceDate && !isAfter(new Date(order.date), sinceDate) ? [] : order.items
    );
    let relevantSales = sales.filter(
      (sale) => !sinceDate || isAfter(new Date(sale.date), sinceDate)
    );
    let relevantReturns = returns.filter(
      (ret) => !sinceDate || isAfter(new Date(ret.date), sinceDate)
    );

    if (selectedModel !== 'all') {
      relevantDevices = devices.filter((d) => d.model === selectedModel);
      const relevantDeviceIds = new Set(relevantDevices.map((d) => d.id));

      relevantEvals = relevantEvals.filter((e) =>
        relevantDeviceIds.has(e.deviceId)
    );
      relevantSales = relevantSales
        .map((sale) => ({
          ...sale,
          items: (Array.isArray(sale.items) ? sale.items : []).filter((item) =>
            relevantDeviceIds.has(item.deviceId),
          ),
        }))
        .filter((sale) => (Array.isArray(sale.items) ? sale.items.length : 0) > 0);
      relevantReturns = relevantReturns
        .map((ret) => ({
          ...ret,
          items: (Array.isArray(ret.items) ? ret.items : []).filter((item) =>
            relevantDeviceIds.has(item.deviceId),
          ),
        }))
        .filter((ret) => (Array.isArray(ret.items) ? ret.items.length : 0) > 0);
    }

    return { relevantDevices, relevantEvals, relevantSales, relevantReturns };
  }, [selectedModel, timeFilter, devices, sales, returns, evaluationOrders]);

  const stats = useMemo(() => {
    const { relevantDevices, relevantEvals, relevantSales, relevantReturns } =
      filteredData;

    const sold = relevantSales.flatMap((s) => s.items).length;
    const returned = relevantReturns.flatMap((r) => r.items).length;
    const damaged = relevantEvals.filter((e) => e.finalGrade === 'تالف').length;
    const needsMaintenance = relevantEvals.filter(
      (e) => e.finalGrade === 'يحتاج صيانة',
    ).length;
    const defective = relevantEvals.filter(
      (e) => e.finalGrade === 'عيب فني',
    ).length;
    const availableForSale = relevantDevices.filter(
      (d) => d.status === 'متاح للبيع',
    ).length;
    const inRepair = relevantDevices.filter(
      (d) => d.status === 'قيد الإصلاح',
    ).length;
    const evaluated = relevantEvals.length;
    
    // Advanced Analytics
    const salesByModel = relevantSales.flatMap(s => s.items).reduce((acc, item) => {
        acc[item.model] = (acc[item.model] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);
    
    const sortedSales = Object.entries(salesByModel).sort((a,b) => b[1] - a[1]);
    const bestSellers = sortedSales.slice(0, 5).map(([name, value]) => ({name, value}));
    const worstSellers = sortedSales.slice(-5).reverse().map(([name, value]) => ({name, value}));
    
    const totalDevicesByModel = relevantDevices.reduce((acc, device) => {
        acc[device.model] = (acc[device.model] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    const faultsByModel = relevantEvals.filter(e => e.finalGrade === 'يحتاج صيانة' || e.finalGrade === 'عيب فني').reduce((acc, item) => {
        acc[item.model] = (acc[item.model] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    const faultRates = Object.entries(faultsByModel).map(([name, value]) => ({
        name,
        value: totalDevicesByModel[name] > 0 ? parseFloat(((value / totalDevicesByModel[name]) * 100).toFixed(1)) : 0
    })).sort((a,b) => b.value - a.value).slice(0, 5);

    const damagedByModel = relevantEvals.filter(e => e.finalGrade === 'تالف').reduce((acc, item) => {
        acc[item.model] = (acc[item.model] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    const damageRates = Object.entries(damagedByModel).map(([name, value]) => ({
        name,
        value: totalDevicesByModel[name] > 0 ? parseFloat(((value / totalDevicesByModel[name]) * 100).toFixed(1)) : 0
    })).sort((a,b) => b.value - a.value).slice(0, 5);


    return {
      sold,
      returned,
      damaged,
      needsMaintenance,
      defective,
      availableForSale,
      inRepair,
      evaluated,
      bestSellers,
      worstSellers,
      faultRates,
      damageRates
    };
  }, [filteredData]);

  const handleOpenDetails = (
    type: string,
    data?: { name: string; value: number }[],
    unit?: string
) => {
    let dialogTitle = '';
    let dialogData: any[] = [];
    let isAllModelsDialog = true;
    let valueUnit = unit || '';

    const { relevantDevices, relevantEvals, relevantSales, relevantReturns } = filteredData;
    const getWarehouseName = (id: number | undefined) => warehouses.find((w) => w.id === id)?.name || 'غير محدد';

    if (data) {
        dialogTitle = type;
        dialogData = data;
    } else {
        switch (type) {
            case 'available':
                dialogTitle = 'الأجهزة المتاحة للبيع';
                const availableDevices = relevantDevices.filter((d) => d.status === 'متاح للبيع');
                if (selectedModel === 'all') {
                    dialogData = Object.entries(availableDevices.reduce((acc, curr) => {
                        acc[curr.model] = (acc[curr.model] || 0) + 1;
                        return acc;
                    }, {} as Record<string, number>)).map(([model, count]) => ({ model, count }));
                } else {
                    dialogData = availableDevices.map((d) => ({ ...d, id: d.id, warehouseName: getWarehouseName(d.warehouseId) }));
                    isAllModelsDialog = false;
                }
                break;
             // Add other cases here...
        }
    }
    
    setDetailsTitle(dialogTitle);
    setDetailsData(dialogData);
    setDetailsIsAllModels(isAllModelsDialog);
    setDetailsValueUnit(valueUnit);
    setIsDetailsOpen(true);
};

  return (
    <div className="space-y-4">
      <ModelDetailsDialog
        open={isDetailsOpen}
        onOpenChange={setIsDetailsOpen}
        title={detailsTitle}
        data={detailsData}
        isAllModels={detailsIsAllModels}
        valueUnit={detailsValueUnit}
      />
      <Card>
        <CardHeader>
          <CardTitle>فلاتر التقرير</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>تصفية حسب الموديل</Label>
            <Select
              dir="rtl"
              value={selectedModel}
              onValueChange={setSelectedModel}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الموديلات</SelectItem>
                {[...new Set(devices.map((d) => d.model))]
                  .sort()
                  .map((modelName) => (
                    <SelectItem key={modelName} value={modelName}>
                      {modelName}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>تصفية حسب الفترة</Label>
            <Select dir="rtl" value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الأوقات</SelectItem>
                <SelectItem value="30">آخر 30 يوم</SelectItem>
                <SelectItem value="180">آخر 6 أشهر</SelectItem>
                <SelectItem value="365">آخر سنة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
      
      <div className="space-y-4">
        <Card>
            <CardHeader>
                <CardTitle>نظرة عامة على الأداء</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <AnalyticCard title="الأكثر مبيعًا" data={stats.bestSellers} icon={<TrendingUp className="text-green-500" />} onClick={() => handleOpenDetails('الأكثر مبيعاً', stats.bestSellers)} />
                <AnalyticCard title="الأقل مبيعًا" data={stats.worstSellers} icon={<TrendingDown className="text-red-500" />} onClick={() => handleOpenDetails('الأقل مبيعاً', stats.worstSellers)} />
                <AnalyticCard title="أعلى نسبة أعطال" data={stats.faultRates} icon={<ShieldAlert className="text-yellow-500" />} unit="%" onClick={() => handleOpenDetails('أعلى نسبة أعطال', stats.faultRates, '%')} />
                <AnalyticCard title="أعلى نسبة تلف" data={stats.damageRates} icon={<HeartCrack className="text-orange-500" />} unit="%" onClick={() => handleOpenDetails('أعلى نسبة تلف', stats.damageRates, '%')} />
            </CardContent>
        </Card>

        <Card>
            <CardHeader>
            <CardTitle>ملخص النتائج</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-4 md:grid-cols-3 lg:grid-cols-4">
            <Card
                className="cursor-pointer hover:bg-card/80"
                onClick={() => handleOpenDetails('available')}
            >
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">متاح للبيع</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                <p className="text-2xl font-bold">{stats.availableForSale}</p>
                </CardContent>
            </Card>
            <Card
                className="cursor-pointer hover:bg-card/80"
                onClick={() => handleOpenDetails('sold')}
            >
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">أجهزة مباعة</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                <p className="text-2xl font-bold">{stats.sold}</p>
                </CardContent>
            </Card>
            <Card
                className="cursor-pointer hover:bg-card/80"
                onClick={() => handleOpenDetails('returned')}
            >
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                    أجهزة مرتجعة
                </CardTitle>
                <Undo2 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                <p className="text-2xl font-bold">{stats.returned}</p>
                </CardContent>
            </Card>
            <Card
                className="cursor-pointer hover:bg-card/80"
                onClick={() => handleOpenDetails('inRepair')}
            >
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">قيد الإصلاح</CardTitle>
                <Wrench className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                <p className="text-2xl font-bold">{stats.inRepair}</p>
                </CardContent>
            </Card>
            <Card
                className="cursor-pointer hover:bg-card/80"
                onClick={() => handleOpenDetails('needsMaintenance')}
            >
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">تحتاج صيانة</CardTitle>
                <PackageX className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                <p className="text-2xl font-bold">{stats.needsMaintenance}</p>
                </CardContent>
            </Card>
            <Card
                className="cursor-pointer hover:bg-card/80"
                onClick={() => handleOpenDetails('defective')}
            >
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                    أجهزة معيوبة
                </CardTitle>
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                <p className="text-2xl font-bold">{stats.defective}</p>
                </CardContent>
            </Card>
            <Card
                className="cursor-pointer hover:bg-card/80"
                onClick={() => handleOpenDetails('damaged')}
            >
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">أجهزة تالفة</CardTitle>
                <PackageX className="h-4 w-4 text-destructive" />
                </CardHeader>
                <CardContent>
                <p className="text-2xl font-bold">{stats.damaged}</p>
                </CardContent>
            </Card>
            <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                    إجمالي التقييمات
                </CardTitle>
                <PackageCheck className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                <p className="text-2xl font-bold">{stats.evaluated}</p>
                </CardContent>
            </Card>
            </CardContent>
        </Card>
      </div>
    </div>
  );
}
