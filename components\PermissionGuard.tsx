'use client';

import { ReactNode } from 'react';
import { usePermission } from '@/hooks/usePermission';
import { PermissionPageKey } from '@/lib/types';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ShieldX } from 'lucide-react';

interface PermissionGuardProps {
  children: ReactNode;
  pageKey: PermissionPageKey;
  requiredPermission?: 'view' | 'create' | 'edit' | 'delete';
  fallback?: ReactNode;
  showError?: boolean;
}

/**
 * مكون حماية الصلاحيات
 * يعرض المحتوى فقط إذا كان لدى المستخدم الصلاحية المطلوبة
 */
export function PermissionGuard({
  children,
  pageKey,
  requiredPermission = 'view',
  fallback,
  showError = true,
}: PermissionGuardProps) {
  const permissions = usePermission(pageKey);

  // التحقق من الصلاحية المطلوبة
  let hasPermission = false;
  switch (requiredPermission) {
    case 'view':
      hasPermission = permissions.canView;
      break;
    case 'create':
      hasPermission = permissions.canCreate;
      break;
    case 'edit':
      hasPermission = permissions.canEdit;
      break;
    case 'delete':
      hasPermission = permissions.canDelete;
      break;
    default:
      hasPermission = permissions.canView;
  }

  // إذا كان لدى المستخدم الصلاحية، عرض المحتوى
  if (hasPermission) {
    return <>{children}</>;
  }

  // إذا تم توفير fallback، عرضه
  if (fallback) {
    return <>{fallback}</>;
  }

  // إذا كان showError مفعل، عرض رسالة خطأ
  if (showError) {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-8">
        <Alert className="max-w-md">
          <ShieldX className="h-4 w-4" />
          <AlertDescription className="text-center">
            <div className="space-y-2">
              <p className="font-medium">لا تملك صلاحية الوصول</p>
              <p className="text-sm text-muted-foreground">
                ليس لديك الصلاحية المطلوبة للوصول إلى هذه الصفحة أو تنفيذ هذا الإجراء.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // إذا لم يكن showError مفعل، لا تعرض شيئاً
  return null;
}

/**
 * مكون مبسط لحماية صلاحية العرض فقط
 */
export function ViewGuard({
  children,
  pageKey,
  fallback,
}: {
  children: ReactNode;
  pageKey: PermissionPageKey;
  fallback?: ReactNode;
}) {
  return (
    <PermissionGuard
      pageKey={pageKey}
      requiredPermission="view"
      fallback={fallback}
      showError={false}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * مكون لحماية الأزرار والإجراءات
 */
export function ActionGuard({
  children,
  pageKey,
  action,
}: {
  children: ReactNode;
  pageKey: PermissionPageKey;
  action: 'create' | 'edit' | 'delete';
}) {
  return (
    <PermissionGuard
      pageKey={pageKey}
      requiredPermission={action}
      showError={false}
    >
      {children}
    </PermissionGuard>
  );
}
