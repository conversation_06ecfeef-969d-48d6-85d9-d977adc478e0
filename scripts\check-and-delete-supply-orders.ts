import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkSupplyOrders() {
  try {
    console.log('فحص أوامر التوريد الموجودة...');

    // عرض جميع أوامر التوريد
    const allOrders = await prisma.supplyOrder.findMany({
      select: {
        id: true,
        supplyOrderId: true,
        supplierId: true,
        employeeName: true,
        status: true,
        invoiceNumber: true,
        supplyDate: true,
        warehouseId: true,
        createdAt: true
      },
      orderBy: { id: 'asc' }
    });

    console.log(`تم العثور على ${allOrders.length} أمر توريد:`);
    
    if (allOrders.length === 0) {
      console.log('لا توجد أوامر توريد');
    } else {
      allOrders.forEach((order, index) => {
        console.log(`${index + 1}. ID: ${order.id} | رقم الأمر: ${order.supplyOrderId} | المورد ID: ${order.supplierId} | الموظف: ${order.employeeName} | الحالة: ${order.status} | تاريخ التوريد: ${order.supplyDate} | تاريخ الإنشاء: ${order.createdAt}`);
      });
    }

    console.log('\n--- محاولة حذف الأوامر ---');
    
    // محاولة حذف كل أمر
    for (const order of allOrders) {
      try {
        console.log(`محاولة حذف الأمر: ${order.supplyOrderId} (ID: ${order.id})`);
        
        await prisma.supplyOrder.delete({
          where: { id: order.id }
        });
        
        console.log(`✅ تم حذف الأمر ${order.supplyOrderId} بنجاح`);
      } catch (deleteError) {
        console.error(`❌ خطأ في حذف الأمر ${order.supplyOrderId}:`, deleteError);
      }
    }

    // التحقق من النتيجة النهائية
    const remainingOrders = await prisma.supplyOrder.findMany();
    console.log(`\nالأوامر المتبقية بعد الحذف: ${remainingOrders.length}`);

  } catch (error) {
    console.error('خطأ في فحص أوامر التوريد:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSupplyOrders();
