# دليل المطور - النظام الجديد لإدارة الأجهزة

## 🎯 نظرة عامة
تم إعادة تصميم قاعدة البيانات بالكامل لضمان التكامل المثالي بين جميع أقسام النظام. النظام الجديد يدعم:

### ✨ الميزات الرئيسية
- **تتبع شامل للأجهزة** عبر جميع العمليات
- **إدارة متقدمة للمخازن** مع تتبع المخزون
- **نظام أرقام موحد** لجميع الأوامر
- **تاريخ كامل** لجميع الحركات والعمليات
- **تقارير دقيقة** ومفصلة

## 🏗️ هيكل النظام الجديد

### 📦 الكيانات الرئيسية

#### 1. الجهاز (Device)
```typescript
interface Device {
  id: string              // IMEI/Serial Number
  barcode?: string        // الباركود المولد
  manufacturerId: number  // الشركة المصنعة
  deviceModelId: number   // الموديل
  warehouseId: number     // المخزن الحالي
  supplierId?: number     // المورد الأصلي
  status: string          // الحالة: في المخزن، مُباع، قيد الصيانة
  condition: string       // الحالة: جديد، مستخدم
  grade?: string          // التقييم: A+, A, B, C
  price?: number          // السعر الحالي
  costPrice?: number      // سعر التكلفة
}
```

#### 2. المخزن (Warehouse)
```typescript
interface Warehouse {
  id: number
  name: string            // اسم المخزن
  code?: string          // رمز المخزن
  type: string           // main, branch, maintenance, evaluation
  location: string       // الموقع
  capacity?: number      // السعة القصوى
  status: string         // Active, Inactive, Maintenance
}
```

#### 3. مخزون المخزن (WarehouseStock)
```typescript
interface WarehouseStock {
  warehouseId: number
  manufacturerId: number
  deviceModelId: number
  condition: string
  totalQuantity: number       // الكمية الإجمالية
  availableQuantity: number   // الكمية المتاحة
  reservedQuantity: number    // الكمية المحجوزة
}
```

## 🔄 تدفق العمليات

### 1. عملية التوريد
```mermaid
graph LR
    A[إنشاء أمر توريد] --> B[إضافة الأجهزة]
    B --> C[تحديث حالة الأجهزة]
    C --> D[تحديث مخزون المخزن]
    D --> E[تسجيل حركة الأجهزة]
```

### 2. عملية البيع
```mermaid
graph LR
    A[إنشاء أمر بيع] --> B[اختيار الأجهزة]
    B --> C[تحديث حالة إلى مُباع]
    C --> D[تحديث مخزون المخزن]
    D --> E[تسجيل حركة الأجهزة]
```

### 3. عملية الإرجاع
```mermaid
graph LR
    A[إنشاء أمر إرجاع] --> B[ربط بالبيع الأصلي]
    B --> C{نوع الإرجاع}
    C -->|استرداد| D[تحديث حالة إلى في المخزن]
    C -->|استبدال| E[جهاز جديد + إرجاع القديم]
    D --> F[تحديث المخزون]
    E --> F
```

## 💻 أمثلة الكود

### 1. إنشاء أمر توريد جديد
```typescript
async function createSupplyOrder(data: {
  supplierId: number
  warehouseId: number
  employeeId: number
  devices: Array<{
    imei: string
    manufacturerId: number
    deviceModelId: number
    condition: string
    costPrice: number
  }>
}) {
  const orderNumber = await generateOrderNumber('SO') // SO-2025-0001
  
  const supplyOrder = await prisma.supplyOrder.create({
    data: {
      orderNumber,
      supplierId: data.supplierId,
      warehouseId: data.warehouseId,
      employeeId: data.employeeId,
      totalQuantity: data.devices.length,
      status: 'confirmed'
    }
  })

  // إنشاء الأجهزة وعناصر الأمر
  for (const deviceData of data.devices) {
    // إنشاء الجهاز
    const device = await prisma.device.create({
      data: {
        id: deviceData.imei,
        manufacturerId: deviceData.manufacturerId,
        deviceModelId: deviceData.deviceModelId,
        warehouseId: data.warehouseId,
        supplierId: data.supplierId,
        condition: deviceData.condition,
        costPrice: deviceData.costPrice,
        status: 'في المخزن'
      }
    })

    // إنشاء عنصر الأمر
    await prisma.supplyOrderItem.create({
      data: {
        supplyOrderId: supplyOrder.id,
        deviceId: device.id,
        manufacturerId: deviceData.manufacturerId,
        deviceModelId: deviceData.deviceModelId,
        condition: deviceData.condition,
        costPrice: deviceData.costPrice
      }
    })

    // تحديث مخزون المخزن
    await updateWarehouseStock(
      data.warehouseId,
      deviceData.manufacturerId,
      deviceData.deviceModelId,
      deviceData.condition,
      1 // زيادة بواحد
    )

    // تسجيل حركة الجهاز
    await prisma.deviceMovement.create({
      data: {
        deviceId: device.id,
        movementType: 'supply',
        toWarehouseId: data.warehouseId,
        toStatus: 'في المخزن',
        referenceType: 'supply_order',
        referenceId: supplyOrder.id.toString(),
        referenceNumber: orderNumber,
        employeeId: data.employeeId,
        employeeName: 'اسم الموظف' // يُستخرج من قاعدة البيانات
      }
    })
  }

  return supplyOrder
}
```

### 2. بيع جهاز
```typescript
async function createSale(data: {
  clientId: number
  warehouseId: number
  employeeId: number
  devices: Array<{
    deviceId: string
    unitPrice: number
  }>
}) {
  const saleNumber = await generateOrderNumber('SL') // SL-2025-0001
  
  const sale = await prisma.sale.create({
    data: {
      saleNumber,
      clientId: data.clientId,
      warehouseId: data.warehouseId,
      employeeId: data.employeeId,
      totalQuantity: data.devices.length,
      totalAmount: data.devices.reduce((sum, d) => sum + d.unitPrice, 0),
      netAmount: data.devices.reduce((sum, d) => sum + d.unitPrice, 0)
    }
  })

  for (const deviceData of data.devices) {
    // تحديث حالة الجهاز
    const device = await prisma.device.update({
      where: { id: deviceData.deviceId },
      data: { 
        status: 'مُباع',
        price: deviceData.unitPrice
      },
      include: {
        manufacturer: true,
        deviceModel: true
      }
    })

    // إنشاء عنصر البيع
    await prisma.saleItem.create({
      data: {
        saleId: sale.id,
        deviceId: deviceData.deviceId,
        unitPrice: deviceData.unitPrice,
        finalPrice: deviceData.unitPrice
      }
    })

    // تحديث مخزون المخزن (تقليل)
    await updateWarehouseStock(
      data.warehouseId,
      device.manufacturerId,
      device.deviceModelId,
      device.condition,
      -1 // تقليل بواحد
    )

    // تسجيل حركة الجهاز
    await prisma.deviceMovement.create({
      data: {
        deviceId: device.id,
        movementType: 'sale',
        fromWarehouseId: data.warehouseId,
        fromStatus: 'في المخزن',
        toStatus: 'مُباع',
        referenceType: 'sale',
        referenceId: sale.id.toString(),
        referenceNumber: saleNumber,
        employeeId: data.employeeId,
        employeeName: 'اسم الموظف'
      }
    })
  }

  return sale
}
```

### 3. تحديث مخزون المخزن
```typescript
async function updateWarehouseStock(
  warehouseId: number,
  manufacturerId: number,
  deviceModelId: number,
  condition: string,
  quantityChange: number
) {
  await prisma.warehouseStock.upsert({
    where: {
      warehouseId_manufacturerId_deviceModelId_condition: {
        warehouseId,
        manufacturerId,
        deviceModelId,
        condition
      }
    },
    update: {
      totalQuantity: { increment: quantityChange },
      availableQuantity: { increment: quantityChange }
    },
    create: {
      warehouseId,
      manufacturerId,
      deviceModelId,
      condition,
      totalQuantity: Math.max(0, quantityChange),
      availableQuantity: Math.max(0, quantityChange)
    }
  })
}
```

### 4. البحث والاستعلامات

#### البحث عن الأجهزة المتاحة
```typescript
async function getAvailableDevices(filters: {
  warehouseId?: number
  manufacturerId?: number
  deviceModelId?: number
  condition?: string
  status?: string
}) {
  return await prisma.device.findMany({
    where: {
      ...filters,
      status: filters.status || 'في المخزن',
      isActive: true
    },
    include: {
      manufacturer: true,
      deviceModel: true,
      warehouse: true,
      supplier: true
    },
    orderBy: {
      createdAt: 'desc'
    }
  })
}
```

#### الحصول على مخزون المخزن
```typescript
async function getWarehouseInventory(warehouseId: number) {
  return await prisma.warehouseStock.findMany({
    where: { warehouseId },
    include: {
      manufacturer: true,
      deviceModel: true,
      warehouse: true
    },
    orderBy: [
      { manufacturer: { name: 'asc' } },
      { deviceModel: { name: 'asc' } },
      { condition: 'asc' }
    ]
  })
}
```

#### تتبع تاريخ الجهاز
```typescript
async function getDeviceHistory(deviceId: string) {
  return await prisma.deviceMovement.findMany({
    where: { deviceId },
    include: {
      employee: { select: { name: true, username: true } }
    },
    orderBy: { movementDate: 'desc' }
  })
}
```

## 🛠️ وظائف مساعدة

### 1. مولد أرقام الأوامر
```typescript
async function generateOrderNumber(prefix: string): Promise<string> {
  const year = new Date().getFullYear()
  const lastOrder = await prisma.$queryRaw`
    SELECT MAX(CAST(SUBSTRING(order_number, LENGTH('${prefix}-${year}-') + 1) AS INTEGER)) as last_number
    FROM orders 
    WHERE order_number LIKE '${prefix}-${year}-%'
  `
  
  const nextNumber = (lastOrder[0]?.last_number || 0) + 1
  return `${prefix}-${year}-${nextNumber.toString().padStart(4, '0')}`
}
```

### 2. التحقق من توفر الجهاز
```typescript
async function isDeviceAvailable(deviceId: string): Promise<boolean> {
  const device = await prisma.device.findUnique({
    where: { id: deviceId },
    select: { status: true, isActive: true }
  })
  
  return device?.isActive && device?.status === 'في المخزن'
}
```

### 3. حساب قيمة المخزون
```typescript
async function calculateWarehouseValue(warehouseId: number) {
  const devices = await prisma.device.findMany({
    where: {
      warehouseId,
      status: 'في المخزن',
      isActive: true
    },
    select: {
      costPrice: true,
      price: true
    }
  })

  return {
    totalCostValue: devices.reduce((sum, d) => sum + (d.costPrice || 0), 0),
    totalSellingValue: devices.reduce((sum, d) => sum + (d.price || 0), 0),
    deviceCount: devices.length
  }
}
```

## 📊 تقارير مهمة

### 1. تقرير المخزون الحالي
```typescript
async function getInventoryReport() {
  return await prisma.warehouseStock.groupBy({
    by: ['warehouseId', 'manufacturerId', 'condition'],
    _sum: {
      totalQuantity: true,
      availableQuantity: true
    },
    _count: true
  })
}
```

### 2. تقرير المبيعات اليومية
```typescript
async function getDailySalesReport(date: Date) {
  return await prisma.sale.findMany({
    where: {
      saleDate: {
        gte: startOfDay(date),
        lte: endOfDay(date)
      }
    },
    include: {
      client: true,
      warehouse: true,
      employee: { select: { name: true } },
      items: {
        include: {
          device: {
            include: {
              manufacturer: true,
              deviceModel: true
            }
          }
        }
      }
    }
  })
}
```

## 🔒 الأمان والصلاحيات

### تسجيل العمليات التدقيقية
```typescript
async function logAuditAction(
  userId: number,
  operation: string,
  tableName: string,
  recordId: string,
  oldValues?: any,
  newValues?: any,
  details?: string
) {
  await prisma.auditLog.create({
    data: {
      userId,
      username: 'اسم المستخدم', // يُستخرج من قاعدة البيانات
      operation,
      tableName,
      recordId,
      oldValues: oldValues ? JSON.stringify(oldValues) : null,
      newValues: newValues ? JSON.stringify(newValues) : null,
      details: details || `${operation} ${tableName}`,
      ipAddress: 'IP Address', // يُستخرج من الطلب
      userAgent: 'User Agent'   // يُستخرج من الطلب
    }
  })
}
```

## 📝 الخطوات التالية

1. **تحديث المكونات**: تحديث جميع مكونات React لاستخدام النماذج الجديدة
2. **تحديث API Routes**: تحديث جميع نقاط API للتوافق مع الهيكل الجديد
3. **تحديث الصفحات**: تحديث صفحات التطبيق لاستخدام الوظائف الجديدة
4. **الاختبار**: اختبار شامل لجميع الوظائف
5. **التوثيق**: إنشاء دليل المستخدم النهائي

## 🆘 المساعدة والدعم

- **المراجع**: استخدم الأمثلة أعلاه كنماذج
- **التصحيح**: تحقق من logs قاعدة البيانات عند الأخطاء
- **الأداء**: استخدم الفهارس المناسبة للاستعلامات المعقدة
- **الأمان**: تأكد من تسجيل جميع العمليات المهمة
