import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDevicesEndpoint() {
  try {
    console.log('=== اختبار endpoint الأجهزة ===\n');

    // اختبار مباشر لapi endpoint
    const response = await fetch('http://localhost:3000/api/devices-simple?limit=100');
    const data = await response.json();
    
    console.log('استجابة API:');
    console.log(`- الحالة: ${response.status}`);
    console.log(`- عدد الأجهزة: ${data.data?.length || 0}`);
    console.log(`- الإجمالي: ${data.total || 0}`);
    
    if (data.data && data.data.length > 0) {
      console.log('\nأول 3 أجهزة:');
      data.data.slice(0, 3).forEach((device, index) => {
        console.log(`${index + 1}. ${device.id} - ${device.model} (${device.status})`);
        console.log(`   المخزن: ${device.warehouseId || 'غير محدد'}`);
        console.log(`   المورد: ${device.supplierId || 'غير محدد'}`);
      });
    }

    // اختبار مباشر لقاعدة البيانات
    console.log('\n=== اختبار قاعدة البيانات مباشرة ===');
    const dbDevices = await prisma.device.findMany({
      take: 10,
      orderBy: { dateAdded: 'desc' }
    });
    
    console.log(`عدد الأجهزة في قاعدة البيانات: ${dbDevices.length}`);
    dbDevices.forEach((device, index) => {
      console.log(`${index + 1}. ${device.id} - ${device.model} (${device.status})`);
    });

    // فحص مشاكل الأجهزة المتاحة للبيع
    const availableDevices = await prisma.device.findMany({
      where: { status: 'متاح للبيع' }
    });
    
    console.log(`\nالأجهزة المتاحة للبيع: ${availableDevices.length}`);
    
    // فحص الأجهزة بدون مخزن
    const devicesWithoutWarehouse = await prisma.device.findMany({
      where: { warehouseId: null }
    });
    
    console.log(`الأجهزة بدون مخزن: ${devicesWithoutWarehouse.length}`);

  } catch (error) {
    console.error('خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDevicesEndpoint();
