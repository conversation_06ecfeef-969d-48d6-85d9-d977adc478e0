'use client';

import { useState, useEffect } from 'react';
import { useStore } from '@/context/store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User, Crown, Settings, LogIn, Users } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function UserSwitcherPage() {
  const { currentUser, users, setCurrentUser, fetchUsersData } = useStore();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Refresh users data on page load
  useEffect(() => {
    const refreshUsers = async () => {
      try {
        setIsLoading(true);
        const usersData = await fetchUsersData({ pagination: { limit: 100 } });
        console.log('تم تحديث قائمة المستخدمين:', usersData.data);
      } catch (error) {
        console.error('خطأ في تحديث المستخدمين:', error);
      } finally {
        setIsLoading(false);
      }
    };

    refreshUsers();
  }, [fetchUsersData]);

  const handleSwitchUser = (user: any) => {
    setCurrentUser(user);
    toast({
      title: 'تم تغيير المستخدم',
      description: `تم تسجيل الدخول كـ ${user.name}`,
    });
  };

  const getRoleColor = (role: string) => {
    switch (role?.toLowerCase()) {
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'manager':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'صيانة':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role?.toLowerCase()) {
      case 'admin':
        return <Crown className="h-4 w-4" />;
      case 'manager':
        return <Settings className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Users className="h-8 w-8" />
          إدارة المستخدمين وتسجيل الدخول
        </h1>
        <p className="text-muted-foreground">
          اختر المستخدم الذي تريد تسجيل الدخول به
        </p>
      </div>

      {/* Current User Card */}
      {currentUser && (
        <Card className="border-2 border-primary">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LogIn className="h-5 w-5" />
              المستخدم الحالي
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage
                  src={currentUser.photo || 'https://placehold.co/64x64'}
                  alt={currentUser.name}
                />
                <AvatarFallback className="text-lg">
                  {currentUser.name?.charAt(0) || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="text-xl font-semibold">{currentUser.name}</h3>
                <p className="text-muted-foreground">@{currentUser.username}</p>
                <p className="text-sm text-muted-foreground">{currentUser.email}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge className={getRoleColor(currentUser.role || '')}>
                    {getRoleIcon(currentUser.role || '')}
                    {currentUser.role || 'مستخدم'}
                  </Badge>
                  <Badge variant="outline">
                    {currentUser.status === 'Active' ? 'نشط' : 'غير نشط'}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Available Users */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            المستخدمين المتاحين ({users.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p>جاري تحميل المستخدمين...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">لا يوجد مستخدمين متاحين</p>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {users.map((user) => (
                <Card
                  key={user.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    currentUser?.id === user.id
                      ? 'ring-2 ring-primary bg-primary/5'
                      : 'hover:bg-muted/50'
                  }`}
                  onClick={() => handleSwitchUser(user)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage
                          src={user.photo || 'https://placehold.co/40x40'}
                          alt={user.name}
                        />
                        <AvatarFallback>
                          {user.name?.charAt(0) || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">{user.name}</h4>
                        <p className="text-sm text-muted-foreground truncate">
                          @{user.username}
                        </p>
                        <div className="flex items-center gap-1 mt-1">
                          <Badge
                            size="sm"
                            className={getRoleColor(user.role || '')}
                          >
                            {getRoleIcon(user.role || '')}
                            {user.role || 'مستخدم'}
                          </Badge>
                        </div>
                      </div>
                      {currentUser?.id === user.id && (
                        <div className="text-primary">
                          <LogIn className="h-4 w-4" />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Details */}
      {currentUser && (
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل المستخدم الحالي</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  معرف المستخدم
                </label>
                <p className="font-mono">{currentUser.id}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  البريد الإلكتروني
                </label>
                <p>{currentUser.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  رقم الهاتف
                </label>
                <p>{currentUser.phone || 'غير محدد'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  موقع الفرع
                </label>
                <p>{currentUser.branchLocation || 'غير محدد'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  عدد الصلاحيات
                </label>
                <p>
                  {currentUser.permissions
                    ? Object.keys(currentUser.permissions).length
                    : 0}{' '}
                  صلاحية
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  آخر تسجيل دخول
                </label>
                <p>{currentUser.lastLogin || 'غير محدد'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
