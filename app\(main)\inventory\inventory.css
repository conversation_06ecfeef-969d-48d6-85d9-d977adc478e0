/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 10px;
  height: 0; /* إخفاء شريط التمرير الأفقي */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* For dark mode */
@media (prefers-color-scheme: dark) {
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1e1e1e;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #555;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #777;
  }
  
  .custom-scrollbar {
    scrollbar-color: #555 #1e1e1e;
  }
}
