import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testModelCreation() {
  try {
    console.log('🧪 اختبار إضافة موديل جديد...');
    
    // البحث عن شركة مصنعة موجودة أو إنشاء واحدة
    let manufacturer = await prisma.manufacturer.findFirst();
    
    if (!manufacturer) {
      manufacturer = await prisma.manufacturer.create({
        data: {
          name: 'Apple',
          code: 'APL'
        }
      });
      console.log('✅ تم إنشاء شركة مصنعة جديدة:', manufacturer.name);
    }
    
    // إنشاء موديل جديد للاختبار
    const testModel = await prisma.deviceModel.create({
      data: {
        name: `iPhone 15 Pro Test ${Date.now()}`,
        manufacturerId: manufacturer.id,
        category: 'هاتف ذكي'
      }
    });
    
    console.log('✅ تم إنشاء موديل جديد للاختبار:', testModel.name);
    
    // جلب جميع الموديلات للتأكد
    const allModels = await prisma.deviceModel.findMany({
      include: {
        manufacturer: true
      }
    });
    
    console.log(`📱 إجمالي الموديلات المتوفرة: ${allModels.length}`);
    allModels.forEach(model => {
      console.log(`  - ${model.manufacturer.name} ${model.name}`);
    });
    
    return testModel;
  } catch (error) {
    console.error('❌ خطأ في اختبار إنشاء الموديل:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الاختبار
testModelCreation()
  .then(model => {
    console.log('✅ تم الانتهاء من اختبار إنشاء الموديل بنجاح');
    console.log('🔄 يرجى تحديث صفحة المخزون لرؤية الموديل الجديد في قائمة البحث');
  })
  .catch(error => {
    console.error('❌ فشل اختبار إنشاء الموديل:', error);
    process.exit(1);
  });
