# مكتبة تصدير PDF بالعربية

هذه المكتبة توفر وظائف محسنة لتصدير ملفات PDF باللغة العربية بدون الحاجة لاستخدام المتصفح، مع حل مشكلة الأحرف الغريبة.

## الملفات

### `canvas-pdf.ts` ⭐ **الحل المُوصى به**
يحتوي على وظيفة تصدير PDF باستخدام Canvas لرسم النصوص العربية:
- `createArabicPDFWithCanvas()`: إنشاء PDF بالعربية باستخدام Canvas
- يحل مشكلة الأحرف الغريبة (þ³þ þÞ þ—þŽþ-þóþ¦) نهائياً
- يرسم النصوص كصور بدلاً من نصوص
- دعم كامل للاتجاه RTL

### `arabic-fonts.ts`
يحتوي على وظائف مساعدة للتعامل مع النصوص العربية في PDF:
- `createArabicPDF()`: إنشاء مستند PDF مع إعدادات عربية افتراضية
- `setupArabicFont()`: تهيئة الخط العربي
- `processArabicText()`: معالجة النص العربي قبل الإضافة
- `writeArabicText()`: كتابة نص عربي مع معالجة خاصة
- `splitArabicText()`: تقسيم النص الطويل إلى أسطر

### `html-to-pdf.ts`
يحتوي على وظائف التصدير البديلة:
- `exportDataToPDF()`: تصدير بيانات جدولية إلى PDF
- `exportDeviceTrackingReport()`: تصدير تقرير تتبع الجهاز (طباعة)
- `exportDeviceTrackingReportDirect()`: تصدير HTML محسن
- `exportHTMLToPDF()`: تصدير HTML إلى PDF (للطباعة)
- `exportToCSV()`: تصدير إلى CSV

## الاستخدام

### تصدير تقرير تتبع الجهاز (الطريقة المُوصى بها)

```typescript
import { createArabicPDFWithCanvas } from '@/lib/export-utils/canvas-pdf';

const deviceData = {
  model: "iPhone 14 Pro Max",
  id: "123456789012345",
  status: "متاح للبيع",
  lastSale: {
    clientName: "أحمد محمد علي",
    soNumber: "SO-2024-001",
    opNumber: "OP-2024-001",
    date: new Date().toISOString()
  },
  warrantyInfo: {
    status: "ساري المفعول",
    expiryDate: "2025-12-31",
    remaining: "11 شهر و 15 يوم"
  }
};

const timelineEvents = [
  {
    date: new Date().toISOString(),
    formattedDate: new Date().toLocaleDateString('ar-EG'),
    title: "استلام الجهاز",
    description: "تم استلام الجهاز من المورد",
    user: "محمد أحمد"
  }
];

// تصدير التقرير الكامل (يحل مشكلة الأحرف الغريبة)
createArabicPDFWithCanvas(
  deviceData,
  timelineEvents,
  'device_report_123456789012345',
  false // isCustomerView
);

// تصدير نسخة العميل
createArabicPDFWithCanvas(
  deviceData,
  timelineEvents,
  'customer_report_123456789012345',
  true // isCustomerView
);
```

### تصدير بيانات جدولية

```typescript
import { exportDataToPDF } from '@/lib/export-utils/html-to-pdf';

const data = [
  ['أحمد محمد', 'مطور', '5000'],
  ['سارة أحمد', 'مصممة', '4500'],
  ['علي حسن', 'محلل', '4000']
];

const headers = ['الاسم', 'المنصب', 'الراتب'];

exportDataToPDF(
  data,
  headers,
  'employees_report',
  'تقرير الموظفين'
);
```

## المميزات

### ⭐ **حل مشكلة الأحرف الغريبة**
- طريقة Canvas ترسم النصوص كصور
- لا توجد مشاكل ترميز مع الأحرف العربية
- يحل مشكلة (þ³þ þÞ þ—þŽþ-þóþ¦) نهائياً

### ✅ دعم كامل للعربية
- اتجاه من اليمين إلى اليسار (RTL)
- معالجة النصوص العربية
- تنسيق التواريخ بالعربية
- خطوط عربية واضحة

### ✅ تصدير بدون متصفح
- لا يفتح نوافذ منبثقة (طريقة Canvas)
- تنزيل مباشر للملف
- لا يعتمد على وظيفة الطباعة في المتصفح

### ✅ تنسيق احترافي
- تصميم منظم ومرتب
- ألوان متناسقة
- جداول منسقة
- معلومات منظمة في مربعات

### ✅ مرونة في الاستخدام
- دعم البيانات الجدولية
- تقارير مخصصة
- ثلاث طرق مختلفة للتصدير
- إعدادات قابلة للتخصيص

## الاختبار

يمكنك اختبار الوظائف الجديدة من خلال:

### في صفحة تتبع الجهاز (`/track`):
1. ابحث عن جهاز بالرقم التسلسلي
2. استخدم الأزرار المتاحة:
   - **🖨️ طباعة**: يفتح نافذة طباعة محسنة مع خطوط عربية واضحة
   - **📄 تصدير PDF**: ينزل ملف PDF مباشرة بدون أحرف غريبة

### في صفحة الاختبار (`/test-export`):
1. الانتقال إلى `/test-export`
2. تجربة الطرق المختلفة:
   - **Canvas (مُوصى به)**: يحل مشكلة الأحرف الغريبة نهائياً
   - **الطباعة المباشرة**: نافذة طباعة محسنة
   - **HTML**: تصدير HTML مع خطوط عربية
   - **طباعة تقليدية**: الطريقة القديمة
3. التحقق من صحة النصوص العربية والتنسيق

## المتطلبات

- `jspdf`: ^3.0.1
- `jspdf-autotable`: ^5.0.2

## ملاحظات

- الخطوط العربية تستخدم الخط الافتراضي مع تحسينات RTL
- يمكن إضافة خطوط عربية مخصصة في المستقبل
- جميع النصوص تتم معالجتها لضمان التوافق مع PDF
