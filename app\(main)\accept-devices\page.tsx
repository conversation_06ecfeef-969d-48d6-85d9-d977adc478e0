'use client';

import { useState, useEffect } from 'react';
import { useStore } from '@/context/store';
import { AcceptanceOrder, AcceptanceOrderItem, Warehouse } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { PlusCircle, Trash2, Printer, FileText, Eye } from 'lucide-react';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';

export default function AcceptDevicesPage() {
  const {
    addAcceptanceOrder,
    acceptanceOrders,
    warehouses: storeWarehouses,
    currentUser,
    updateAcceptanceOrder,
    deleteAcceptanceOrder,
  } = useStore();
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (storeWarehouses) {
      setWarehouses(storeWarehouses);
    }
  }, [storeWarehouses]);
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>('');
  const [items, setItems] = useState<AcceptanceOrderItem[]>([
    {
      deviceId: '',
      model: '',
      condition: 'جديد',
      storage: '',
      price: 0,
      notes: '',
    },
  ]);
  const [notes, setNotes] = useState<string>('');
  const [viewingOrder, setViewingOrder] = useState<AcceptanceOrder | null>(
    null
    );
  const [noteForManagement, setNoteForManagement] = useState<string>('');

  useEffect(() => {
    if (warehouses.length > 0 && !selectedWarehouse) {
      setSelectedWarehouse(String(warehouses[0].id));
    }
  }, [warehouses, selectedWarehouse]);

  const handleAddItem = () => {
    setItems([
      ...items,
      {
        deviceId: '',
        model: '',
        condition: 'جديد',
        storage: '',
        price: 0,
        notes: '',
      },
    ]);
  };

  const handleRemoveItem = (index: number) => {
    const newItems = items.filter((_, i) => i !== index);
    setItems(newItems);
  };

  const handleItemChange = (
    index: number,
    field: keyof AcceptanceOrderItem,
    value: string | number,
  ) => {
    const newItems = [...items];
    (newItems[index][field] as any) = value;
    setItems(newItems);
  };

  const {toast} = useToast();

  const handleSubmit = () => {
    if (
      !selectedWarehouse ||
      items.some(
        (item) =>
          !item.deviceId || !item.model || !item.storage || item.price <= 0,
      )
    ) {
      toast({
        title: 'خطأ',
        description: 'الرجاء تعبئة جميع الحقول المطلوبة وتحديد المخزن.',
        variant: 'destructive',
      });
      return;
    }

    const newOrder: Omit<AcceptanceOrder, 'id' | 'acceptanceId'> = {
      date: new Date().toISOString(),
      warehouseId: Number(selectedWarehouse),
      employeeName: currentUser?.name || 'غير معروف',
      items: items,
      notes: notes,
    };

    addAcceptanceOrder(newOrder);
    toast({
      title: 'تم بنجاح',
      description: 'تم قبول الأجهزة وإضافتها إلى المخزون.',
    });
    // Reset form
    setItems([
      {
        deviceId: '',
        model: '',
        condition: 'جديد',
        storage: '',
        price: 0,
        notes: '',
      },
    ]);
    setNotes('');
  };

  const exportToPdf = (order: AcceptanceOrder) => {
    const doc = new jsPDF();
    doc.setFont('Amiri-Regular'); // Assuming Amiri-Regular is added and set as default font
    doc.setFontSize(18);
    doc.text('تقرير أمر قبول أجهزة', 105, 20, { align: 'center' });
    doc.setFontSize(12);
    doc.text(`رقم الأمر: ${order.acceptanceId}`, 10, 40);
    doc.text(`التاريخ: ${new Date(order.date).toLocaleDateString()}`, 10, 47);
    doc.text(`الموظف: ${order.employeeName}`, 10, 54);
    doc.text(
      `المخزن: ${warehouses.find((w) => w.id === order.warehouseId)?.name || 'غير معروف'}`,
      10,
      61
    );
    doc.text(`ملاحظات: ${order.notes || 'لا يوجد'}`, 10, 68);

    (doc as any).autoTable({
      startY: 80,
      head: [['IMEI', 'الموديل', 'الحالة', 'السعة', 'السعر', 'ملاحظات']],
      body: (Array.isArray(order.items) ? order.items : []).map((item) => [
        item.deviceId,
        item.model,
        item.condition,
        item.storage,
        item.price,
        item.notes || '',
      ]),
      styles: { font: 'Amiri-Regular', halign: 'right' },
      headStyles: { fillColor: [200, 200, 200], textColor: [0, 0, 0] },
      alternateRowStyles: { fillColor: [240, 240, 240] },
    });

    doc.save(`AcceptanceOrder_${order.acceptanceId}.pdf`);
  };

  const exportToExcel = (order: AcceptanceOrder) => {
    const ws = XLSX.utils.json_to_sheet(
      (Array.isArray(order.items) ? order.items : []).map((item) => ({
        IMEI: item.deviceId,
        الموديل: item.model,
        الحالة: item.condition,
        السعة: item.storage,
        السعر: item.price,
        ملاحظات: item.notes || '',
      }))
    );
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Accepted Devices');
    XLSX.writeFile(wb, `AcceptanceOrder_${order.acceptanceId}.xlsx`);
  };

  const handleViewOrder = (order: AcceptanceOrder) => {
    setViewingOrder(order);
  };

  const handleEditOrder = (order: AcceptanceOrder) => {
    // Implement edit logic here, likely pre-filling the form with order data
    toast({
      title: 'تعديل أمر',
      description: `جاري تعديل الأمر ${order.acceptanceId}.`,
    });
    // For now, just set the form to edit mode
    setSelectedWarehouse(String(order.warehouseId));
    setItems(order.items);
    setNotes(order.notes || '');
    setViewingOrder(null); // Exit view mode
  };

  const handleDeleteOrder = (orderId: number) => {
    if (
      confirm(
        'هل أنت متأكد أنك تريد حذف هذا الأمر؟ سيتم حذف الأجهزة المرتبطة به من المخزون.',
      )
    ) {
      deleteAcceptanceOrder(orderId);
      toast({
        title: 'تم الحذف',
        description: 'تم حذف أمر القبول والأجهزة المرتبطة به بنجاح.',
      });
      setViewingOrder(null); // Exit view mode after deletion
    }
  };

  const handleSendNote = () => {
    if (noteForManagement.trim() === '') {
      toast({
        title: 'خطأ',
        description: 'الرجاء كتابة الملاحظة قبل الإرسال.',
        variant: 'destructive',
      });
      return;
    }
    // Here you would typically send this note to a backend or internal messaging system
    console.log(
      `Note for management regarding order ${viewingOrder?.acceptanceId}: ${noteForManagement}`
    );
    toast({
      title: 'تم الإرسال',
      description: 'تم إرسال الملاحظة إلى الإدارة.',
    });
    setNoteForManagement('');
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">قبول أجهزة جديدة في المخزن</h1>

      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        {isClient && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <Label htmlFor="warehouse">المخزن</Label>
              <Select
                onValueChange={setSelectedWarehouse}
                value={selectedWarehouse}
              >
                <SelectTrigger id="warehouse">
                  <SelectValue placeholder="اختر المخزن" />
                </SelectTrigger>
                <SelectContent>
                  {warehouses.length > 0 ? (
                    warehouses.map((warehouse) => (
                      <SelectItem
                        key={warehouse.id}
                        value={String(warehouse.id)}
                      >
                        {warehouse.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      لا توجد مستودعات متاحة
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="employeeName">الموظف</Label>
              <Input
                id="employeeName"
                type="text"
                value={currentUser?.name || 'غير معروف'}
                disabled
              />
            </div>
          </div>
        )}

        <h2 className="text-xl font-semibold mb-3">تفاصيل الأجهزة</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>IMEI</TableHead>
              <TableHead>الموديل</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>السعة</TableHead>
              <TableHead>السعر</TableHead>
              <TableHead>ملاحظات</TableHead>
              <TableHead>إجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Input
                    value={item.deviceId}
                    onChange={(e) =>
                      handleItemChange(index, 'deviceId', e.target.value)
                    }
                    placeholder="IMEI"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    value={item.model}
                    onChange={(e) =>
                      handleItemChange(index, 'model', e.target.value)
                    }
                    placeholder="الموديل"
                  />
                </TableCell>
                <TableCell>
                  <Select
                    onValueChange={(value) =>
                      handleItemChange(index, 'condition', value)
                    }
                    value={item.condition}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="جديد">جديد</SelectItem>
                      <SelectItem value="مستخدم">مستخدم</SelectItem>
                    </SelectContent>
                  </Select>
                </TableCell>
                <TableCell>
                  <Input
                    value={item.storage}
                    onChange={(e) =>
                      handleItemChange(index, 'storage', e.target.value)
                    }
                    placeholder="السعة"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    value={item.price}
                    onChange={(e) =>
                      handleItemChange(index, 'price', Number(e.target.value))
                    }
                    placeholder="السعر"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    value={item.notes}
                    onChange={(e) =>
                      handleItemChange(index, 'notes', e.target.value)
                    }
                    placeholder="ملاحظات (اختياري)"
                  />
                </TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="icon"
                    onClick={() => handleRemoveItem(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <Button onClick={handleAddItem} className="mt-4">
          <PlusCircle className="h-4 w-4 mr-2" /> إضافة جهاز
        </Button>

        <div className="mt-6">
          <Label htmlFor="orderNotes">ملاحظات عامة على الطلب</Label>
          <Textarea
            id="orderNotes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="ملاحظات إضافية على أمر القبول (اختياري)"
            rows={3}
          />
        </div>

        <Button onClick={handleSubmit} className="mt-6 w-full">
          قبول الأجهزة وإضافتها للمخزن
        </Button>
      </div>

      <h1 className="text-2xl font-bold mb-4">أوامر القبول السابقة</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        {!acceptanceOrders || acceptanceOrders.length === 0 ? (
          <p>لا توجد أوامر قبول سابقة.</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الأمر</TableHead>
                <TableHead>التاريخ</TableHead>
                <TableHead>الموظف</TableHead>
                <TableHead>المخزن</TableHead>
                <TableHead>عدد الأجهزة</TableHead>
                <TableHead>إجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {(acceptanceOrders || []).map((order) => (
                <TableRow key={order.id}>
                  <TableCell>{order.acceptanceId}</TableCell>
                  <TableCell>
                    {new Date(order.date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>{order.employeeName}</TableCell>
                  <TableCell>
                    {warehouses.find((w) => w.id === order.warehouseId)?.name ||
                      'غير معروف'}
                  </TableCell>
                  <TableCell>{(Array.isArray(order.items) ? order.items.length : 0)}</TableCell>
                  <TableCell className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleViewOrder(order)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => exportToPdf(order)}
                    >
                      <Printer className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => exportToExcel(order)}
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      {viewingOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">
              تفاصيل أمر القبول: {viewingOrder.acceptanceId}
            </h2>
            <p>
              <strong>التاريخ:</strong>{' '}
              {new Date(viewingOrder.date).toLocaleDateString()}
            </p>
            <p>
              <strong>الموظف:</strong> {viewingOrder.employeeName}
            </p>
            <p>
              <strong>المخزن:</strong>{' '}
              {warehouses.find((w) => w.id === viewingOrder.warehouseId)
                ?.name || 'غير معروف'}
            </p>
            <p>
              <strong>ملاحظات عامة:</strong> {viewingOrder.notes || 'لا يوجد'}
            </p>

            <h3 className="text-lg font-semibold mt-4 mb-2">
              الأجهزة المقبولة:
            </h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>IMEI</TableHead>
                  <TableHead>الموديل</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>السعة</TableHead>
                  <TableHead>السعر</TableHead>
                  <TableHead>ملاحظات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(Array.isArray(viewingOrder.items) ? viewingOrder.items : []).map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.deviceId}</TableCell>
                    <TableCell>{item.model}</TableCell>
                    <TableCell>{item.condition}</TableCell>
                    <TableCell>{item.storage}</TableCell>
                    <TableCell>{item.price}</TableCell>
                    <TableCell>{item.notes || ''}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="mt-4">
              <Label htmlFor="noteForManagement">ملاحظة للإدارة</Label>
              <Textarea
                id="noteForManagement"
                value={noteForManagement}
                onChange={(e) => setNoteForManagement(e.target.value)}
                placeholder="أضف ملاحظة للإدارة بخصوص هذا الطلب..."
                rows={3}
              />
              <Button onClick={handleSendNote} className="mt-2">
                إرسال الملاحظة
              </Button>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setViewingOrder(null)}>
                إغلاق
              </Button>
              <Button
                variant="secondary"
                onClick={() => handleEditOrder(viewingOrder)}
              >
                تعديل
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleDeleteOrder(viewingOrder.id)}
              >
                حذف
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
