/**
 * اختبارات نظام جلب البيانات عند الطلب
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { globalCache, staticDataCache, dynamicDataCache } from '@/lib/cache-manager';
import { fetchDevices, fetchSales, dataFetcher } from '@/lib/data-fetcher';
import { 
  extractApiQueryParams, 
  paginationToPrisma, 
  sortToPrisma, 
  searchToPrisma,
  createPaginatedResponse 
} from '@/lib/api-helpers';

// Mock API client
vi.mock('@/lib/api-client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  },
  handleApiResponse: vi.fn()
}));

describe('Cache Manager', () => {
  beforeEach(() => {
    globalCache.clear();
    staticDataCache.clear();
    dynamicDataCache.clear();
  });

  it('should store and retrieve data from cache', () => {
    const testData = { id: 1, name: 'Test Device' };
    const key = 'test-key';
    
    globalCache.set(key, testData);
    const retrieved = globalCache.get(key);
    
    expect(retrieved).toEqual(testData);
  });

  it('should respect TTL and expire data', async () => {
    const testData = { id: 1, name: 'Test Device' };
    const key = 'test-key';
    
    // Set with very short TTL
    globalCache.set(key, testData, 1); // 1ms TTL
    
    // Wait for expiration
    await new Promise(resolve => setTimeout(resolve, 10));
    
    const retrieved = globalCache.get(key);
    expect(retrieved).toBeNull();
  });

  it('should detect stale data', () => {
    const testData = { id: 1, name: 'Test Device' };
    const key = 'test-key';
    
    globalCache.set(key, testData, 100); // 100ms TTL
    
    // Data should not be stale immediately
    expect(globalCache.isStale(key)).toBe(false);
    
    // Mock time passage
    vi.useFakeTimers();
    vi.advanceTimersByTime(150);
    
    expect(globalCache.isStale(key)).toBe(true);
    
    vi.useRealTimers();
  });

  it('should provide accurate cache stats', () => {
    globalCache.set('key1', { data: 'test1' });
    globalCache.set('key2', { data: 'test2' });
    
    const stats = globalCache.getStats();
    
    expect(stats.totalEntries).toBe(2);
    expect(stats.hitRate).toBe(0); // No hits yet
  });
});

describe('API Helpers', () => {
  it('should extract query parameters correctly', () => {
    const mockRequest = {
      nextUrl: {
        searchParams: new URLSearchParams('page=2&limit=20&sort=name&direction=asc&search=test&status=active')
      }
    } as any;

    const allowedFilters = ['status', 'category'];
    const params = extractApiQueryParams(mockRequest, allowedFilters);

    expect(params.pagination).toEqual({ page: 2, limit: 20 });
    expect(params.sort).toEqual({ field: 'name', direction: 'asc' });
    expect(params.search).toEqual({ query: 'test' });
    expect(params.filters).toEqual({ status: 'active' });
  });

  it('should convert pagination to Prisma format', () => {
    const pagination = { page: 3, limit: 15 };
    const prismaParams = paginationToPrisma(pagination);

    expect(prismaParams).toEqual({
      skip: 30, // (3-1) * 15
      take: 15
    });
  });

  it('should convert sort to Prisma format', () => {
    const sort = { field: 'createdAt', direction: 'desc' as const };
    const allowedFields = ['id', 'createdAt', 'name'];
    const prismaSort = sortToPrisma(sort, allowedFields);

    expect(prismaSort).toEqual({ createdAt: 'desc' });
  });

  it('should convert search to Prisma format', () => {
    const search = { query: 'iPhone', fields: ['model', 'description'] };
    const prismaSearch = searchToPrisma(search, ['model', 'description']);

    expect(prismaSearch).toEqual({
      OR: [
        { model: { contains: 'iPhone', mode: 'insensitive' } },
        { description: { contains: 'iPhone', mode: 'insensitive' } }
      ]
    });
  });

  it('should create paginated response correctly', () => {
    const data = [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' }
    ];
    const total = 25;
    const pagination = { page: 2, limit: 10 };
    const queryParams = { pagination, sort: { field: 'name', direction: 'asc' as const } };

    const response = createPaginatedResponse(data, total, pagination, queryParams);

    expect(response.data).toEqual(data);
    expect(response.pagination).toEqual({
      page: 2,
      limit: 10,
      total: 25,
      totalPages: 3,
      hasNext: true,
      hasPrev: true
    });
    expect(response.meta.query).toEqual(queryParams);
  });
});

describe('Data Fetcher', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    dataFetcher.clearAllCache();
  });

  it('should fetch devices with caching', async () => {
    const mockDevicesData = {
      data: [{ id: '1', model: 'iPhone 13', status: 'available' }],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      meta: { cached: false, executionTime: 50 }
    };

    // Mock API response
    const { apiClient, handleApiResponse } = await import('@/lib/api-client');
    (apiClient.get as any).mockResolvedValue({ ok: true });
    (handleApiResponse as any).mockResolvedValue(mockDevicesData);

    // First call - should hit API
    const result1 = await fetchDevices({ pagination: { page: 1, limit: 10 } });
    expect(result1).toEqual(mockDevicesData);
    expect(apiClient.get).toHaveBeenCalledTimes(1);

    // Second call - should use cache
    const result2 = await fetchDevices({ pagination: { page: 1, limit: 10 } });
    expect(result2).toEqual({ ...mockDevicesData, meta: { ...mockDevicesData.meta, cached: true } });
    expect(apiClient.get).toHaveBeenCalledTimes(1); // Still 1, not called again
  });

  it('should handle force refresh', async () => {
    const mockData = {
      data: [{ id: 1, clientName: 'Test Client' }],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      meta: { cached: false, executionTime: 30 }
    };

    const { apiClient, handleApiResponse } = await import('@/lib/api-client');
    (apiClient.get as any).mockResolvedValue({ ok: true });
    (handleApiResponse as any).mockResolvedValue(mockData);

    // First call
    await fetchSales({ pagination: { page: 1, limit: 10 } });
    expect(apiClient.get).toHaveBeenCalledTimes(1);

    // Second call with force refresh
    await fetchSales({ pagination: { page: 1, limit: 10 } }, { forceRefresh: true });
    expect(apiClient.get).toHaveBeenCalledTimes(2); // Called again due to force refresh
  });

  it('should track loading states', () => {
    const cacheKey = 'test-loading-key';
    
    // Initially not loading
    expect(dataFetcher.getLoadingState(cacheKey)).toEqual({
      isLoading: false,
      isRefreshing: false,
      lastFetch: null,
      error: null
    });

    // Set loading state
    dataFetcher.setLoadingState(cacheKey, { isLoading: true });
    expect(dataFetcher.getLoadingState(cacheKey).isLoading).toBe(true);

    // Set error state
    const error = new Error('Test error');
    dataFetcher.setLoadingState(cacheKey, { isLoading: false, error });
    const state = dataFetcher.getLoadingState(cacheKey);
    expect(state.isLoading).toBe(false);
    expect(state.error).toBe(error);
  });
});

describe('Integration Tests', () => {
  beforeEach(() => {
    globalCache.clear();
    staticDataCache.clear();
    dynamicDataCache.clear();
  });

  it('should handle complete data fetching workflow', async () => {
    const mockResponse = {
      data: [
        { id: '1', model: 'iPhone 13', status: 'available', price: 3000 },
        { id: '2', model: 'Samsung S21', status: 'sold', price: 2500 }
      ],
      pagination: { page: 1, limit: 10, total: 2, totalPages: 1, hasNext: false, hasPrev: false },
      meta: { cached: false, executionTime: 75 }
    };

    const { apiClient, handleApiResponse } = await import('@/lib/api-client');
    (apiClient.get as any).mockResolvedValue({ ok: true });
    (handleApiResponse as any).mockResolvedValue(mockResponse);

    // Test with complex query parameters
    const queryParams = {
      pagination: { page: 1, limit: 10 },
      sort: { field: 'price', direction: 'desc' as const },
      search: { query: 'iPhone' },
      filters: { status: 'available', priceMin: 2000 }
    };

    const result = await fetchDevices(queryParams);

    expect(result.data).toHaveLength(2);
    expect(result.pagination.total).toBe(2);
    expect(result.meta.cached).toBe(false);

    // Verify API was called with correct parameters
    expect(apiClient.get).toHaveBeenCalledWith(
      '/api/devices?page=1&limit=10&sort=price&direction=desc&search=iPhone&status=available&priceMin=2000'
    );
  });

  it('should handle cache invalidation correctly', async () => {
    const mockData = {
      data: [{ id: 1, name: 'Test' }],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      meta: { cached: false, executionTime: 50 }
    };

    const { apiClient, handleApiResponse } = await import('@/lib/api-client');
    (apiClient.get as any).mockResolvedValue({ ok: true });
    (handleApiResponse as any).mockResolvedValue(mockData);

    const params = { pagination: { page: 1, limit: 10 } };

    // First call - populate cache
    await fetchDevices(params);
    expect(apiClient.get).toHaveBeenCalledTimes(1);

    // Second call - use cache
    await fetchDevices(params);
    expect(apiClient.get).toHaveBeenCalledTimes(1);

    // Invalidate cache
    dataFetcher.invalidateCache('/api/devices', params);

    // Third call - should hit API again
    await fetchDevices(params);
    expect(apiClient.get).toHaveBeenCalledTimes(2);
  });
});

describe('Performance Tests', () => {
  it('should handle large datasets efficiently', async () => {
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      id: i + 1,
      model: `Device ${i + 1}`,
      status: i % 2 === 0 ? 'available' : 'sold'
    }));

    const mockResponse = {
      data: largeDataset.slice(0, 50), // First page
      pagination: { page: 1, limit: 50, total: 1000, totalPages: 20, hasNext: true, hasPrev: false },
      meta: { cached: false, executionTime: 120 }
    };

    const { apiClient, handleApiResponse } = await import('@/lib/api-client');
    (apiClient.get as any).mockResolvedValue({ ok: true });
    (handleApiResponse as any).mockResolvedValue(mockResponse);

    const startTime = performance.now();
    const result = await fetchDevices({ pagination: { page: 1, limit: 50 } });
    const endTime = performance.now();

    expect(result.data).toHaveLength(50);
    expect(result.pagination.total).toBe(1000);
    expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
  });

  it('should manage memory efficiently with cache limits', () => {
    // Fill cache with many entries
    for (let i = 0; i < 1000; i++) {
      globalCache.set(`key-${i}`, { data: `value-${i}` });
    }

    const stats = globalCache.getStats();
    
    // Cache should have reasonable limits
    expect(stats.totalEntries).toBeLessThanOrEqual(500); // Assuming LRU eviction
    expect(stats.memoryUsage).toBeLessThan(10 * 1024 * 1024); // Less than 10MB
  });
});
