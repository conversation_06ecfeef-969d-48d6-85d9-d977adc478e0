#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixSupplyNumberGeneration() {
  try {
    console.log('🔧 إصلاح مشكلة توليد رقم أمر التوريد...');

    // 1. فحص أوامر التوريد الموجودة
    console.log('\n📋 فحص أوامر التوريد الموجودة...');
    
    const supplyOrders = await prisma.supplyOrder.findMany({
      orderBy: { id: 'asc' }
    });
    
    console.log(`✅ تم العثور على ${supplyOrders.length} أمر توريد`);
    
    if (supplyOrders.length > 0) {
      console.log('\n📋 الأوامر الموجودة:');
      supplyOrders.forEach(order => {
        console.log(`- ID: ${order.id}, رقم الأمر: ${order.supplyOrderId}`);
      });
    }

    // 2. اختبار دالة توليد الأرقام يدوياً
    console.log('\n🧪 اختبار دالة توليد الأرقام...');
    
    try {
      const lastOrder = await prisma.supplyOrder.findFirst({
        where: {
          supplyOrderId: {
            startsWith: 'SUP-'
          }
        },
        orderBy: {
          supplyOrderId: 'desc'
        }
      });

      console.log('📋 آخر أمر توريد يبدأ بـ SUP-:', lastOrder);

      let nextNumber = 1;
      if (lastOrder && lastOrder.supplyOrderId) {
        // استخراج الرقم من نهاية السلسلة
        const match = lastOrder.supplyOrderId.match(/SUP-(\d+)$/);
        if (match) {
          const currentNumber = parseInt(match[1]);
          if (!isNaN(currentNumber)) {
            nextNumber = currentNumber + 1;
          }
        }
      }

      const newSupplyNumber = `SUP-${nextNumber}`;
      console.log(`✅ الرقم التالي المقترح: ${newSupplyNumber}`);

      // 3. التحقق من عدم وجود الرقم
      const existingOrder = await prisma.supplyOrder.findUnique({
        where: { supplyOrderId: newSupplyNumber }
      });

      if (existingOrder) {
        console.log('⚠️ الرقم موجود بالفعل!');
      } else {
        console.log('✅ الرقم متاح للاستخدام');
      }

    } catch (error) {
      console.error('❌ خطأ في اختبار توليد الأرقام:', error);
    }

    // 4. إنشاء دالة توليد محسّنة
    console.log('\n🔧 إنشاء دالة توليد محسّنة...');
    
    const generateSupplyNumber = async () => {
      try {
        // البحث عن آخر رقم صحيح
        const allOrders = await prisma.supplyOrder.findMany({
          select: { supplyOrderId: true },
          orderBy: { id: 'desc' }
        });

        let maxNumber = 0;

        allOrders.forEach(order => {
          const match = order.supplyOrderId.match(/SUP-(\d+)$/);
          if (match) {
            const num = parseInt(match[1]);
            if (!isNaN(num) && num > maxNumber) {
              maxNumber = num;
            }
          }
        });

        return `SUP-${maxNumber + 1}`;
      } catch (error) {
        console.error('خطأ في توليد رقم أمر التوريد:', error);
        throw error;
      }
    };

    const newNumber = await generateSupplyNumber();
    console.log(`✅ الرقم الجديد المولد: ${newNumber}`);

    // 5. اختبار إنشاء أمر تجريبي
    console.log('\n🧪 اختبار إنشاء أمر تجريبي...');
    
    try {
      const testOrder = await prisma.supplyOrder.create({
        data: {
          supplyOrderId: newNumber,
          supplierId: 1,
          supplyDate: new Date().toISOString().split('T')[0],
          warehouseId: 1,
          employeeName: 'اختبار النظام',
          notes: 'أمر تجريبي لاختبار توليد الأرقام',
          status: 'completed'
        }
      });

      console.log(`✅ تم إنشاء أمر تجريبي: ${testOrder.supplyOrderId} (ID: ${testOrder.id})`);

      // حذف الأمر التجريبي
      await prisma.supplyOrder.delete({
        where: { id: testOrder.id }
      });

      console.log('✅ تم حذف الأمر التجريبي');

    } catch (error) {
      console.error('❌ خطأ في إنشاء الأمر التجريبي:', error);
    }

    console.log('\n✅ تم الانتهاء من إصلاح مشكلة توليد أرقام أوامر التوريد');

  } catch (error) {
    console.error('❌ خطأ عام في إصلاح النظام:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixSupplyNumberGeneration();
