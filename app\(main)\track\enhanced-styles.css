/* تحسينات مظهر صفحة تتبع الجهاز */

/* تحسينات بطاقة البحث */
.search-card {
  position: relative;
  overflow: hidden;
}

.search-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* تحسينات حاوية البحث */
.search-container {
  position: relative;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تأثيرات الإدخال المحسنة */
.search-container input:focus {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
}

/* تحسينات الأزرار */
.search-container button:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.search-container button:active {
  transform: scale(0.98);
}

/* تحسينات الخط الزمني */
.enhanced-timeline {
  position: relative;
  padding-right: 2rem;
}

.enhanced-timeline::before {
  content: '';
  position: absolute;
  right: 1rem;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, 
    #3b82f6 0%, 
    #6366f1 25%, 
    #8b5cf6 50%, 
    #ec4899 75%, 
    #10b981 100%
  );
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

/* تحسينات عناصر الخط الزمني */
.enhanced-timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-right: 3.5rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.enhanced-timeline-item:hover {
  transform: translateX(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.enhanced-timeline-item::before {
  content: '';
  position: absolute;
  right: -0.75rem;
  top: 1.5rem;
  width: 16px;
  height: 16px;
  background: var(--timeline-color, #3b82f6);
  border: 4px solid white;
  border-radius: 50%;
  box-shadow: 0 0 0 3px var(--timeline-color, #3b82f6), 0 4px 10px rgba(0, 0, 0, 0.1);
  z-index: 2;
  transition: all 0.3s ease;
}

.enhanced-timeline-item:hover::before {
  transform: scale(1.2);
  box-shadow: 0 0 0 6px var(--timeline-color, #3b82f6), 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* ألوان مختلفة لأنواع الأحداث */
.timeline-supply { --timeline-color: #06b6d4; }
.timeline-evaluation { --timeline-color: #6366f1; }
.timeline-maintenance { --timeline-color: #f59e0b; }
.timeline-transfer { --timeline-color: #6b7280; }
.timeline-sale { --timeline-color: #10b981; }
.timeline-return { --timeline-color: #ef4444; }
.timeline-replacement { --timeline-color: #8b5cf6; }

/* تحسينات البطاقات */
.enhanced-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.enhanced-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--card-accent, #3b82f6), var(--card-accent-end, #6366f1));
}

.enhanced-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
}

/* ألوان مختلفة للبطاقات */
.card-sale { --card-accent: #10b981; --card-accent-end: #059669; }
.card-warranty { --card-accent: #3b82f6; --card-accent-end: #1d4ed8; }
.card-replacement { --card-accent: #8b5cf6; --card-accent-end: #7c3aed; }
.card-device-info { --card-accent: #6366f1; --card-accent-end: #4f46e5; }

/* تحسينات نسخة العميل */
.customer-view-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
}

/* تحسينات معلومات الضمان */
.warranty-active {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border: 2px solid #10b981;
  color: #065f46;
}

.warranty-expired {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 2px solid #ef4444;
  color: #991b1b;
}

.warranty-none {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 2px solid #6b7280;
  color: #374151;
}

/* تأثيرات الأيقونات */
.icon-bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* تحسينات الأزرار */
.action-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-button:hover::before {
  left: 100%;
}

/* تحسينات التصميم المتجاوب */
@media (max-width: 768px) {
  .search-container {
    padding: 0.75rem;
  }
  
  .search-container .flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .enhanced-timeline-item {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .enhanced-timeline-item:hover {
    transform: translateX(-4px);
  }
  
  .customer-view-enhanced {
    padding: 1rem;
    border-radius: 1rem;
  }
}

/* تأثيرات التحميل */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer-loading 1.5s infinite;
}

@keyframes shimmer-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* تحسينات الطباعة */
@media print {
  .enhanced-timeline-item {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
  }
  
  .enhanced-card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
  }
}
