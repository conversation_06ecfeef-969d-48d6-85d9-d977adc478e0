#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkDatabaseStatus() {
  try {
    console.log('🔍 فحص حالة قاعدة البيانات والاتصالات...');
    console.log('═'.repeat(50));

    // 1. فحص متغيرات البيئة أولاً
    console.log('\n🌍 فحص متغيرات البيئة...');
    
    const databaseUrl = process.env.DATABASE_URL;
    console.log(`📊 DATABASE_URL: ${databaseUrl}`);
    
    if (databaseUrl) {
      try {
        const url = new URL(databaseUrl);
        console.log(`✅ تفاصيل الاتصال:`);
        console.log(`   - الخادم: ${url.hostname}:${url.port}`);
        console.log(`   - المستخدم: ${url.username}`);
        console.log(`   - قاعدة البيانات: ${url.pathname.slice(1)}`);
      } catch (error) {
        console.log('❌ رابط قاعدة البيانات غير صحيح');
      }
    }

    // 2. فحص الاتصال
    console.log('\n📊 فحص الاتصال بقاعدة البيانات...');
    
    try {
      await prisma.$connect();
      console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
      
      // الحصول على معلومات قاعدة البيانات
      const dbInfo = await prisma.$queryRaw`SELECT current_database() as db_name, current_user as user_name, version() as version`;
      console.log('📋 معلومات قاعدة البيانات:', dbInfo);
      
    } catch (error) {
      console.error('❌ فشل الاتصال بقاعدة البيانات:', error);
      return;
    }

    // 3. فحص أوامر التوريد والأجهزة
    console.log('\n📦 فحص أوامر التوريد والأجهزة...');
    
    try {
      // عدد أوامر التوريد
      const supplyOrdersCount = await prisma.supplyOrder.count();
      console.log(`📦 إجمالي أوامر التوريد: ${supplyOrdersCount}`);
      
      // عدد الأجهزة
      const devicesCount = await prisma.device.count();
      console.log(`📱 إجمالي الأجهزة: ${devicesCount}`);
      
      // عدد المخازن
      const warehousesCount = await prisma.warehouse.count();
      console.log(`🏢 إجمالي المخازن: ${warehousesCount}`);
      
      // آخر أمر توريد
      const lastSupplyOrder = await prisma.supplyOrder.findFirst({
        orderBy: { id: 'desc' },
        include: { items: true }
      });
      
      if (lastSupplyOrder) {
        console.log(`📦 آخر أمر توريد: ${lastSupplyOrder.supplyOrderId} (عدد العناصر: ${lastSupplyOrder.items.length})`);
      }
      
    } catch (error) {
      console.error('❌ خطأ في جلب البيانات:', error);
    }

    // 4. فحص جدول اتصالات قواعد البيانات
    console.log('\n🔗 فحص جدول اتصالات قواعد البيانات...');
    
    try {
      const connections = await prisma.databaseConnection.findMany();
      console.log(`🔗 عدد الاتصالات المسجلة: ${connections.length}`);
      
      if (connections.length === 0) {
        console.log('ℹ️ لا توجد اتصالات مسجلة، سيتم إنشاء الاتصال الافتراضي...');
        
        // إنشاء الاتصال الافتراضي
        const defaultConnection = await prisma.databaseConnection.create({
          data: {
            name: 'الاتصال الرئيسي',
            host: 'localhost',
            port: 5432,
            database: 'deviceflow_db',
            username: 'postgres',
            password: 'postgres123',
            isActive: true,
            isDefault: true
          }
        });
        
        console.log(`✅ تم إنشاء الاتصال الافتراضي (ID: ${defaultConnection.id})`);
      } else {
        console.log('📋 الاتصالات الموجودة:');
        connections.forEach(conn => {
          console.log(`  - ${conn.name}: ${conn.host}:${conn.port}/${conn.database} (نشط: ${conn.isActive})`);
        });
      }
      
    } catch (error: any) {
      console.log('ℹ️ جدول اتصالات قواعد البيانات غير متاح:', error.message);
    }

    // 5. اختبار API
    console.log('\n🌐 اختبار API...');
    
    try {
      const response = await fetch('http://localhost:9005/api/supply', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${Buffer.from('user:admin:admin').toString('base64')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ API أوامر التوريد يعمل، عدد الأوامر: ${data.data?.length || data.length || 0}`);
      } else {
        console.log(`❌ API لا يعمل، رمز الخطأ: ${response.status}`);
      }
    } catch (error) {
      console.log('⚠️ لا يمكن الوصول إلى API (تأكد من تشغيل الخادم)');
    }

    console.log('\n═'.repeat(50));
    console.log('✅ تم الانتهاء من فحص حالة قاعدة البيانات');

  } catch (error) {
    console.error('❌ خطأ عام:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseStatus();
