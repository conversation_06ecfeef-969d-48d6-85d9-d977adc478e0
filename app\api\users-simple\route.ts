import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع المستخدمين
export async function GET() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        role: true,
        phone: true,
        photo: true,
        status: true,
        lastLogin: true,
        branchLocation: true,
        permissions: true,
        warehouseAccess: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: { name: 'asc' }
    });

    // تحويل حقول JSON من strings إلى objects
    const processedUsers = users.map((user: any) => ({
      ...user,
      permissions: user.permissions ?
        (typeof user.permissions === 'string' ?
          JSON.parse(user.permissions) : user.permissions) : null,
      warehouseAccess: user.warehouseAccess ?
        (typeof user.warehouseAccess === 'string' ?
          JSON.parse(user.warehouseAccess) : user.warehouseAccess) : null,
    }));

    return NextResponse.json(processedUsers);
  } catch (error) {
    console.error('خطأ في جلب المستخدمين:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المستخدمين' },
      { status: 500 }
    );
  }
}

// POST - إنشاء مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, username, email, role, phone, photo, status, branchLocation, permissions, warehouseAccess } = body;

    // التحقق من البيانات المطلوبة
    if (!name || !username || !email) {
      return NextResponse.json(
        { error: 'الاسم واسم المستخدم والبريد الإلكتروني مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    const existingUserByEmail = await prisma.user.findFirst({
      where: { email }
    });

    if (existingUserByEmail) {
      return NextResponse.json(
        { error: 'يوجد مستخدم بهذا البريد الإلكتروني بالفعل' },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار اسم المستخدم
    const existingUserByUsername = await prisma.user.findFirst({
      where: { username }
    });

    if (existingUserByUsername) {
      return NextResponse.json(
        { error: 'يوجد مستخدم بهذا الاسم بالفعل' },
        { status: 400 }
      );
    }

    const user = await prisma.user.create({
      data: {
        name,
        username,
        email,
        role: role || 'user',
        phone: phone || '',
        photo: photo || '',
        status: status || 'Active',
        branchLocation: branchLocation || null,
        warehouseAccess: warehouseAccess ? JSON.stringify(warehouseAccess) : null,
        permissions: permissions ? JSON.stringify(permissions) : null,
        lastLogin: new Date().toISOString()
      }
    });

    // معالجة حقول JSON قبل الإرسال
    const processedUser = {
      ...user,
      permissions: user.permissions ?
        (typeof user.permissions === 'string' ?
          JSON.parse(user.permissions) : user.permissions) : null,
      warehouseAccess: user.warehouseAccess ?
        (typeof user.warehouseAccess === 'string' ?
          JSON.parse(user.warehouseAccess) : user.warehouseAccess) : null,
    };

    return NextResponse.json(processedUser, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء المستخدم:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء المستخدم' },
      { status: 500 }
    );
  }
}

// PUT - تحديث مستخدم
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, username, email, role, phone, photo, status, branchLocation, permissions, warehouseAccess } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من عدم تكرار البريد الإلكتروني (إذا تم تغييره)
    if (email && email !== existingUser.email) {
      const duplicateEmail = await prisma.user.findFirst({
        where: { 
          email,
          id: { not: parseInt(id) }
        }
      });

      if (duplicateEmail) {
        return NextResponse.json(
          { error: 'يوجد مستخدم آخر بهذا البريد الإلكتروني' },
          { status: 400 }
        );
      }
    }

    // التحقق من عدم تكرار اسم المستخدم (إذا تم تغييره)
    if (username && username !== existingUser.username) {
      const duplicateUsername = await prisma.user.findFirst({
        where: { 
          username,
          id: { not: parseInt(id) }
        }
      });

      if (duplicateUsername) {
        return NextResponse.json(
          { error: 'يوجد مستخدم آخر بهذا الاسم' },
          { status: 400 }
        );
      }
    }

    const user = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        ...(name && { name }),
        ...(username !== undefined && { username }),
        ...(email !== undefined && { email }),
        ...(role !== undefined && { role }),
        ...(phone !== undefined && { phone }),
        ...(photo !== undefined && { photo }),
        ...(status !== undefined && { status }),
        ...(branchLocation !== undefined && { branchLocation }),
        ...(warehouseAccess !== undefined && { 
          warehouseAccess: warehouseAccess ? JSON.stringify(warehouseAccess) : null 
        }),
        ...(permissions !== undefined && { 
          permissions: permissions ? JSON.stringify(permissions) : null 
        })
      }
    });

    // معالجة حقول JSON قبل الإرسال
    const processedUser = {
      ...user,
      permissions: user.permissions ?
        (typeof user.permissions === 'string' ?
          JSON.parse(user.permissions) : user.permissions) : null,
      warehouseAccess: user.warehouseAccess ?
        (typeof user.warehouseAccess === 'string' ?
          JSON.parse(user.warehouseAccess) : user.warehouseAccess) : null,
    };

    return NextResponse.json(processedUser);
  } catch (error) {
    console.error('خطأ في تحديث المستخدم:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث المستخدم' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مستخدم
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    // منع حذف المستخدم الأدمن الرئيسي
    if (parseInt(id) === 1 || existingUser.role === 'admin') {
      return NextResponse.json(
        { error: 'لا يمكن حذف المستخدم الأدمن' },
        { status: 400 }
      );
    }

    // 🔒 فحص العمليات المرتبطة بالمستخدم
    const relatedOperations = {
      sales: 0,
      supply: 0,
      maintenance: 0,
      returns: 0,
      evaluations: 0,
      auditLogs: 0
    };

    // فحص المبيعات المنشأة بواسطة المستخدم
    try {
      relatedOperations.sales = await prisma.sale.count({
        where: { employeeName: existingUser.name || existingUser.username || '' }
      });
    } catch (error) {
      console.warn('Error checking user sales:', error);
    }

    // فحص أوامر التوريد المنشأة بواسطة المستخدم
    try {
      relatedOperations.supply = await prisma.supplyOrder.count({
        where: { employeeName: existingUser.name || existingUser.username || '' }
      });
    } catch (error) {
      console.warn('Error checking user supply orders:', error);
    }

    // فحص أوامر الصيانة المرتبطة بالمستخدم
    try {
      relatedOperations.maintenance = await prisma.maintenanceOrder.count({
        where: { employeeName: existingUser.name || existingUser.username || '' }
      });
    } catch (error) {
      console.warn('Error checking user maintenance orders:', error);
    }

    // فحص عمليات الإرجاع المنشأة بواسطة المستخدم
    try {
      relatedOperations.returns = await prisma.return.count({
        where: { employeeName: existingUser.name || existingUser.username || '' }
      });
    } catch (error) {
      console.warn('Error checking user returns:', error);
    }

    // فحص أوامر التقييم المرتبطة بالمستخدم
    try {
      relatedOperations.evaluations = await prisma.evaluationOrder.count({
        where: { employeeName: existingUser.name || existingUser.username || '' }
      });
    } catch (error) {
      console.warn('Error checking user evaluations:', error);
    }

    // فحص سجلات التدقيق المرتبطة بالمستخدم
    try {
      relatedOperations.auditLogs = await prisma.auditLog.count({
        where: { userId: parseInt(id) }
      });
    } catch (error) {
      console.warn('Error checking user audit logs:', error);
    }

    // التحقق من وجود عمليات مرتبطة
    const totalOperations = Object.values(relatedOperations).reduce((sum, count) => sum + count, 0);

    if (totalOperations > 0) {
      const operationMessages = [];
      if (relatedOperations.sales > 0) operationMessages.push(`${relatedOperations.sales} عملية بيع`);
      if (relatedOperations.supply > 0) operationMessages.push(`${relatedOperations.supply} أمر توريد`);
      if (relatedOperations.maintenance > 0) operationMessages.push(`${relatedOperations.maintenance} أمر صيانة`);
      if (relatedOperations.returns > 0) operationMessages.push(`${relatedOperations.returns} عملية إرجاع`);
      if (relatedOperations.evaluations > 0) operationMessages.push(`${relatedOperations.evaluations} أمر تقييم`);
      if (relatedOperations.auditLogs > 0) operationMessages.push(`${relatedOperations.auditLogs} سجل تدقيق`);

      return NextResponse.json({
        error: 'لا يمكن حذف المستخدم',
        reason: 'المستخدم مرتبط بعمليات في النظام',
        relatedOperations,
        details: `المستخدم مرتبط بـ: ${operationMessages.join('، ')}`,
        suggestion: 'يمكنك إلغاء تفعيل المستخدم بدلاً من حذفه عن طريق تغيير حالته إلى "غير نشط"',
        userInfo: {
          id: existingUser.id,
          name: existingUser.name,
          username: existingUser.username,
          role: existingUser.role,
          status: existingUser.status
        }
      }, { status: 400 });
    }

    await prisma.user.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المستخدم:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المستخدم' },
      { status: 500 }
    );
  }
}
