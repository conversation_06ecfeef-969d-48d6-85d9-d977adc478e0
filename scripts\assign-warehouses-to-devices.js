const { default: fetch } = require('node-fetch');

async function assignWarehousesToDevices() {
  try {
    console.log('🏪 Assigning warehouses to devices...\n');
    
    // 1. Get all warehouses
    console.log('1️⃣ Fetching warehouses...');
    const warehousesResponse = await fetch('http://localhost:9005/api/warehouses-simple');
    
    if (!warehousesResponse.ok) {
      console.log('❌ Failed to fetch warehouses');
      return;
    }
    
    const warehousesResult = await warehousesResponse.json();
    const warehouses = warehousesResult.data || warehousesResult;
    console.log(`✅ Found ${warehouses.length} warehouses`);
    
    // 2. Get all devices
    console.log('\n2️⃣ Fetching devices...');
    const devicesResponse = await fetch('http://localhost:9005/api/devices-simple');
    
    if (!devicesResponse.ok) {
      console.log('❌ Failed to fetch devices');
      return;
    }
    
    const devicesResult = await devicesResponse.json();
    const devices = devicesResult.data || devicesResult;
    console.log(`✅ Found ${devices.length} devices`);
    
    // 3. Find devices without warehouses
    const devicesWithoutWarehouses = devices.filter(d => !d.warehouseId);
    console.log(`\n📦 Devices without warehouses: ${devicesWithoutWarehouses.length}`);
    
    if (devicesWithoutWarehouses.length === 0) {
      console.log('✅ All devices already have warehouses assigned!');
      return;
    }
    
    // 4. Assign warehouses to devices
    console.log('\n3️⃣ Assigning warehouses to devices...');
    
    // Find main warehouse first
    const mainWarehouse = warehouses.find(w => w.type === 'رئيسي');
    const defaultWarehouse = mainWarehouse || warehouses[0];
    
    if (!defaultWarehouse) {
      console.log('❌ No warehouses available for assignment');
      return;
    }
    
    console.log(`Using warehouse: ${defaultWarehouse.name} (ID: ${defaultWarehouse.id})`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const device of devicesWithoutWarehouses) {
      try {
        console.log(`Assigning warehouse to device ${device.id}...`);
        
        const updateResponse = await fetch('http://localhost:9005/api/devices-simple', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id: device.id,
            warehouseId: defaultWarehouse.id
          })
        });
        
        if (updateResponse.ok) {
          console.log(`✅ Device ${device.id} assigned to warehouse ${defaultWarehouse.name}`);
          successCount++;
        } else {
          const error = await updateResponse.text();
          console.log(`❌ Failed to assign warehouse to device ${device.id}: ${error}`);
          errorCount++;
        }
        
        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.log(`❌ Error assigning warehouse to device ${device.id}:`, error.message);
        errorCount++;
      }
    }
    
    // 5. Verify assignments
    console.log('\n4️⃣ Verifying assignments...');
    const verifyResponse = await fetch('http://localhost:9005/api/devices-simple');
    
    if (verifyResponse.ok) {
      const verifyResult = await verifyResponse.json();
      const updatedDevices = verifyResult.data || verifyResult;
      
      const devicesWithWarehouses = updatedDevices.filter(d => d.warehouseId);
      const devicesStillWithoutWarehouses = updatedDevices.filter(d => !d.warehouseId);
      
      console.log(`✅ Devices with warehouses: ${devicesWithWarehouses.length}`);
      console.log(`⚠️ Devices still without warehouses: ${devicesStillWithoutWarehouses.length}`);
      
      // Show warehouse distribution
      console.log('\n📊 Warehouse distribution:');
      warehouses.forEach(warehouse => {
        const warehouseDevices = updatedDevices.filter(d => d.warehouseId === warehouse.id);
        if (warehouseDevices.length > 0) {
          console.log(`   - ${warehouse.name}: ${warehouseDevices.length} devices`);
        }
      });
    }
    
    console.log('\n🎉 Warehouse assignment completed!');
    console.log(`✅ Successfully assigned: ${successCount}`);
    console.log(`❌ Failed assignments: ${errorCount}`);
    
  } catch (error) {
    console.error('❌ Error during warehouse assignment:', error);
  }
}

assignWarehousesToDevices();
