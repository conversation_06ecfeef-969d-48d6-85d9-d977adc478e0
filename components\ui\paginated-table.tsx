"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, Filter, RefreshCw, ChevronLeft, ChevronRight } from 'lucide-react';
import { ApiQueryParams, PaginatedResponse } from '@/lib/types';

interface Column<T> {
  key: string;
  title: string;
  render?: (item: T, index: number) => React.ReactNode;
  sortable?: boolean;
  searchable?: boolean;
  width?: string;
}

interface PaginatedTableProps<T> {
  title?: string;
  description?: string;
  columns: Column<T>[];
  fetchData: (params: ApiQueryParams) => Promise<PaginatedResponse<T>>;
  searchPlaceholder?: string;
  defaultPageSize?: number;
  defaultSort?: { field: string; direction: 'asc' | 'desc' };
  filters?: React.ReactNode;
  actions?: React.ReactNode;
  onRowClick?: (item: T) => void;
  className?: string;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
}

export function PaginatedTable<T extends { id: string | number }>({
  title,
  description,
  columns,
  fetchData,
  searchPlaceholder = "البحث...",
  defaultPageSize = 10,
  defaultSort = { field: 'id', direction: 'desc' },
  filters,
  actions,
  onRowClick,
  className = "",
  emptyMessage = "لا توجد بيانات للعرض",
  emptyIcon
}: PaginatedTableProps<T>) {
  // State management
  const [data, setData] = useState<PaginatedResponse<T> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [sortField, setSortField] = useState(defaultSort.field);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(defaultSort.direction);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page on search
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch data function
  const loadData = useCallback(async (forceRefresh = false) => {
    setIsLoading(true);
    
    try {
      const queryParams: ApiQueryParams = {
        pagination: {
          page: currentPage,
          limit: pageSize
        },
        sort: {
          field: sortField,
          direction: sortDirection
        },
        search: debouncedSearchTerm ? {
          query: debouncedSearchTerm
        } : undefined
      };

      const result = await fetchData(queryParams);
      setData(result);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchData, currentPage, pageSize, sortField, sortDirection, debouncedSearchTerm]);

  // Load data on parameter changes
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Handle page size change
  const handlePageSizeChange = (newSize: string) => {
    setPageSize(Number(newSize));
    setCurrentPage(1);
  };

  // Render loading state
  if (isLoading && !data) {
    return (
      <Card className={className}>
        {title && (
          <CardHeader>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
        )}
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-muted-foreground">جاري تحميل البيانات...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {title && (
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{title}</CardTitle>
              {description && <CardDescription>{description}</CardDescription>}
            </div>
            {actions && <div className="flex gap-2">{actions}</div>}
          </div>
        </CardHeader>
      )}
      
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          {filters && (
            <div className="flex gap-2">
              {filters}
            </div>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadData(true)}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin ml-2" />
            ) : (
              <RefreshCw className="h-4 w-4 ml-2" />
            )}
            تحديث
          </Button>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead
                    key={column.key}
                    className={`${column.width || ''} ${column.sortable ? 'cursor-pointer hover:bg-muted/50' : ''}`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-2">
                      {column.title}
                      {column.sortable && sortField === column.key && (
                        <span className="text-xs">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {!data?.data || data.data.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-32 text-center">
                    <div className="flex flex-col items-center space-y-3 text-muted-foreground">
                      {emptyIcon || <Search className="h-12 w-12" />}
                      <p className="text-lg font-medium">{emptyMessage}</p>
                      {debouncedSearchTerm && (
                        <p className="text-sm">لا توجد نتائج للبحث عن "{debouncedSearchTerm}"</p>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                data.data.map((item, index) => (
                  <TableRow
                    key={item.id}
                    className={onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}
                    onClick={() => onRowClick?.(item)}
                  >
                    {columns.map((column) => (
                      <TableCell key={column.key}>
                        {column.render ? column.render(item, index) : (item as any)[column.key]}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {data && data.pagination.total > 0 && (
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>
                عرض {((data.pagination.page - 1) * data.pagination.limit) + 1} إلى{' '}
                {Math.min(data.pagination.page * data.pagination.limit, data.pagination.total)} من{' '}
                {data.pagination.total} عنصر
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 عناصر</SelectItem>
                  <SelectItem value="10">10 عناصر</SelectItem>
                  <SelectItem value="20">20 عنصر</SelectItem>
                  <SelectItem value="50">50 عنصر</SelectItem>
                  <SelectItem value="100">100 عنصر</SelectItem>
                </SelectContent>
              </Select>
              
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!data.pagination.hasPrev || isLoading}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                
                <div className="flex items-center px-3 py-1 text-sm">
                  {data.pagination.page} / {data.pagination.totalPages}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!data.pagination.hasNext || isLoading}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Loading overlay */}
        {isLoading && data && (
          <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
            <div className="flex items-center gap-2 bg-background border rounded-lg px-4 py-2 shadow-lg">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">جاري التحديث...</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
