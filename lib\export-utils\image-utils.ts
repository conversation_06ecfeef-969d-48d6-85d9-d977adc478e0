'use client';

/**
 * Utility function to load and convert image to base64
 */
export async function loadImageAsBase64(imageUrl: string): Promise<string> {
  // If already base64, return as is
  if (imageUrl.startsWith('data:')) {
    return imageUrl;
  }

  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to convert image to base64'));
        }
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error: any) {
    throw new Error(`Failed to load image: ${error?.message || 'Unknown error'}`);
  }
}

/**
 * Utility function to load image element
 */
export async function loadImage(imageUrl: string): Promise<HTMLImageElement> {
  const base64Url = await loadImageAsBase64(imageUrl);
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = base64Url;
  });
}

/**
 * Function to safely draw image to canvas
 */
export async function drawImageToCanvas(
  ctx: CanvasRenderingContext2D,
  imageUrl: string,
  x: number,
  y: number,
  width: number,
  height: number
): Promise<void> {
  try {
    const img = await loadImage(imageUrl);
    ctx.drawImage(img, x, y, width, height);
  } catch (error) {
    console.warn('Failed to draw image:', error);
    throw error;
  }
}
