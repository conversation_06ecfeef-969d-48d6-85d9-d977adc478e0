import fetch from 'node-fetch';

// اختبار API endpoints للتأكد من البيانات
async function testApiEndpoints() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🔍 اختبار API endpoints...\n');

  try {
    // 1. اختبار endpoint الأجهزة
    console.log('1. اختبار /api/devices-simple');
    const devicesResponse = await fetch(`${baseUrl}/api/devices-simple`);
    const devicesData = await devicesResponse.json();
    
    console.log(`   الحالة: ${devicesResponse.status}`);
    console.log(`   عدد الأجهزة: ${devicesData.data?.length || 0}`);
    
    if (devicesData.data && devicesData.data.length > 0) {
      console.log('   الأجهزة الموجودة:');
      devicesData.data.forEach((device, index) => {
        console.log(`   ${index + 1}. ${device.id} - ${device.model} (${device.status})`);
      });
    }

    // 2. اختبار endpoint أوامر التوريد
    console.log('\n2. اختبار /api/supply');
    const supplyResponse = await fetch(`${baseUrl}/api/supply`);
    const supplyData = await supplyResponse.json();
    
    console.log(`   الحالة: ${supplyResponse.status}`);
    console.log(`   عدد أوامر التوريد: ${supplyData.data?.length || 0}`);
    
    if (supplyData.data && supplyData.data.length > 0) {
      console.log('   أوامر التوريد الموجودة:');
      supplyData.data.forEach((order, index) => {
        console.log(`   ${index + 1}. ${order.supplyOrderId} - ${order.status}`);
      });
    }

    // 3. اختبار endpoint المبيعات
    console.log('\n3. اختبار /api/sales');
    const salesResponse = await fetch(`${baseUrl}/api/sales`);
    const salesData = await salesResponse.json();
    
    console.log(`   الحالة: ${salesResponse.status}`);
    console.log(`   عدد المبيعات: ${salesData.data?.length || 0}`);

    // 4. اختبار endpoint المخازن
    console.log('\n4. اختبار /api/warehouses-simple');
    const warehousesResponse = await fetch(`${baseUrl}/api/warehouses-simple`);
    const warehousesData = await warehousesResponse.json();
    
    console.log(`   الحالة: ${warehousesResponse.status}`);
    console.log(`   عدد المخازن: ${warehousesData.data?.length || 0}`);

    // 5. اختبار endpoint الموردين
    console.log('\n5. اختبار /api/suppliers');
    const suppliersResponse = await fetch(`${baseUrl}/api/suppliers`);
    const suppliersData = await suppliersResponse.json();
    
    console.log(`   الحالة: ${suppliersResponse.status}`);
    console.log(`   عدد الموردين: ${suppliersData.data?.length || 0}`);

    console.log('\n✅ جميع API endpoints تعمل وتُرجع البيانات من قاعدة البيانات الصحيحة');
    console.log('\n💡 إذا كانت الواجهة تُظهر بيانات مختلفة، فالمشكلة في:');
    console.log('   - Cache المتصفح');
    console.log('   - LocalStorage أو SessionStorage');
    console.log('   - عدم تحديث الواجهة بعد تغيير البيانات');
    console.log('\n🔧 الحلول المقترحة:');
    console.log('   1. مسح cache المتصفح (Ctrl+Shift+R)');
    console.log('   2. فتح الموقع في نافذة خاصة/مجهولة');
    console.log('   3. فحص Network tab في Developer Tools');
    console.log('   4. تشغيل السكربت clear-frontend-cache.js في المتصفح');

  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n🚨 الخادم غير يعمل! تأكد من تشغيل الخادم بـ:');
      console.log('   npm run dev');
      console.log('   أو');
      console.log('   yarn dev');
    }
  }
}

testApiEndpoints();
