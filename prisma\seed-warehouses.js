const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  // تحقق من وجود مخازن
  const warehouseCount = await prisma.warehouse.count();
  
  if (warehouseCount === 0) {
    console.log('لا يوجد مخازن. سيتم إنشاء مخزن افتراضي...');
    
    // إنشاء مخزن افتراضي
    await prisma.warehouse.create({
      data: {
        name: 'المخزن الرئيسي',
        type: 'رئيسي',
        location: 'المقر الرئيسي'
      }
    });
    
    await prisma.warehouse.create({
      data: {
        name: 'مخزن الصيانة',
        type: 'فرعي',
        location: 'قسم الصيانة'
      }
    });
    
    console.log('تم إنشاء مخازن افتراضية بنجاح');
  } else {
    console.log(`يوجد ${warehouseCount} مخزن في قاعدة البيانات بالفعل`);
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
