import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { backupId, targetConnectionId, targetDatabaseName } = await request.json();

    if (!backupId) {
      return NextResponse.json(
        { error: 'Backup ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // الحصول على معلومات النسخة الاحتياطية
      const backup = await tx.databaseBackup.findUnique({
        where: { id: backupId },
        include: {
          connection: {
            select: {
              name: true,
              database: true,
            }
          }
        }
      });

      if (!backup) {
        throw new Error('Database backup not found');
      }

      // الحصول على معلومات الاتصال المستهدف
      let targetConnection;
      if (targetConnectionId) {
        targetConnection = await tx.databaseConnection.findUnique({
          where: { id: targetConnectionId }
        });
      } else {
        // استخدام نفس الاتصال إذا لم يتم تحديد هدف
        targetConnection = await tx.databaseConnection.findUnique({
          where: { id: backup.connectionId }
        });
      }

      if (!targetConnection) {
        throw new Error('Target database connection not found');
      }

      // التحقق من وجود ملف النسخة الاحتياطية
      if (!fs.existsSync(backup.filePath)) {
        throw new Error('Backup file not found on disk');
      }

      // التحقق من حالة النسخة الاحتياطية
      if (backup.status !== 'completed') {
        throw new Error('Backup is not in completed status');
      }

      // فك تشفير كلمة المرور
      const dbPassword = targetConnection.password;

      // تحديد اسم قاعدة البيانات المستهدفة
      const targetDb = targetDatabaseName || targetConnection.database;

      // تنفيذ أمر psql للاستعادة
      const restoreCommand = `psql -h ${targetConnection.host} -p ${targetConnection.port} -U ${targetConnection.username} -d ${targetDb} -f "${backup.filePath}" --no-password`;

      const env = {
        ...process.env,
        PGPASSWORD: dbPassword
      };

      await execAsync(restoreCommand, { env });

      // إنشاء audit log للنجاح
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'RESTORE',
        details: `Restored database from backup: ${backup.name} to connection: ${targetConnection.name}, target database: ${targetDb}`,
        tableName: 'databaseBackup',
        recordId: backup.id.toString()
      });

      return {
        message: 'Database restored successfully',
        backup: {
          name: backup.name,
          createdAt: backup.createdAt,
          fileSize: backup.fileSize,
        },
        target: {
          name: targetConnection.name,
          database: targetDb,
        }
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Restore operation error:', error);

    if (error instanceof Error) {
      if (error.message.includes('Database backup not found')) {
        return NextResponse.json({ error: 'Database backup not found' }, { status: 404 });
      }
      if (error.message.includes('Target database connection not found')) {
        return NextResponse.json({ error: 'Target database connection not found' }, { status: 404 });
      }
      if (error.message.includes('Backup file not found on disk')) {
        return NextResponse.json({ error: 'Backup file not found on disk' }, { status: 404 });
      }
      if (error.message.includes('Backup is not in completed status')) {
        return NextResponse.json({ error: 'Backup is not in completed status' }, { status: 400 });
      }
    }

    return NextResponse.json(
      { error: 'Failed to restore database' },
      { status: 500 }
    );
  }
}

// GET للحصول على تاريخ الاستعادة
export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // البحث في سجل الأنشطة عن عمليات الاستعادة
    const restoreHistory = await prisma.auditLog.findMany({
      where: {
        operation: 'RESTORE'
      },
      orderBy: { timestamp: 'desc' },
      take: 50 // آخر 50 عملية استعادة
    });

    return NextResponse.json({
      success: true,
      restoreHistory
    });
  } catch (error) {
    console.error('Failed to fetch restore history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch restore history' },
      { status: 500 }
    );
  }
}
