import { useState, useEffect } from 'react';

export const useDarkMode = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // التحقق من الإعداد المحفوظ في localStorage
    const savedMode = localStorage.getItem('maintenance-dark-mode');
    
    if (savedMode !== null) {
      // استخدام الإعداد المحفوظ
      const isDark = savedMode === 'true';
      setIsDarkMode(isDark);
      updateDocumentClass(isDark);
    } else {
      // اكتشاف تفضيل النظام
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDarkMode(prefersDark);
      updateDocumentClass(prefersDark);
      localStorage.setItem('maintenance-dark-mode', prefersDark.toString());
    }

    // الاستماع لتغييرات تفضيل النظام
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      // فقط إذا لم يكن هناك إعداد محفوظ
      if (localStorage.getItem('maintenance-dark-mode') === null) {
        setIsDarkMode(e.matches);
        updateDocumentClass(e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const updateDocumentClass = (isDark: boolean) => {
    if (isDark) {
      document.documentElement.classList.add('dark-mode');
    } else {
      document.documentElement.classList.remove('dark-mode');
    }
  };

  const toggleDarkMode = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    updateDocumentClass(newMode);
    localStorage.setItem('maintenance-dark-mode', newMode.toString());
  };

  return { isDarkMode, toggleDarkMode };
};
