import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import { SystemSettings } from "../types";

export interface ReportGenerationOptions {
  title: {
    ar: string;
    en: string;
  };
  reportType: string;
  orderInfo: {
    clientName?: string;
    supplierName?: string;
    warehouseName?: string;
    orderNumber: string;
    employeeName: string;
    date: string;
  };
  tableHeaders: string[];
  tableData: any[][];
  totals?: {
    label: string;
    value: string;
  };
}

export function generateAdvancedReport(
  settings: SystemSettings,
  options: ReportGenerationOptions
): jsPDF {
  // إنشاء PDF جديد بتوجيه عمودي
  const doc = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
  });

  // تفعيل دعم الكتابة من اليمين لليسار
  doc.setR2L(true);

  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  let currentY = 15;

  // إضافة الشعار في المنتصف
  if (settings.logoUrl) {
    try {
      const logoSize = 25;
      const logoX = (pageWidth - logoSize) / 2;
      doc.addImage(settings.logoUrl, "PNG", logoX, currentY, logoSize, logoSize);
      currentY += logoSize + 10;
    } catch (error) {
      console.warn("تعذر إضافة الشعار:", error);
    }
  }

  // إضافة اسم الشركة بحجم كبير وخط عريض
  doc.setFontSize(24);
  doc.setFont("helvetica", "bold");
  const companyNameWidth = doc.getTextWidth(settings.companyNameAr);
  doc.text(settings.companyNameAr, pageWidth / 2, currentY, { align: "center" });
  currentY += 12;

  // إضافة العنوان العربي والإنجليزي
  doc.setFontSize(16);
  // العنوان العربي على اليمين
  doc.text(options.title.ar, pageWidth - 20, currentY, { align: "right" });
  // العنوان الإنجليزي على اليسار
  doc.text(options.title.en, 20, currentY, { align: "left" });
  currentY += 10;

  // إضافة نوع التقرير مع تسطير
  doc.setFontSize(14);
  const reportTypeWidth = doc.getTextWidth(options.reportType);
  const reportTypeX = (pageWidth - reportTypeWidth) / 2;
  doc.text(options.reportType, pageWidth / 2, currentY, { align: "center" });
  
  // إضافة خط تحت نوع التقرير
  doc.setLineWidth(0.5);
  doc.line(
    reportTypeX - 5,
    currentY + 1,
    reportTypeX + reportTypeWidth + 5,
    currentY + 1
  );
  currentY += 10;

  // إضافة صف المعلومات
  doc.setFontSize(11);
  doc.setFont("helvetica", "normal");

  // تجميع معلومات الطلب في مصفوفة
  const infoRows: string[][] = [];
  let currentRow: string[] = [];

  // إضافة معلومات حسب نوع التقرير
  if (options.orderInfo.clientName) {
    currentRow.push(`العميل: ${options.orderInfo.clientName}`);
  }
  if (options.orderInfo.supplierName) {
    currentRow.push(`المورد: ${options.orderInfo.supplierName}`);
  }
  if (options.orderInfo.warehouseName) {
    currentRow.push(`المخزن: ${options.orderInfo.warehouseName}`);
  }

  if (currentRow.length > 0) {
    infoRows.push([...currentRow]);
  }

  // إضافة معلومات الطلب الأساسية
  currentRow = [
    `رقم الطلب: ${options.orderInfo.orderNumber}`,
    `الموظف: ${options.orderInfo.employeeName}`,
    `التاريخ: ${options.orderInfo.date}`,
  ];
  infoRows.push(currentRow);

  // طباعة صفوف المعلومات
  infoRows.forEach((row) => {
    const rowText = row.join("   |   ");
    const rowWidth = doc.getTextWidth(rowText);
    doc.text(rowText, pageWidth / 2, currentY, { align: "center" });
    currentY += 7;
  });
  currentY += 5;

  // إضافة الجدول
  if (options.tableHeaders.length > 0 && options.tableData.length > 0) {
    autoTable(doc, {
      head: [options.tableHeaders],
      body: options.tableData,
      startY: currentY,
      theme: "grid",
      styles: {
        fontSize: 10,
        cellPadding: 5,
        halign: "right",
        font: "helvetica",
      },
      headStyles: {
        fillColor: [44, 62, 80],
        textColor: 255,
        halign: "center",
        fontStyle: "bold",
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      margin: { left: 20, right: 20 },
    });

    // إضافة المجموع إذا كان موجوداً
    if (options.totals) {
      const finalY = (doc as any).lastAutoTable.finalY + 5;
      doc.setFontSize(12);
      doc.setFont("helvetica", "bold");
      const totalsText = `${options.totals.label}: ${options.totals.value}`;
      doc.text(totalsText, pageWidth - 20, finalY, { align: "right" });
    }
  }

  // إضافة تذييل الصفحة
  doc.setFontSize(10);
  doc.setFont("helvetica", "normal");
  if (settings.footerTextAr) {
    doc.text(settings.footerTextAr, pageWidth / 2, pageHeight - 10, {
      align: "center",
    });
  }

  return doc;
}
