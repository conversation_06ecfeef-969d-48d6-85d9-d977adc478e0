/**
 * Network utilities for connection management
 */

export function getLocalIpAddress(): string {
  // في بيئة المتصفح، لا يمكن الحصول على عنوان IP المحلي مباشرة
  // هذه محاكاة للعنوان المحلي
  return '*************';
}

export async function checkInternetConnection(): Promise<boolean> {
  try {
    // محاولة الوصول لخدمة خارجية للتحقق من الاتصال
    const response = await fetch('https://www.google.com/favicon.ico', {
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-cache'
    });
    return true;
  } catch (error) {
    console.warn('Internet connection check failed:', error);
    return false;
  }
}

export function generateDeviceId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function getDeviceInfo(): { name: string; userAgent: string; type: string } {
  const userAgent = navigator.userAgent;
  let deviceType = 'Desktop';
  let deviceName = 'جهاز غير معروف';

  // تحديد نوع الجهاز
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    deviceType = 'Mobile';
    if (/iPhone/.test(userAgent)) {
      deviceName = 'iPhone';
    } else if (/iPad/.test(userAgent)) {
      deviceName = 'iPad';
    } else if (/Android/.test(userAgent)) {
      deviceName = 'Android Device';
    }
  } else if (/Tablet/.test(userAgent)) {
    deviceType = 'Tablet';
    deviceName = 'Tablet';
  } else {
    // تحديد نوع المتصفح للأجهزة المكتبية
    if (/Chrome/.test(userAgent)) {
      deviceName = 'Chrome Browser';
    } else if (/Firefox/.test(userAgent)) {
      deviceName = 'Firefox Browser';
    } else if (/Safari/.test(userAgent)) {
      deviceName = 'Safari Browser';
    } else if (/Edge/.test(userAgent)) {
      deviceName = 'Edge Browser';
    }
  }

  return {
    name: deviceName,
    userAgent,
    type: deviceType
  };
}

export async function setupPortForwarding(port: number): Promise<boolean> {
  // في بيئة المتصفح، لا يمكن إعداد إعادة توجيه المنافذ
  // هذه محاكاة للوظيفة
  console.log(`محاولة إعداد إعادة توجيه المنفذ ${port}`);
  
  // محاكاة تأخير العملية
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // إرجاع نتيجة عشوائية للمحاكاة
  return Math.random() > 0.3;
}

export function validateIpAddress(ip: string): boolean {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
}

export function isPrivateIp(ip: string): boolean {
  if (!validateIpAddress(ip)) return false;
  
  const parts = ip.split('.').map(Number);
  
  // 10.0.0.0/8
  if (parts[0] === 10) return true;
  
  // **********/12
  if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return true;
  
  // ***********/16
  if (parts[0] === 192 && parts[1] === 168) return true;
  
  return false;
}

export function formatConnectionTime(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `منذ ${days} ${days === 1 ? 'يوم' : 'أيام'}`;
  } else if (hours > 0) {
    return `منذ ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
  } else if (minutes > 0) {
    return `منذ ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
  } else {
    return 'الآن';
  }
}
