/* تنسيقات مكون ترويسة وتذييل المستندات */

.document-header {
  font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
  color: #333;
  background: white;
}

.document-footer {
  font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
  color: #333;
  background: white;
}

/* تنسيقات الاتجاه */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* تنسيقات الطباعة */
@media print {
  .document-header,
  .document-footer {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .print-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: white;
    z-index: 1000;
  }

  .print-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    z-index: 1000;
  }

  /* تجنب كسر الصفحة داخل الترويسة */
  .header-main {
    page-break-inside: avoid;
  }

  /* تنسيق الشعار للطباعة */
  .logo-container img {
    max-width: 60px;
    max-height: 60px;
    object-fit: contain;
  }

  /* تحسين الخطوط للطباعة */
  .company-names h1,
  .company-names h2 {
    font-weight: bold;
    color: #000 !important;
  }

  .addresses,
  .contact-info,
  .footer-text,
  .timestamp {
    color: #333 !important;
  }

  /* إخفاء الحدود الملونة في الطباعة */
  .border-b-2,
  .border-t-2 {
    border-color: #000 !important;
  }
}

/* تنسيقات خاصة للعرض على الشاشة */
@media screen {
  .document-header {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
  }

  .document-footer {
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
  }
}

/* تنسيقات الشعار */
.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-container img {
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* تنسيقات أسماء الشركة */
.company-names h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
}

.company-names h2 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.3;
}

/* تنسيقات العناوين */
.addresses p {
  margin: 0;
  line-height: 1.4;
}

/* تنسيقات معلومات الاتصال */
.contact-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.contact-info span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;
}

/* تنسيقات عنوان التقرير */
.report-title h2 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.report-title p {
  margin: 0;
  line-height: 1.4;
}

/* تنسيقات التذييل */
.footer-text p {
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.timestamp {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
  .header-main {
    flex-direction: column;
    text-align: center;
  }

  .logo-container {
    margin-bottom: 1rem;
  }

  .company-names h1 {
    font-size: 1.25rem;
  }

  .company-names h2 {
    font-size: 1rem;
  }

  .contact-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .report-title h2 {
    font-size: 1.5rem;
  }
}

/* تحسينات للطباعة بالأبيض والأسود */
@media print and (monochrome) {
  .document-header,
  .document-footer {
    background: white !important;
    color: black !important;
  }

  .company-names h1,
  .company-names h2,
  .report-title h2 {
    color: black !important;
  }

  .addresses,
  .contact-info,
  .footer-text,
  .timestamp {
    color: #333 !important;
  }
}

/* تنسيقات خاصة للتصدير إلى PDF */
.pdf-export .document-header,
.pdf-export .document-footer {
  font-family: 'Helvetica', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تحسينات للخطوط العربية */
.rtl {
  font-feature-settings: 'liga' 1, 'calt' 1, 'kern' 1;
  text-rendering: optimizeLegibility;
}

.ltr {
  font-feature-settings: 'liga' 1, 'calt' 1, 'kern' 1;
  text-rendering: optimizeLegibility;
}
