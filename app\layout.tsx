import type { Metadata } from 'next';
import { Toaster } from '@/components/ui/toaster';
import { StoreProvider } from '@/context/store';
import { StocktakeStoreProvider } from '@/context/stocktake-store';
import { PT_Sans } from 'next/font/google';
import './globals.css';

const ptSans = PT_Sans({
  subsets: ['latin'],
  weight: ['400', '700'],
});

export const metadata: Metadata = {
  title: 'DeviceFlow',
  description: 'نظام إدارة المخزون المتقدم',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={ptSans.className}>
        <StoreProvider>
          <StocktakeStoreProvider>
            {children}
            <Toaster />
          </StocktakeStoreProvider>
        </StoreProvider>
      </body>
    </html>
  );
}
