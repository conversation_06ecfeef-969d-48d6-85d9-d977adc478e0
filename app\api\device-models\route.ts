import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import {
  extractApiQueryParams,
  paginationToPrisma,
  sortToPrisma,
  searchToPrisma,
  filtersToPrisma,
  createPaginatedResponse,
  validatePaginationParams,
  validateSortParams
} from '@/lib/api-helpers';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استخراج معاملات API
    const allowedFilters = ['name', 'manufacturerId'];
    const queryParams = extractApiQueryParams(request, allowedFilters);

    // التحقق من صحة المعاملات
    if (queryParams.pagination) {
      const paginationErrors = validatePaginationParams(queryParams.pagination);
      if (paginationErrors.length > 0) {
        return NextResponse.json({ error: paginationErrors.join(', ') }, { status: 400 });
      }
    }

    const allowedSortFields = ['id', 'name', 'manufacturerId'];
    if (queryParams.sort) {
      const sortErrors = validateSortParams(queryParams.sort, allowedSortFields);
      if (sortErrors.length > 0) {
        return NextResponse.json({ error: sortErrors.join(', ') }, { status: 400 });
      }
    }

    // تحويل المعاملات إلى Prisma
    const paginationPrisma = paginationToPrisma(queryParams.pagination);
    const sortPrisma = sortToPrisma(queryParams.sort, allowedSortFields);
    const searchPrisma = searchToPrisma(queryParams.search, ['name']);
    const filtersPrisma = filtersToPrisma(queryParams.filters, allowedFilters);

    // دمج شروط البحث والتصفية
    const whereClause = {
      ...filtersPrisma,
      ...(searchPrisma && { ...searchPrisma })
    };

    // جلب العدد الإجمالي
    const total = await prisma.deviceModel.count({ where: whereClause });

    // جلب البيانات بدون العلاقات مؤقتاً
    const deviceModels = await prisma.deviceModel.findMany({
      where: whereClause,
      ...paginationPrisma,
      orderBy: sortPrisma || { id: 'desc' }
    });

    // إنشاء الاستجابة المرقمة
    const response = createPaginatedResponse(deviceModels, total, queryParams.pagination || {}, queryParams);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Failed to fetch device models:', error);
    return NextResponse.json({ error: 'Failed to fetch device models' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newModel = await request.json();

    // Basic validation
    if (!newModel.name || !newModel.manufacturerId) {
      return NextResponse.json(
        { error: 'Model name and manufacturer ID are required' },
        { status: 400 }
      );
    }

    // Check if model already exists
    const existingModel = await prisma.deviceModel.findFirst({
      where: {
        name: newModel.name,
        manufacturerId: newModel.manufacturerId
      }
    });

    if (existingModel) {
      return NextResponse.json(
        { error: 'Model already exists for this manufacturer' },
        { status: 400 }
      );
    }

    // Create the model in the database
    const model = await prisma.deviceModel.create({
      data: {
        name: newModel.name,
        manufacturerId: newModel.manufacturerId,
        category: newModel.category || 'هاتف ذكي'
      }
    });

    return NextResponse.json(model, { status: 201 });
  } catch (error) {
    console.error('Failed to create device model:', error);
    return NextResponse.json({ error: 'Failed to create device model' }, { status: 500 });
  }
}
