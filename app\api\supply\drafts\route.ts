import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

// GET - جلب المسودات للمستخدم
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const userId = authResult.user!.id;

    const drafts = await prisma.$queryRaw`
      SELECT * FROM "SupplyOrderDraft" 
      WHERE "userId" = ${userId}
      ORDER BY "updatedAt" DESC
    `;

    return NextResponse.json({ data: drafts });
  } catch (error) {
    console.error('خطأ في جلب المسودات:', error);
    return NextResponse.json({ error: 'فشل في جلب المسودات' }, { status: 500 });
  }
}

// POST - إنشاء/تحديث مسودة
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const userId = authResult.user!.id;
    const body = await request.json();
    const { draftId, ...draftData } = body;

    console.log('💾 حفظ مسودة في قاعدة البيانات:', { draftId, userId });

    // التحقق من وجود المسودة
    const existingDraft = await prisma.$queryRaw`
      SELECT * FROM "SupplyOrderDraft" 
      WHERE "draftId" = ${draftId} AND "userId" = ${userId}
    `;

    if (Array.isArray(existingDraft) && existingDraft.length > 0) {
      // تحديث المسودة الموجودة
      await prisma.$executeRaw`
        UPDATE "SupplyOrderDraft" 
        SET 
          "supplierName" = ${draftData.supplierName || null},
          "supplierId" = ${draftData.supplierId || null},
          "warehouseId" = ${draftData.warehouseId || null},
          "employeeName" = ${draftData.employeeName},
          "supplyDate" = ${draftData.supplyDate},
          "notes" = ${draftData.notes || null},
          "items" = ${JSON.stringify(draftData.items || [])}::jsonb,
          "attachments" = ${JSON.stringify(draftData.attachments || [])}::jsonb,
          "formState" = ${JSON.stringify(draftData.formState || {})}::jsonb,
          "updatedAt" = CURRENT_TIMESTAMP
        WHERE "draftId" = ${draftId} AND "userId" = ${userId}
      `;
    } else {
      // إنشاء مسودة جديدة
      await prisma.$executeRaw`
        INSERT INTO "SupplyOrderDraft" (
          "draftId", "userId", "supplierName", "supplierId", "warehouseId",
          "employeeName", "supplyDate", "notes", "items", "attachments", "formState"
        ) VALUES (
          ${draftId}, ${userId}, ${draftData.supplierName || null}, 
          ${draftData.supplierId || null}, ${draftData.warehouseId || null},
          ${draftData.employeeName}, ${draftData.supplyDate}, ${draftData.notes || null},
          ${JSON.stringify(draftData.items || [])}::jsonb,
          ${JSON.stringify(draftData.attachments || [])}::jsonb,
          ${JSON.stringify(draftData.formState || {})}::jsonb
        )
      `;
    }

    return NextResponse.json({ success: true, message: 'تم حفظ المسودة بنجاح' });
  } catch (error) {
    console.error('خطأ في حفظ المسودة:', error);
    return NextResponse.json({ error: 'فشل في حفظ المسودة' }, { status: 500 });
  }
}

// DELETE - حذف مسودة
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const userId = authResult.user!.id;
    const { searchParams } = new URL(request.url);
    const draftId = searchParams.get('draftId');

    if (!draftId) {
      return NextResponse.json({ error: 'معرف المسودة مطلوب' }, { status: 400 });
    }

    await prisma.$executeRaw`
      DELETE FROM "SupplyOrderDraft" 
      WHERE "draftId" = ${draftId} AND "userId" = ${userId}
    `;

    return NextResponse.json({ success: true, message: 'تم حذف المسودة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المسودة:', error);
    return NextResponse.json({ error: 'فشل في حذف المسودة' }, { status: 500 });
  }
}
