@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 180 25% 8%; /* Dark teal background */
    --foreground: 210 40% 98%;
    --card: 180 25% 8%;
    --card-foreground: 210 40% 98%;
    --popover: 180 25% 8%;
    --popover-foreground: 210 40% 98%;
    --primary: 180 85% 55%; /* Turquoise primary */
    --primary-foreground: 180 25% 8%;
    --secondary: 180 20% 15%;
    --secondary-foreground: 210 40% 98%;
    --muted: 180 20% 15%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 30 94% 65%; /* Light orange */
    --accent-foreground: 24 9.8% 10%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 180 20% 15%;
    --input: 180 20% 15%;
    --ring: 180 85% 75%;
    --radius: 0.5rem;
    --chart-1: 180 85% 65%;
    --chart-2: 33 100% 63%;
    --chart-3: 180 40% 25%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 180 30% 6%; /* Darker teal for sidebar */
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 180 85% 55%;
    --sidebar-primary-foreground: 180 25% 8%;
    --sidebar-accent: 180 20% 15%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 180 20% 15%;
    --sidebar-ring: 180 85% 75%;
  }

  .dark {
    --background: 180 30% 12%; /* Dark teal background */
    --foreground: 210 40% 98%;
    --card: 180 25% 15%;
    --card-foreground: 210 40% 98%;
    --popover: 180 30% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 180 85% 65%; /* Bright turquoise primary */
    --primary-foreground: 180 30% 8%;
    --secondary: 180 20% 20%;
    --secondary-foreground: 210 40% 98%;
    --muted: 180 20% 20%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 30 94% 65%; /* Light orange */
    --accent-foreground: 24 9.8% 10%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 180 20% 20%;
    --input: 180 20% 20%;
    --ring: 180 85% 65%;
    --radius: 0.5rem;
    --chart-1: 180 85% 65%;
    --chart-2: 30 94% 65%;
    --chart-3: 180 60% 45%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 180 35% 8%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 180 85% 65%;
    --sidebar-primary-foreground: 180 30% 8%;
    --sidebar-accent: 180 20% 20%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 180 20% 20%;
    --sidebar-ring: 180 85% 65%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'PT Sans', sans-serif;
    direction: rtl;
  }
}

@layer components {
  .table {
    @apply w-full text-sm text-right;
  }
  .table th {
    @apply p-4 bg-muted/50 text-right font-semibold;
  }
  .table td {
    @apply p-4 border-b;
    border-color: hsl(var(--border));
  }
  .table tbody tr:hover {
    @apply bg-muted/50;
  }
}
