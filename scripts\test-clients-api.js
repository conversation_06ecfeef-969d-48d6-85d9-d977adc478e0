// اختبار API العملاء
async function testClientsAPI() {
  try {
    console.log('اختبار GET /api/clients...');
    
    const response = await fetch('http://localhost:9005/api/clients?limit=100', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('البيانات المستلمة:', data);
    
    if (data.data && Array.isArray(data.data)) {
      console.log(`عدد العملاء: ${data.data.length}`);
      data.data.forEach((client, index) => {
        console.log(`${index + 1}. ${client.name} - ${client.phone}`);
      });
    }
    
  } catch (error) {
    console.error('خطأ في API:', error);
  }
}

// اختبار إضافة عميل جديد
async function testAddClientAPI() {
  try {
    console.log('\nاختبار POST /api/clients...');
    
    const response = await fetch('http://localhost:9005/api/clients', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'عميل API جديد',
        phone: '0507654321',
        email: '<EMAIL>'
      })
    });
    
    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorData}`);
    }
    
    const newClient = await response.json();
    console.log('تم إنشاء العميل عبر API:', newClient);
    
    // تحديث قائمة العملاء
    await testClientsAPI();
    
  } catch (error) {
    console.error('خطأ في إضافة العميل:', error);
  }
}

// تشغيل الاختبارات
async function runTests() {
  await testClientsAPI();
  await testAddClientAPI();
}

runTests();
