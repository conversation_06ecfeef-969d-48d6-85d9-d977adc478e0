import { useState, useEffect } from 'react';

export function useDarkMode() {
  // التحقق من الإعداد المحفوظ أو تفضيل النظام
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem('darkMode');
        if (saved !== null) {
          return JSON.parse(saved);
        }
        // التحقق من تفضيل النظام
        return window.matchMedia('(prefers-color-scheme: dark)').matches;
      } catch (error) {
        console.warn('Error reading dark mode preference:', error);
        return false;
      }
    }
    return false;
  });

  useEffect(() => {
    // التأكد من وجود window قبل التنفيذ
    if (typeof window === 'undefined') return;

    try {
      // حفظ الإعداد في localStorage
      localStorage.setItem('darkMode', JSON.stringify(isDarkMode));

      // تطبيق الفئة على الجسم والعنصر الجذر
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark-mode');
        // إضافة فئة للعنصر الجذر لضمان التوافق
        document.documentElement.setAttribute('data-theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark-mode');
        document.documentElement.setAttribute('data-theme', 'light');
      }
    } catch (error) {
      console.warn('Error applying dark mode:', error);
    }
  }, [isDarkMode]);

  // الاستماع لتغيير تفضيل النظام
  useEffect(() => {
    // التأكد من وجود window قبل التنفيذ
    if (typeof window === 'undefined') return;

    try {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        try {
          // تطبيق تفضيل النظام فقط إذا لم يكن هناك إعداد محفوظ
          const saved = localStorage.getItem('darkMode');
          if (saved === null) {
            setIsDarkMode(e.matches);
          }
        } catch (error) {
          console.warn('Error handling media query change:', error);
        }
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => {
        try {
          mediaQuery.removeEventListener('change', handleChange);
        } catch (error) {
          console.warn('Error removing media query listener:', error);
        }
      };
    } catch (error) {
      console.warn('Error setting up media query listener:', error);
    }
  }, []);

  const toggleDarkMode = () => {
    setIsDarkMode(prev => !prev);
  };

  return { isDarkMode, toggleDarkMode };
}
