#!/usr/bin/env tsx

// اختبار إنشاء النسخ الاحتياطية
async function testBackupCreation() {
  console.log('🔍 اختبار إنشاء النسخ الاحتياطية...');

  const baseUrl = 'http://localhost:9005';
  const token = Buffer.from('user:admin:admin').toString('base64');
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  try {
    // 1. جلب الاتصالات المتاحة
    console.log('\n📋 جلب اتصالات قواعد البيانات...');
    
    const connectionsResponse = await fetch(`${baseUrl}/api/database/connections`, {
      method: 'GET',
      headers
    });

    if (!connectionsResponse.ok) {
      console.log(`❌ فشل في جلب الاتصالات: ${connectionsResponse.status}`);
      return;
    }

    const connectionsData = await connectionsResponse.json();
    console.log(`✅ تم جلب ${connectionsData.length} اتصال`);
    
    if (connectionsData.length === 0) {
      console.log('❌ لا توجد اتصالات متاحة');
      return;
    }

    const defaultConnection = connectionsData.find((c: any) => c.isDefault) || connectionsData[0];
    console.log(`📊 سيتم استخدام الاتصال: ${defaultConnection.name}`);

    // 2. اختبار إنشاء نسخة احتياطية
    console.log('\n💾 إنشاء نسخة احتياطية...');
    
    const backupData = {
      connectionId: defaultConnection.id,
      name: `نسخة احتياطية تجريبية ${new Date().toISOString().split('T')[0]}`,
      description: 'نسخة احتياطية للاختبار'
    };

    const backupResponse = await fetch(`${baseUrl}/api/database/backup`, {
      method: 'POST',
      headers,
      body: JSON.stringify(backupData)
    });

    console.log(`📊 حالة الاستجابة: ${backupResponse.status}`);

    if (backupResponse.ok) {
      const backupResult = await backupResponse.json();
      console.log('✅ تم إنشاء النسخة الاحتياطية بنجاح:');
      console.log(`   - ID: ${backupResult.id}`);
      console.log(`   - الاسم: ${backupResult.name}`);
      console.log(`   - الحالة: ${backupResult.status}`);
      console.log(`   - حجم الملف: ${backupResult.fileSize || 'غير محدد'}`);
    } else {
      const errorText = await backupResponse.text();
      console.log(`❌ فشل في إنشاء النسخة الاحتياطية: ${errorText}`);
    }

    // 3. جلب قائمة النسخ الاحتياطية
    console.log('\n📋 جلب قائمة النسخ الاحتياطية...');
    
    const backupsListResponse = await fetch(`${baseUrl}/api/database/backup`, {
      method: 'GET',
      headers
    });

    if (backupsListResponse.ok) {
      const backupsList = await backupsListResponse.json();
      console.log(`✅ تم جلب ${backupsList.length} نسخة احتياطية`);
      
      if (backupsList.length > 0) {
        console.log('\n📋 آخر 3 نسخ احتياطية:');
        backupsList.slice(0, 3).forEach((backup: any) => {
          console.log(`   - ${backup.name}: ${backup.status} (${backup.fileSize || 'غير محدد'})`);
        });
      }
    } else {
      console.log(`❌ فشل في جلب قائمة النسخ الاحتياطية: ${backupsListResponse.status}`);
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار النسخ الاحتياطية:', error);
  }
}

testBackupCreation();
