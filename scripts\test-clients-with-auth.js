// اختبار API العملاء مع التفويض
async function testClientsAPIWithAuth() {
  try {
    console.log('اختبار API العملاء مع التفويض...');
    
    const devToken = btoa('user:admin:admin');
    
    // اختبار GET
    console.log('\n1. اختبار GET /api/clients...');
    const getResponse = await fetch('http://localhost:9005/api/clients?limit=100', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${devToken}`
      }
    });
    
    if (!getResponse.ok) {
      throw new Error(`GET HTTP ${getResponse.status}: ${getResponse.statusText}`);
    }
    
    const getData = await getResponse.json();
    console.log('✅ GET نجح - عدد العملاء:', getData.data?.length || 0);
    
    if (getData.data && Array.isArray(getData.data)) {
      getData.data.slice(0, 3).forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.name} - ${client.phone}`);
      });
    }
    
    // اختبار POST
    console.log('\n2. اختبار POST /api/clients...');
    const postResponse = await fetch('http://localhost:9005/api/clients', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${devToken}`
      },
      body: JSON.stringify({
        name: 'عميل تجريبي جديد',
        phone: '0509876543',
        email: '<EMAIL>'
      })
    });
    
    if (!postResponse.ok) {
      const errorData = await postResponse.text();
      throw new Error(`POST HTTP ${postResponse.status}: ${errorData}`);
    }
    
    const newClient = await postResponse.json();
    console.log('✅ POST نجح - تم إنشاء العميل:', newClient.name);
    console.log('   المعرف:', newClient.id);
    
    // إعادة اختبار GET للتأكد من الإضافة
    console.log('\n3. التحقق من إضافة العميل...');
    const checkResponse = await fetch('http://localhost:9005/api/clients?limit=100', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${devToken}`
      }
    });
    
    const checkData = await checkResponse.json();
    console.log('✅ العدد الجديد للعملاء:', checkData.data?.length || 0);
    
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    return false;
  }
}

// تشغيل الاختبار
testClientsAPIWithAuth().then(success => {
  if (success) {
    console.log('\n🎉 جميع الاختبارات نجحت!');
  } else {
    console.log('\n💥 فشل الاختبار');
  }
});
