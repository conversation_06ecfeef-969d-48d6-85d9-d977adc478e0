# حل مشكلة الصلاحيات بعد تحديث اسم المستخدم

## وصف المشكلة
عند تغيير اسم المستخدم من "مدير النظام" إلى "مدير النظام 2"، يحدث خلل في نظام المصادقة والصلاحيات، مما يؤدي إلى ظهور رسائل خطأ مثل:
```
Can't check permissions for dashboard currentUser or permissions missing
```

## أسباب المشكلة
1. نظام المصادقة يبحث عن المستخدم باستخدام `username` ثابت (`admin`)
2. عند تحديث اسم المستخدم، لا يتم تحديث معلومات المستخدم الحالي في النظام
3. عدم تطابق بين التوكن المُرسل وبيانات المستخدم في قاعدة البيانات

## الحلول المطبقة

### 1. تحديث نظام المصادقة (`lib/auth.ts`)
- تحسين البحث عن المستخدم الإداري ليشمل عدة معايير
- إضافة دعم للبحث بالاسم الذي يحتوي على "مدير"
- تحسين معالجة المستخدمين الإداريين

### 2. تحديث فحص الصلاحيات (`hooks/usePermission.ts`)
- إضافة منطق خاص للمستخدمين الإداريين
- إعطاء صلاحيات كاملة للمستخدمين الذين يحتوون على "مدير" في الاسم
- تحسين التحقق من صلاحيات المدير

### 3. تحديث مكونات واجهة المستخدم
- `components/main-nav.tsx`: تحسين فحص الصلاحيات وإزالة رسائل الخطأ
- `components/permissions-section.tsx`: دعم الأسماء المختلفة للمدير
- `app/(main)/messaging/page.tsx`: تحديث منطق ترتيب المستخدمين

### 4. تحديث إدارة الحالة (`context/store.tsx`)
- تحسين تحديث المستخدم الحالي عند تحديث بيانات المستخدم
- تحسين إنشاء التوكن للمصادقة

## خطوات الحل اليدوي

### 1. تشغيل سكريبت الإصلاح
```bash
node scripts/fix-user-auth.js
```

### 2. إعادة تشغيل الخادم
```bash
npm run dev
```

### 3. التحقق من النتائج
- تسجيل الدخول مرة أخرى
- التحقق من عدم ظهور رسائل خطأ الصلاحيات
- التأكد من عمل جميع الصفحات بشكل صحيح

## الحلول البديلة

### إذا استمرت المشكلة
1. **مسح الكاش وإعادة التشغيل:**
   ```bash
   rm -rf .next
   npm run dev
   ```

2. **تحديث بيانات المستخدم يدوياً في قاعدة البيانات:**
   ```sql
   UPDATE User 
   SET username = 'admin', email = '<EMAIL>', status = 'Active'
   WHERE role = 'admin' AND name LIKE '%مدير%';
   ```

3. **إنشاء مستخدم إداري جديد:**
   ```sql
   INSERT OR REPLACE INTO User (username, email, name, role, status, createdAt, updatedAt)
   VALUES ('admin', '<EMAIL>', 'مدير النظام', 'admin', 'Active', datetime('now'), datetime('now'));
   ```

## نصائح لتجنب المشكلة مستقبلاً

1. **لا تغيير `username` للمستخدم الإداري الرئيسي**
2. **استخدم حقل `name` لتغيير الاسم المعروض**
3. **احتفظ بـ `username = 'admin'` ثابتاً للمستخدم الإداري**
4. **اختبر النظام بعد أي تحديث على بيانات المستخدمين**

## ملفات مهمة تم تحديثها
- `lib/auth.ts` - نظام المصادقة
- `hooks/usePermission.ts` - فحص الصلاحيات
- `components/main-nav.tsx` - شريط التنقل الرئيسي
- `components/permissions-section.tsx` - قسم إدارة الصلاحيات
- `context/store.tsx` - إدارة الحالة العامة
- `app/(main)/messaging/page.tsx` - صفحة الرسائل

## التحقق من نجاح الحل
✅ عدم ظهور رسائل خطأ الصلاحيات في console المطور
✅ عمل جميع صفحات النظام بشكل صحيح
✅ إمكانية تحديث بيانات المستخدم دون مشاكل
✅ عرض جميع عناصر شريط التنقل بشكل صحيح
