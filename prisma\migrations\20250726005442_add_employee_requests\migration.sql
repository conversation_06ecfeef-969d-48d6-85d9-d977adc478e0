-- CreateTable
CREATE TABLE "employee_requests" (
    "id" SERIAL NOT NULL,
    "requestNumber" TEXT NOT NULL,
    "requestType" TEXT NOT NULL,
    "priority" TEXT NOT NULL,
    "notes" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'قيد المراجعة',
    "requestDate" TEXT NOT NULL,
    "employeeName" TEXT NOT NULL,
    "employeeId" INTEGER NOT NULL,
    "relatedOrderType" TEXT,
    "relatedOrderId" INTEGER,
    "relatedOrderDisplayId" TEXT,
    "attachmentName" TEXT,
    "adminNotes" TEXT,
    "processedBy" INTEGER,
    "processedDate" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "employee_requests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "employee_requests_requestNumber_key" ON "employee_requests"("requestNumber");
