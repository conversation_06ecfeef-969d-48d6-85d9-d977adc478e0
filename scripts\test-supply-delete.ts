import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testSupplyOrderDeletion() {
  try {
    console.log('=== اختبار حذف أمر التوريد ===\n');

    // التحقق من الأوامر الموجودة
    const existingOrders = await prisma.supplyOrder.findMany({
      select: { id: true, supplyOrderId: true, status: true }
    });
    
    console.log(`أوامر التوريد الموجودة: ${existingOrders.length}`);
    existingOrders.forEach(order => {
      console.log(`- ${order.supplyOrderId} (ID: ${order.id}) - ${order.status}`);
    });

    if (existingOrders.length === 0) {
      console.log('لا توجد أوامر توريد للاختبار. إنشاء أمر تجريبي...');
      
      // إنشاء أمر تجريبي
      const testOrder = await prisma.supplyOrder.create({
        data: {
          supplyOrderId: 'SUP-TEST-DELETE',
          supplierId: 1,
          warehouseId: 1,
          employeeName: 'مستخدم تجريبي',
          supplyDate: new Date().toISOString(),
          status: 'pending'
        }
      });
      
      console.log(`تم إنشاء أمر تجريبي: ${testOrder.supplyOrderId} (ID: ${testOrder.id})`);
      
      // محاولة حذف الأمر التجريبي
      const deletedOrder = await prisma.supplyOrder.delete({
        where: { id: testOrder.id }
      });
      
      console.log(`تم حذف الأمر بنجاح: ${deletedOrder.supplyOrderId}`);
      
    } else {
      // اختبار حذف أول أمر موجود
      const orderToDelete = existingOrders[0];
      console.log(`\nمحاولة حذف الأمر: ${orderToDelete.supplyOrderId}`);
      
      const deletedOrder = await prisma.supplyOrder.delete({
        where: { id: orderToDelete.id }
      });
      
      console.log(`تم حذف الأمر بنجاح: ${deletedOrder.supplyOrderId}`);
      
      // التحقق من الأوامر المتبقية
      const remainingOrders = await prisma.supplyOrder.count();
      console.log(`الأوامر المتبقية: ${remainingOrders}`);
    }

  } catch (error) {
    console.error('خطأ في اختبار الحذف:', error);
    
    // فحص نوع الخطأ
    if (error.code === 'P2003') {
      console.log('الخطأ: قيود على الحذف - الأمر مرتبط ببيانات أخرى');
    } else if (error.code === 'P2025') {
      console.log('الخطأ: الأمر غير موجود');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testSupplyOrderDeletion();
