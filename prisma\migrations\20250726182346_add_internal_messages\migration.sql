-- CreateTable
CREATE TABLE "internal_messages" (
    "id" SERIAL NOT NULL,
    "threadId" INTEGER NOT NULL,
    "senderId" INTEGER NOT NULL,
    "senderName" TEXT NOT NULL,
    "recipientId" INTEGER NOT NULL,
    "recipientName" TEXT NOT NULL,
    "recipientIds" JSONB,
    "text" TEXT NOT NULL,
    "attachmentName" TEXT,
    "attachmentContent" TEXT,
    "attachmentType" TEXT,
    "attachmentUrl" TEXT,
    "attachmentFileName" TEXT,
    "attachmentSize" INTEGER,
    "sentDate" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'مرسلة',
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "parentMessageId" INTEGER,
    "employeeRequestId" INTEGER,
    "resolutionNote" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "internal_messages_pkey" PRIMARY KEY ("id")
);
