import React from 'react';
import { <PERSON>, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useDarkMode } from './useDarkMode';

interface DarkModeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'outline' | 'default' | 'ghost';
}

export function DarkModeToggle({
  className = '',
  size = 'md',
  variant = 'outline'
}: DarkModeToggleProps) {
  const { isDarkMode, toggleDarkMode } = useDarkMode();

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <Button
      variant={variant}
      size="icon"
      onClick={toggleDarkMode}
      className={`
        ${sizeClasses[size]}
        enhanced-button
        transition-all
        duration-300
        hover:scale-105
        ${className}
      `}
      title={isDarkMode ? 'تفعيل الوضع النهاري' : 'تفعيل الوضع الليلي'}
    >
      <div className="relative">
        <Sun
          className={`
            ${iconSizes[size]}
            absolute
            transition-all
            duration-300
            ${isDarkMode
              ? 'rotate-90 scale-0 opacity-0'
              : 'rotate-0 scale-100 opacity-100'
            }
          `}
        />
        <Moon
          className={`
            ${iconSizes[size]}
            absolute
            transition-all
            duration-300
            ${isDarkMode
              ? 'rotate-0 scale-100 opacity-100'
              : '-rotate-90 scale-0 opacity-0'
            }
          `}
        />
      </div>
    </Button>
  );
}
