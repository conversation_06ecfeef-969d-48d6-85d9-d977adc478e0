import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

/**
 * تنفيذ عملية داخل معاملة مع إدارة الأخطاء
 */
export async function executeInTransaction<T>(
  operation: (tx: Prisma.TransactionClient) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(async (tx) => {
    return await operation(tx);
  });
}

/**
 * تنفيذ عدة عمليات داخل معاملة واحدة
 */
export async function executeMultipleInTransaction<T>(
  operations: ((tx: Prisma.TransactionClient) => Promise<any>)[]
): Promise<T[]> {
  return await prisma.$transaction(async (tx) => {
    const results: T[] = [];
    for (const operation of operations) {
      const result = await operation(tx);
      results.push(result);
    }
    return results;
  });
}

/**
 * إنشاء audit log داخل معاملة
 */
export async function createAuditLogInTransaction(
  tx: any,
  data: {
    userId: number;
    username: string;
    operation: string;
    details: string;
  }
) {
  return await tx.auditLog.create({
    data: {
      userId: data.userId,
      username: data.username,
      operation: data.operation,
      details: data.details,
      timestamp: new Date()
    }
  });
}

/**
 * التحقق من وجود العلاقات قبل الحذف
 */
export async function checkRelationsBeforeDelete(
  tx: Prisma.TransactionClient,
  tableName: string,
  recordId: string | number
): Promise<{ hasRelations: boolean; relations: string[] }> {
  const relations: string[] = [];
  
  try {
    switch (tableName) {
      case 'client':
        const clientOperations = await tx.maintenanceOrder.count({
          where: { clientId: recordId as string }
        });
        if (clientOperations > 0) {
          relations.push(`${clientOperations} maintenance orders`);
        }
        break;
        
      case 'device':
        const deviceLogs = await tx.maintenanceLog.count({
          where: { deviceId: recordId as string }
        });
        if (deviceLogs > 0) {
          relations.push(`${deviceLogs} maintenance logs`);
        }
        break;
        
      case 'supplier':
        const supplierOrders = await tx.maintenanceOrder.count({
          where: { supplierId: recordId as string }
        });
        if (supplierOrders > 0) {
          relations.push(`${supplierOrders} maintenance orders`);
        }
        break;
        
      default:
        // يمكن إضافة المزيد من الجداول حسب الحاجة
        break;
    }
    
    return {
      hasRelations: relations.length > 0,
      relations
    };
  } catch (error) {
    console.error('Error checking relations:', error);
    return {
      hasRelations: false,
      relations: []
    };
  }
}

/**
 * إنشاء رقم تسلسلي فريد (للاستخدام مع Prisma Client مباشرة)
 */
export async function generateUniqueId(
  tableName: string,
  prefix: string = '',
  client: any = null
): Promise<string> {
  const prismaClient = client || prisma;
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    let id: string;
    let exists = false;

    try {
      switch (tableName) {
        case 'supplyOrder':
          // للتوريد، استخدم أرقام متسلسلة - البحث عن أكبر رقم صحيح
          const allSupplyOrders = await (prismaClient as any).supply_orders.findMany({
            select: { orderNumber: true },
            orderBy: { id: 'desc' }
          });

          let maxSupplyNumber = 0;

          // البحث عن أكبر رقم صحيح في جميع الأوامر
          allSupplyOrders.forEach(order => {
            const match = order.orderNumber.match(/^SUP-(\d+)$/);
            if (match) {
              const num = parseInt(match[1]);
              if (!isNaN(num) && num > maxSupplyNumber) {
                maxSupplyNumber = num;
              }
            }
          });

          id = `${prefix}${maxSupplyNumber + 1}`;

          // التحقق من عدم وجود الرقم
          const existingSupplyOrder = await (prismaClient as any).supply_orders.findUnique({
            where: { orderNumber: id }
          });
          exists = !!existingSupplyOrder;
          break;

        case 'sale':
          // للمبيعات، استخدم أرقام متسلسلة
          const lastSale = await prismaClient.sale.findFirst({
            where: {
              soNumber: {
                startsWith: prefix
              }
            },
            orderBy: {
              soNumber: 'desc'
            }
          });

          let nextSaleNumber = 1;
          if (lastSale && lastSale.soNumber) {
            const currentNumber = parseInt(lastSale.soNumber.replace(prefix, ''));
            if (!isNaN(currentNumber)) {
              nextSaleNumber = currentNumber + 1;
            }
          }

          id = `${prefix}${nextSaleNumber}`;

          // التحقق من عدم وجود الرقم
          const existingSale = await prismaClient.sale.findUnique({
            where: { soNumber: id }
          });
          exists = !!existingSale;
          break;

        case 'return':
          // للمرتجعات، استخدم أرقام متسلسلة
          const lastReturn = await prismaClient.return.findFirst({
            where: {
              roNumber: {
                startsWith: prefix
              }
            },
            orderBy: {
              roNumber: 'desc'
            }
          });

          let nextReturnNumber = 1;
          if (lastReturn && lastReturn.roNumber) {
            const currentNumber = parseInt(lastReturn.roNumber.replace(prefix, ''));
            if (!isNaN(currentNumber)) {
              nextReturnNumber = currentNumber + 1;
            }
          }

          id = `${prefix}${nextReturnNumber}`;

          // التحقق من عدم وجود الرقم
          const existingReturn = await prismaClient.return.findUnique({
            where: { roNumber: id }
          });
          exists = !!existingReturn;
          break;

        case 'maintenanceOrder':
          // للصيانة، استخدم timestamp + random
          const timestamp = Date.now();
          const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${timestamp}${random}`;

          const order = await prismaClient.maintenanceOrder.findFirst({
            where: { orderNumber: id }
          });
          exists = !!order;
          break;

        case 'deliveryOrder':
          // للتسليم، استخدم timestamp + random
          const deliveryTimestamp = Date.now();
          const deliveryRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${deliveryTimestamp}${deliveryRandom}`;

          const delivery = await prismaClient.deliveryOrder.findFirst({
            where: { deliveryOrderNumber: id }
          });
          exists = !!delivery;
          break;

        default:
          // للجداول الأخرى، استخدم timestamp + random
          const defaultTimestamp = Date.now();
          const defaultRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${defaultTimestamp}${defaultRandom}`;
          exists = false;
      }

      if (!exists) {
        return id;
      }
    } catch (error) {
      console.error('Error checking ID uniqueness:', error);
    }

    attempts++;
    // انتظار قصير قبل المحاولة التالية
    await new Promise(resolve => setTimeout(resolve, 10));
  }

  throw new Error(`Failed to generate unique ID after ${maxAttempts} attempts`);
}

/**
 * إنشاء رقم تسلسلي فريد داخل معاملة
 */
export async function generateUniqueIdInTransaction(
  tx: Prisma.TransactionClient,
  tableName: string,
  prefix: string = ''
): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    let id: string;
    let exists = false;

    try {
      switch (tableName) {
        case 'supplyOrder':
          // للتوريد، استخدم أرقام متسلسلة - البحث عن أكبر رقم صحيح
          const allSupplyOrders = await (tx as any).supply_orders.findMany({
            select: { orderNumber: true },
            orderBy: { id: 'desc' }
          });

          let maxSupplyNumber = 0;

          // البحث عن أكبر رقم صحيح في جميع الأوامر
          allSupplyOrders.forEach(order => {
            const match = order.orderNumber.match(/^SUP-(\d+)$/);
            if (match) {
              const num = parseInt(match[1]);
              if (!isNaN(num) && num > maxSupplyNumber) {
                maxSupplyNumber = num;
              }
            }
          });

          id = `${prefix}${maxSupplyNumber + 1}`;

          // التحقق من عدم وجود الرقم
          const existingSupplyOrder = await (tx as any).supply_orders.findUnique({
            where: { orderNumber: id }
          });
          exists = !!existingSupplyOrder;
          break;

        case 'sale':
          // للمبيعات، استخدم أرقام متسلسلة
          const lastSale = await tx.sale.findFirst({
            where: {
              soNumber: {
                startsWith: prefix
              }
            },
            orderBy: {
              soNumber: 'desc'
            }
          });

          let nextSaleNumber = 1;
          if (lastSale && lastSale.soNumber) {
            const currentNumber = parseInt(lastSale.soNumber.replace(prefix, ''));
            if (!isNaN(currentNumber)) {
              nextSaleNumber = currentNumber + 1;
            }
          }

          id = `${prefix}${nextSaleNumber}`;

          // التحقق من عدم وجود الرقم
          const existingSale = await tx.sale.findUnique({
            where: { soNumber: id }
          });
          exists = !!existingSale;
          break;

        case 'return':
          // للمرتجعات، استخدم أرقام متسلسلة
          const lastReturn = await tx.return.findFirst({
            where: {
              roNumber: {
                startsWith: prefix
              }
            },
            orderBy: {
              roNumber: 'desc'
            }
          });

          let nextReturnNumber = 1;
          if (lastReturn && lastReturn.roNumber) {
            const currentNumber = parseInt(lastReturn.roNumber.replace(prefix, ''));
            if (!isNaN(currentNumber)) {
              nextReturnNumber = currentNumber + 1;
            }
          }

          id = `${prefix}${nextReturnNumber}`;

          // التحقق من عدم وجود الرقم
          const existingReturn = await tx.return.findUnique({
            where: { roNumber: id }
          });
          exists = !!existingReturn;
          break;

        case 'maintenanceOrder':
          // للصيانة، استخدم timestamp + random
          const timestamp = Date.now();
          const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${timestamp}${random}`;

          const order = await tx.maintenanceOrder.findFirst({
            where: { orderNumber: id }
          });
          exists = !!order;
          break;

        case 'deliveryOrder':
          // للتسليم، استخدم timestamp + random
          const deliveryTimestamp = Date.now();
          const deliveryRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${deliveryTimestamp}${deliveryRandom}`;

          const delivery = await tx.deliveryOrder.findFirst({
            where: { deliveryOrderNumber: id }
          });
          exists = !!delivery;
          break;

        default:
          // للجداول الأخرى، استخدم timestamp + random
          const defaultTimestamp = Date.now();
          const defaultRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${defaultTimestamp}${defaultRandom}`;
          exists = false;
      }

      if (!exists) {
        return id;
      }
    } catch (error) {
      console.error('Error checking ID uniqueness:', error);
    }

    attempts++;
    // انتظار قصير قبل المحاولة التالية
    await new Promise(resolve => setTimeout(resolve, 10));
  }

  throw new Error(`Failed to generate unique ID after ${maxAttempts} attempts`);
}
