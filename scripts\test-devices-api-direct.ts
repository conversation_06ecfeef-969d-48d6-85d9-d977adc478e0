import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDevicesApi() {
  try {
    console.log('=== اختبار API للأجهزة مباشرة ===\n');

    // محاكاة استدعاء API endpoint
    console.log('1. اختبار جلب الأجهزة من قاعدة البيانات:');
    
    const devices = await prisma.device.findMany({
      orderBy: { dateAdded: 'desc' },
      take: 1000
    });

    console.log(`   عدد الأجهزة: ${devices.length}`);
    
    if (devices.length > 0) {
      console.log('   الأجهزة الموجودة:');
      devices.forEach((device, index) => {
        console.log(`   ${index + 1}. ${device.id} - ${device.model}`);
        console.log(`      الحالة: ${device.status}`);
        console.log(`      المخزن: ${device.warehouseId}`);
        console.log(`      المورد: ${device.supplierId}`);
        console.log(`      السعر: ${device.price}`);
        console.log(`      التخزين: ${device.storage}`);
        console.log('      ---');
      });
    }

    // اختبار فلترة الأجهزة المتاحة للبيع
    console.log('\n2. الأجهزة المتاحة للبيع فقط:');
    const availableDevices = await prisma.device.findMany({
      where: { status: 'متاح للبيع' },
      orderBy: { dateAdded: 'desc' }
    });

    console.log(`   عدد الأجهزة المتاحة: ${availableDevices.length}`);
    availableDevices.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device.id} - ${device.model} (${device.status})`);
    });

    // اختبار البحث
    console.log('\n3. اختبار البحث (Apple):');
    const searchResults = await prisma.device.findMany({
      where: {
        OR: [
          { id: { contains: 'Apple', mode: 'insensitive' } },
          { model: { contains: 'Apple', mode: 'insensitive' } },
          { storage: { contains: 'Apple', mode: 'insensitive' } },
          { status: { contains: 'Apple', mode: 'insensitive' } }
        ]
      }
    });

    console.log(`   نتائج البحث: ${searchResults.length}`);
    searchResults.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device.id} - ${device.model}`);
    });

    // فحص المخازن المرتبطة
    console.log('\n4. فحص المخازن المرتبطة:');
    const warehouses = await prisma.warehouse.findMany({
      select: { id: true, name: true }
    });
    
    warehouses.forEach(warehouse => {
      const devicesInWarehouse = devices.filter(d => d.warehouseId === warehouse.id);
      console.log(`   مخزن ${warehouse.name} (${warehouse.id}): ${devicesInWarehouse.length} جهاز`);
    });

    console.log('\n✅ اختبار API مكتمل');

  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDevicesApi();
