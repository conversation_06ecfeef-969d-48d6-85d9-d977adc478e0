/**
 * Local Server Service for Connection Management
 * This is a client-side simulation of a local server
 */

import { ConnectedDevice, ServerInfo } from './types';
import { getLocalIpAddress, generateDeviceId, getDeviceInfo, formatConnectionTime } from './network';

class LocalServerService {
  private connectedDevices: Map<string, ConnectedDevice> = new Map();
  private serverInfo: ServerInfo = {
    port: 3000,
    ipAddress: getLocalIpAddress(),
    isActive: false,
    remoteAccessEnabled: false
  };
  private connectionListeners: ((devices: ConnectedDevice[]) => void)[] = [];
  private simulationInterval: NodeJS.Timeout | null = null;

  constructor() {
    // محاكاة بعض الأجهزة المتصلة للعرض التوضيحي
    this.addMockDevices();
  }

  private addMockDevices() {
    const mockDevices: Omit<ConnectedDevice, 'id'>[] = [
      {
        name: 'iPhone 13 Pro',
        ip: '*************',
        connectedAt: new Date(Date.now() - 300000), // 5 minutes ago
        status: 'متصل',
        deviceType: 'Mobile',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)',
        lastActivity: new Date(Date.now() - 60000)
      },
      {
        name: 'Chrome Browser',
        ip: '*************',
        connectedAt: new Date(Date.now() - 1800000), // 30 minutes ago
        status: 'متصل',
        deviceType: 'Desktop',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        lastActivity: new Date(Date.now() - 120000)
      }
    ];

    mockDevices.forEach(device => {
      const id = generateDeviceId();
      this.connectedDevices.set(id, { ...device, id });
    });
  }

  public start(): ServerInfo {
    this.serverInfo.isActive = true;
    this.serverInfo.ipAddress = getLocalIpAddress();
    
    // بدء محاكاة الأنشطة
    this.startActivitySimulation();
    
    console.log(`الخادم المحلي يعمل على المنفذ ${this.serverInfo.port}`);
    this.notifyListeners();
    
    return { ...this.serverInfo };
  }

  public stop(): void {
    this.serverInfo.isActive = false;
    this.serverInfo.remoteAccessEnabled = false;
    
    // إيقاف المحاكاة
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
      this.simulationInterval = null;
    }
    
    // قطع اتصال جميع الأجهزة
    this.connectedDevices.clear();
    this.addMockDevices(); // إعادة إضافة الأجهزة الوهمية
    
    console.log('تم إيقاف الخادم المحلي');
    this.notifyListeners();
  }

  public enableRemoteAccess(): boolean {
    if (!this.serverInfo.isActive) {
      return false;
    }
    
    this.serverInfo.remoteAccessEnabled = true;
    console.log('تم تمكين الوصول عن بُعد');
    return true;
  }

  public disableRemoteAccess(): void {
    this.serverInfo.remoteAccessEnabled = false;
    console.log('تم تعطيل الوصول عن بُعد');
  }

  public getConnectedDevices(): ConnectedDevice[] {
    return Array.from(this.connectedDevices.values());
  }

  public getServerInfo(): ServerInfo {
    return { ...this.serverInfo };
  }

  public blockDevice(deviceId: string): boolean {
    const device = this.connectedDevices.get(deviceId);
    if (device) {
      device.status = 'محظور';
      this.connectedDevices.set(deviceId, device);
      this.notifyListeners();
      console.log(`تم حظر الجهاز: ${device.name}`);
      return true;
    }
    return false;
  }

  public allowDevice(deviceId: string): boolean {
    const device = this.connectedDevices.get(deviceId);
    if (device) {
      device.status = 'مسموح';
      this.connectedDevices.set(deviceId, device);
      this.notifyListeners();
      console.log(`تم السماح للجهاز: ${device.name}`);
      return true;
    }
    return false;
  }

  public disconnectDevice(deviceId: string): boolean {
    const device = this.connectedDevices.get(deviceId);
    if (device) {
      this.connectedDevices.delete(deviceId);
      this.notifyListeners();
      console.log(`تم قطع اتصال الجهاز: ${device.name}`);
      return true;
    }
    return false;
  }

  public addConnectionListener(callback: (devices: ConnectedDevice[]) => void): void {
    this.connectionListeners.push(callback);
  }

  public removeConnectionListener(callback: (devices: ConnectedDevice[]) => void): void {
    const index = this.connectionListeners.indexOf(callback);
    if (index > -1) {
      this.connectionListeners.splice(index, 1);
    }
  }

  private notifyListeners(): void {
    const devices = this.getConnectedDevices();
    this.connectionListeners.forEach(callback => callback(devices));
  }

  private startActivitySimulation(): void {
    // محاكاة أنشطة الأجهزة المتصلة
    this.simulationInterval = setInterval(() => {
      if (!this.serverInfo.isActive) return;

      // تحديث آخر نشاط لبعض الأجهزة عشوائياً
      const devices = Array.from(this.connectedDevices.values());
      devices.forEach(device => {
        if (Math.random() > 0.7) { // 30% احتمال تحديث النشاط
          device.lastActivity = new Date();
          this.connectedDevices.set(device.id, device);
        }
      });

      // إضافة جهاز جديد أحياناً
      if (Math.random() > 0.95 && devices.length < 5) {
        this.addRandomDevice();
      }

      // إزالة جهاز أحياناً
      if (Math.random() > 0.98 && devices.length > 2) {
        const randomDevice = devices[Math.floor(Math.random() * devices.length)];
        this.connectedDevices.delete(randomDevice.id);
      }

      this.notifyListeners();
    }, 5000); // كل 5 ثوان
  }

  private addRandomDevice(): void {
    const deviceNames = ['Samsung Galaxy', 'iPad Air', 'MacBook Pro', 'Firefox Browser', 'Edge Browser'];
    const randomName = deviceNames[Math.floor(Math.random() * deviceNames.length)];
    const randomIp = `192.168.1.${Math.floor(Math.random() * 200) + 10}`;
    
    const newDevice: ConnectedDevice = {
      id: generateDeviceId(),
      name: randomName,
      ip: randomIp,
      connectedAt: new Date(),
      status: 'متصل',
      deviceType: randomName.includes('iPad') || randomName.includes('Galaxy') ? 'Mobile' : 'Desktop',
      userAgent: 'Mozilla/5.0 (simulated device)',
      lastActivity: new Date()
    };

    this.connectedDevices.set(newDevice.id, newDevice);
    console.log(`جهاز جديد متصل: ${newDevice.name}`);
  }

  public refreshDevices(): ConnectedDevice[] {
    // محاكاة تحديث قائمة الأجهزة
    console.log('تحديث قائمة الأجهزة المتصلة...');
    this.notifyListeners();
    return this.getConnectedDevices();
  }
}

// Singleton instance
const localServer = new LocalServerService();
export default localServer;
