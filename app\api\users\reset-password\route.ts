import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية لإعادة تعيين كلمات المرور
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { userId, username, email } = await request.json();

    if (!userId && !username && !email) {
      return NextResponse.json(
        { message: 'User ID, username, or email is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // البحث عن المستخدم
      let user;
      if (userId) {
        user = await tx.user.findUnique({ where: { id: userId } });
      } else if (username) {
        user = await tx.user.findUnique({ where: { username } });
      } else if (email) {
        user = await tx.user.findUnique({ where: { email } });
      }

      if (!user) {
        throw new Error('User not found');
      }

      // منع إعادة تعيين كلمة مرور المدير الرئيسي
      if (user.role === 'admin' && user.id !== authResult.user!.id) {
        throw new Error('Cannot reset admin password');
      }

      // في التطبيق الحقيقي، ستقوم بإرسال رمز إعادة التعيين عبر البريد الإلكتروني
      // هنا سنقوم بمحاكاة العملية
      console.log(
        `Password reset initiated for user ${user.username} (${user.email}). Confirmation would be sent.`
    );

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'PASSWORD_RESET_INITIATED',
        details: `Password reset initiated for user ${user.username} (${user.email})`,
        tableName: 'user',
        recordId: user.id.toString()
      });

      return {
        message: 'Password reset initiated successfully',
        user: {
          id: user.id,
          username: user.username,
          email: user.email
        }
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to initiate password reset:', error);

    if (error instanceof Error) {
      if (error.message === 'User not found') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      if (error.message === 'Cannot reset admin password') {
        return NextResponse.json({ error: 'Cannot reset admin password' }, { status: 403 });
      }
    }

    return NextResponse.json({ error: 'Failed to initiate password reset' }, { status: 500 });
  }
}
