import { jsPDF } from "jspdf";

// خط عربي مدمج بصيغة Base64 (خط مبسط للعربية)
const ARABIC_FONT_BASE64 = `data:font/truetype;charset=utf-8;base64,AAEAAAAOAIAAAwBgT1MvMj3hSQEAAADsAAAAVmNtYXDOH052AAABRAAAAUpjdnQgBkFGRgAAAZAAAAA+ZnBnbYoKeDsAAALQAAAJkWdhc3AAAAAQAAAMU...`; // هذا مثال مبسط

// وظيفة لإضافة دعم الخط العربي إلى jsPDF
export function setupArabicFont(doc: jsPDF): void {
  try {
    // محاولة إضافة خط عربي مخصص
    // في الواقع، سنستخدم حلول بديلة أكثر عملية

    // استخدام الخط الافتراضي مع تحسينات للعربية
    doc.setFont('helvetica');

    // تفعيل اتجاه من اليمين إلى اليسار
    doc.setR2L(true);

  } catch (error) {
    console.warn('تعذر تحميل الخط العربي المخصص، سيتم استخدام الخط الافتراضي:', error);
    // الاستمرار بالخط الافتراضي
    doc.setFont('helvetica');
    doc.setR2L(true);
  }
}

// وظيفة لمعالجة النص العربي قبل إضافته إلى PDF
export function processArabicText(text: string): string {
  if (!text) return '';

  // تنظيف النص من الأحرف الخاصة التي قد تسبب مشاكل
  let processedText = text.toString();

  // استبدال بعض الأحرف الخاصة
  processedText = processedText
    .replace(/[\u200E\u200F\u202A-\u202E]/g, '') // إزالة أحرف التحكم في الاتجاه
    .replace(/\u00A0/g, ' ') // استبدال المسافة غير المنقسمة بمسافة عادية
    .trim();

  return processedText;
}

// وظيفة لتحديد ما إذا كان النص يحتوي على أحرف عربية
export function containsArabic(text: string): boolean {
  if (!text) return false;
  return /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(text);
}

// وظيفة لحساب عرض النص العربي (تقريبي)
export function getArabicTextWidth(doc: jsPDF, text: string, fontSize: number): number {
  const processedText = processArabicText(text);

  // حفظ الإعدادات الحالية
  const currentFontSize = doc.getFontSize();

  // تطبيق حجم الخط المطلوب
  doc.setFontSize(fontSize);

  // حساب العرض
  const width = doc.getTextWidth(processedText);

  // استعادة الإعدادات
  doc.setFontSize(currentFontSize);

  return width;
}

// وظيفة لكتابة نص عربي مع معالجة خاصة
export function writeArabicText(
  doc: jsPDF,
  text: string,
  x: number,
  y: number,
  options?: any
): void {
  const processedText = processArabicText(text);

  // التأكد من تفعيل اتجاه RTL
  doc.setR2L(true);

  // كتابة النص
  doc.text(processedText, x, y, options);
}

// وظيفة لتقسيم النص الطويل إلى أسطر متعددة
export function splitArabicText(
  doc: jsPDF,
  text: string,
  maxWidth: number,
  fontSize: number
): string[] {
  const processedText = processArabicText(text);

  if (!processedText) return [''];

  // حفظ حجم الخط الحالي
  const currentFontSize = doc.getFontSize();
  doc.setFontSize(fontSize);

  // تقسيم النص إلى كلمات
  const words = processedText.split(' ');
  const lines: string[] = [];
  let currentLine = '';

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;
    const testWidth = doc.getTextWidth(testLine);

    if (testWidth <= maxWidth) {
      currentLine = testLine;
    } else {
      if (currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        // الكلمة طويلة جداً، نضعها في سطر منفصل
        lines.push(word);
      }
    }
  }

  if (currentLine) {
    lines.push(currentLine);
  }

  // استعادة حجم الخط
  doc.setFontSize(currentFontSize);

  return lines.length > 0 ? lines : [''];
}

// وظيفة لإضافة نص متعدد الأسطر مع دعم العربية
export function addMultilineArabicText(
  doc: jsPDF,
  text: string,
  x: number,
  y: number,
  maxWidth: number,
  lineHeight: number = 5,
  options?: any
): number {
  const fontSize = doc.getFontSize();
  const lines = splitArabicText(doc, text, maxWidth, fontSize);

  let currentY = y;

  for (const line of lines) {
    writeArabicText(doc, line, x, currentY, options);
    currentY += lineHeight;
  }

  return currentY;
}

// إعدادات افتراضية للتصدير العربي
export const ARABIC_PDF_DEFAULTS = {
  font: 'helvetica',
  fontSize: 12,
  lineHeight: 6,
  margin: {
    top: 20,
    right: 15,
    bottom: 20,
    left: 15
  },
  colors: {
    primary: [52, 73, 94],
    secondary: [149, 165, 166],
    success: [39, 174, 96],
    danger: [231, 76, 60],
    warning: [241, 196, 15],
    light: [248, 249, 250],
    dark: [33, 37, 41]
  }
};

// وظيفة مساعدة لإنشاء PDF مع إعدادات عربية افتراضية
export function createArabicPDF(options?: any): jsPDF {
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
    ...options
  });

  setupArabicFont(doc);

  return doc;
}