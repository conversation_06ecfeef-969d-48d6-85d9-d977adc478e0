# نظام المرفقات - دليل شامل

## 📋 نظرة عامة

تم تطوير نظام مرفقات متكامل يدعم رفع وعرض وإدارة الملفات لجميع أقسام النظام مع الميزات التالية:

- 📁 **تنظيم الملفات** في مجلدات منفصلة لكل قسم
- 🔄 **رفع متعدد** للملفات بدون حدود للحجم أو العدد
- 👁️ **عرض متقدم** للملفات مع معاينة للصور
- 💾 **حفظ آمن** في مجلد `public/attachments`
- 🗑️ **حذف ذكي** مع صلاحيات محددة

---

## 🏗️ هيكل النظام

### 1. مجلدات التخزين
```
public/
└── attachments/
    ├── supply/          # مرفقات التوريد
    ├── sales/           # مرفقات المبيعات
    ├── returns/         # مرفقات المرتجعات
    ├── evaluation/      # مرفقات التقييم
    ├── maintenance/     # مرفقات الصيانة
    └── warehouse/       # مرفقات المخازن
```

### 2. الملفات الأساسية
- `app/api/upload/route.ts` - API endpoint لرفع الملفات
- `components/AttachmentsViewer.tsx` - مكون عرض المرفقات
- `docs/attachments-system.md` - هذا الدليل

---

## 🔧 API Endpoint

### رفع الملفات (POST /api/upload)

**المعاملات:**
- `files`: ملف أو عدة ملفات
- `section`: اسم القسم (supply, sales, returns, etc.)

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم رفع 2 ملف بنجاح",
  "files": [
    {
      "originalName": "document.pdf",
      "fileName": "1703123456789_abc123.pdf",
      "filePath": "/attachments/supply/1703123456789_abc123.pdf",
      "size": 1024000,
      "type": "application/pdf",
      "uploadedAt": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### جلب الملفات (GET /api/upload)

**المعاملات:**
- `section`: اسم القسم
- `orderId`: معرف الأمر

---

## 🎨 مكون AttachmentsViewer

### الخصائص (Props)
```typescript
interface AttachmentsViewerProps {
  isOpen: boolean;                    // حالة فتح المودال
  onClose: () => void;               // دالة الإغلاق
  attachments: AttachmentFile[];     // قائمة المرفقات
  onRemove?: (fileName: string) => void; // دالة الحذف
  canDelete?: boolean;               // صلاحية الحذف
}
```

### نوع البيانات
```typescript
interface AttachmentFile {
  originalName: string;  // الاسم الأصلي
  fileName: string;      // اسم الملف المحفوظ
  filePath: string;      // مسار الملف
  size: number;          // حجم الملف بالبايت
  type: string;          // نوع الملف (MIME type)
  uploadedAt: string;    // تاريخ الرفع
}
```

### الميزات
- 🖼️ **معاينة الصور** في مودال منفصل
- 📄 **أيقونات مختلفة** حسب نوع الملف
- 📊 **عرض معلومات** الملف (الحجم، النوع، التاريخ)
- ⬇️ **تحميل مباشر** للملفات
- 🗑️ **حذف مشروط** حسب الصلاحيات

---

## 🔌 التكامل مع الصفحات

### 1. إضافة الـ Imports
```typescript
import AttachmentsViewer from '@/components/AttachmentsViewer';
```

### 2. تعريف نوع البيانات
```typescript
interface AttachmentFile {
  originalName: string;
  fileName: string;
  filePath: string;
  size: number;
  type: string;
  uploadedAt: string;
}

const [attachments, setAttachments] = useState<AttachmentFile[]>([]);
const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = useState(false);
```

### 3. دالة رفع الملفات
```typescript
const handleFileUpload = async (files: FileList) => {
  try {
    const formData = new FormData();
    Array.from(files).forEach(file => formData.append('files', file));
    formData.append('section', 'supply'); // اسم القسم

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();

    if (result.success) {
      setAttachments(prev => [...prev, ...result.files]);
      toast({
        title: 'تم إرفاق الملفات',
        description: result.message,
      });
    } else {
      toast({
        title: 'خطأ في رفع الملفات',
        description: result.error,
        variant: 'destructive',
      });
    }
  } catch (error) {
    toast({
      title: 'خطأ في رفع الملفات',
      description: 'حدث خطأ غير متوقع',
      variant: 'destructive',
    });
  }
};
```

### 4. حقل رفع الملفات
```typescript
<input
  type="file"
  ref={attachmentsInputRef}
  className="hidden"
  multiple
  onChange={(e) => {
    if (e.target.files) {
      handleFileUpload(e.target.files);
    }
    if (e.target) e.target.value = '';
  }}
/>

<Button
  variant="outline"
  onClick={() => attachmentsInputRef.current?.click()}
  className="border-indigo-300 text-indigo-600 hover:bg-indigo-50"
>
  <Upload className="ml-1 h-2 w-2" />
  {attachments.length > 0 ? `${attachments.length}` : '0'}
</Button>
```

### 5. زر عرض المرفقات
```typescript
{attachments.length > 0 && (
  <Button
    variant="outline"
    size="icon"
    onClick={() => setIsAttachmentsModalOpen(true)}
    className="border-blue-300 text-blue-600 hover:bg-blue-50"
  >
    <Eye className="h-2 w-2" />
  </Button>
)}
```

### 6. مكون العرض
```typescript
<AttachmentsViewer
  isOpen={isAttachmentsModalOpen}
  onClose={() => setIsAttachmentsModalOpen(false)}
  attachments={attachments}
  onRemove={(fileName) => {
    setAttachments(prev => prev.filter(file => file.fileName !== fileName));
  }}
  canDelete={canCreate && (isCreating || !!loadedOrder)}
/>
```

---

## 💾 حفظ واسترجاع المرفقات

### حفظ في قاعدة البيانات
```typescript
const orderData = {
  // ... بيانات أخرى
  invoiceFileName: attachments.map(file => file.fileName).join(';'),
};
```

### استرجاع من قاعدة البيانات
```typescript
const loadAttachments = (order: SupplyOrder) => {
  const fileNames = order.invoiceFileName ? order.invoiceFileName.split(';') : [];
  const convertedAttachments: AttachmentFile[] = fileNames.map(fileName => ({
    originalName: fileName,
    fileName: fileName,
    filePath: `/attachments/supply/${fileName}`,
    size: 0,
    type: 'application/octet-stream',
    uploadedAt: order.createdAt || new Date().toISOString()
  }));
  setAttachments(convertedAttachments);
};
```

---

## 🎯 الميزات المتقدمة

### 1. أنواع الملفات المدعومة
- 🖼️ **الصور**: PNG, JPG, GIF, WebP
- 📄 **المستندات**: PDF, DOC, DOCX, TXT
- 🎵 **الصوت**: MP3, WAV, OGG
- 🎬 **الفيديو**: MP4, AVI, MOV
- 📊 **جداول البيانات**: XLS, XLSX, CSV
- 📁 **أخرى**: جميع أنواع الملفات

### 2. أيقونات ذكية
- تحديد الأيقونة تلقائياً حسب نوع الملف
- ألوان مختلفة لكل نوع ملف
- عرض معلومات مفصلة عند التمرير

### 3. معاينة الصور
- فتح الصور في مودال منفصل
- تكبير وتصغير
- عرض بجودة كاملة

### 4. تحميل الملفات
- تحميل مباشر بالاسم الأصلي
- رسائل تأكيد للمستخدم
- دعم جميع المتصفحات

---

## 🔒 الأمان والصلاحيات

### 1. صلاحيات الرفع
- فقط المستخدمون المصرح لهم
- في وضع الإنشاء أو التعديل فقط

### 2. صلاحيات الحذف
- فقط المستخدمون المصرح لهم
- في وضع الإنشاء أو التعديل فقط
- تأكيد قبل الحذف

### 3. أمان الملفات
- أسماء ملفات فريدة لمنع التضارب
- حفظ في مجلدات منظمة
- عدم الكشف عن المسارات الحقيقية

---

## 🚀 التطبيق على صفحات أخرى

### خطوات التطبيق:
1. **نسخ الكود** من صفحة التوريد
2. **تغيير اسم القسم** في `formData.append('section', 'اسم_القسم')`
3. **تحديث مسار الحفظ** في قاعدة البيانات
4. **اختبار الوظائف** (رفع، عرض، حذف)

### أقسام جاهزة للتطبيق:
- ✅ **التوريد** - مطبق ويعمل
- ⏳ **المبيعات** - جاهز للتطبيق
- ⏳ **المرتجعات** - جاهز للتطبيق
- ⏳ **التقييم** - جاهز للتطبيق
- ⏳ **الصيانة** - جاهز للتطبيق

---

## 🎉 الخلاصة

نظام المرفقات الآن:
- ✅ **يعمل بكامل طاقته** في صفحة التوريد
- ✅ **يحفظ الملفات** في مجلدات منظمة
- ✅ **يعرض الملفات** بشكل جميل ومنظم
- ✅ **يدعم جميع أنواع الملفات** بدون حدود
- ✅ **آمن ومحمي** بالصلاحيات
- ✅ **جاهز للتطبيق** على الصفحات الأخرى

**🎯 النتيجة: نظام مرفقات احترافي ومتكامل!**
