--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: AuditLog; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."AuditLog" (
    id integer NOT NULL,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "userId" integer NOT NULL,
    username text NOT NULL,
    operation text NOT NULL,
    details text NOT NULL
);


ALTER TABLE public."AuditLog" OWNER TO deviceflow_user;

--
-- Name: AuditLog_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."AuditLog_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."AuditLog_id_seq" OWNER TO deviceflow_user;

--
-- Name: AuditLog_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."AuditLog_id_seq" OWNED BY public."AuditLog".id;


--
-- Name: Client; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Client" (
    id integer NOT NULL,
    name text NOT NULL,
    phone text NOT NULL,
    email text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Client" OWNER TO deviceflow_user;

--
-- Name: Client_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Client_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Client_id_seq" OWNER TO deviceflow_user;

--
-- Name: Client_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Client_id_seq" OWNED BY public."Client".id;


--
-- Name: DeliveryOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."DeliveryOrder" (
    id integer NOT NULL,
    "deliveryOrderNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "warehouseId" integer NOT NULL,
    "warehouseName" text NOT NULL,
    "employeeName" text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."DeliveryOrder" OWNER TO deviceflow_user;

--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."DeliveryOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."DeliveryOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."DeliveryOrder_id_seq" OWNED BY public."DeliveryOrder".id;


--
-- Name: Device; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Device" (
    id text NOT NULL,
    model text NOT NULL,
    status text NOT NULL,
    storage text NOT NULL,
    price double precision NOT NULL,
    condition text NOT NULL,
    "warehouseId" integer,
    "supplierId" integer,
    "dateAdded" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "replacementInfo" jsonb
);


ALTER TABLE public."Device" OWNER TO deviceflow_user;

--
-- Name: MaintenanceOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."MaintenanceOrder" (
    id integer NOT NULL,
    "orderNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "employeeName" text NOT NULL,
    "maintenanceEmployeeId" integer,
    "maintenanceEmployeeName" text,
    items jsonb NOT NULL,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'wip'::text NOT NULL,
    source text DEFAULT 'warehouse'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."MaintenanceOrder" OWNER TO deviceflow_user;

--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."MaintenanceOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."MaintenanceOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."MaintenanceOrder_id_seq" OWNED BY public."MaintenanceOrder".id;


--
-- Name: MaintenanceReceiptOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."MaintenanceReceiptOrder" (
    id integer NOT NULL,
    "receiptNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "employeeName" text NOT NULL,
    "maintenanceEmployeeName" text,
    items jsonb NOT NULL,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."MaintenanceReceiptOrder" OWNER TO deviceflow_user;

--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."MaintenanceReceiptOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."MaintenanceReceiptOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."MaintenanceReceiptOrder_id_seq" OWNED BY public."MaintenanceReceiptOrder".id;


--
-- Name: Post; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Post" (
    id integer NOT NULL,
    title text NOT NULL,
    content text,
    published boolean DEFAULT false NOT NULL,
    "authorId" integer NOT NULL
);


ALTER TABLE public."Post" OWNER TO deviceflow_user;

--
-- Name: Post_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Post_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Post_id_seq" OWNER TO deviceflow_user;

--
-- Name: Post_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Post_id_seq" OWNED BY public."Post".id;


--
-- Name: Return; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Return" (
    id integer NOT NULL,
    "roNumber" text NOT NULL,
    "opReturnNumber" text NOT NULL,
    date text NOT NULL,
    "saleId" integer NOT NULL,
    "soNumber" text NOT NULL,
    "clientName" text NOT NULL,
    "warehouseName" text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    status text DEFAULT 'معلق'::text NOT NULL,
    "processedBy" text,
    "processedDate" text,
    "employeeName" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    attachments text
);


ALTER TABLE public."Return" OWNER TO deviceflow_user;

--
-- Name: Return_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Return_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Return_id_seq" OWNER TO deviceflow_user;

--
-- Name: Return_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Return_id_seq" OWNED BY public."Return".id;


--
-- Name: Sale; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Sale" (
    id integer NOT NULL,
    "soNumber" text NOT NULL,
    "opNumber" text NOT NULL,
    date text NOT NULL,
    "clientName" text NOT NULL,
    "warehouseName" text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    "warrantyPeriod" text NOT NULL,
    "employeeName" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    attachments text
);


ALTER TABLE public."Sale" OWNER TO deviceflow_user;

--
-- Name: Sale_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Sale_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Sale_id_seq" OWNER TO deviceflow_user;

--
-- Name: Sale_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Sale_id_seq" OWNED BY public."Sale".id;


--
-- Name: Supplier; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Supplier" (
    id integer NOT NULL,
    name text NOT NULL,
    phone text NOT NULL,
    email text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Supplier" OWNER TO deviceflow_user;

--
-- Name: Supplier_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Supplier_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Supplier_id_seq" OWNER TO deviceflow_user;

--
-- Name: Supplier_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Supplier_id_seq" OWNED BY public."Supplier".id;


--
-- Name: SupplyOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."SupplyOrder" (
    id integer NOT NULL,
    "supplyOrderId" text NOT NULL,
    "supplierId" integer NOT NULL,
    "invoiceNumber" text NOT NULL,
    "supplyDate" text NOT NULL,
    "warehouseId" integer NOT NULL,
    "employeeName" text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    "invoiceFileName" text,
    "referenceNumber" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    status text DEFAULT 'completed'::text
);


ALTER TABLE public."SupplyOrder" OWNER TO deviceflow_user;

--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."SupplyOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."SupplyOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."SupplyOrder_id_seq" OWNED BY public."SupplyOrder".id;


--
-- Name: SystemSetting; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."SystemSetting" (
    id integer DEFAULT 1 NOT NULL,
    "logoUrl" text DEFAULT ''::text NOT NULL,
    "companyNameAr" text DEFAULT ''::text NOT NULL,
    "companyNameEn" text DEFAULT ''::text NOT NULL,
    "addressAr" text DEFAULT ''::text NOT NULL,
    "addressEn" text DEFAULT ''::text NOT NULL,
    phone text DEFAULT ''::text NOT NULL,
    email text DEFAULT ''::text NOT NULL,
    website text DEFAULT ''::text NOT NULL,
    "footerTextAr" text DEFAULT ''::text NOT NULL,
    "footerTextEn" text DEFAULT ''::text NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."SystemSetting" OWNER TO deviceflow_user;

--
-- Name: Warehouse; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Warehouse" (
    id integer NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    location text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."Warehouse" OWNER TO deviceflow_user;

--
-- Name: Warehouse_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Warehouse_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Warehouse_id_seq" OWNER TO deviceflow_user;

--
-- Name: Warehouse_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Warehouse_id_seq" OWNED BY public."Warehouse".id;


--
-- Name: database_backups; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.database_backups (
    id integer NOT NULL,
    name text NOT NULL,
    description text,
    "filePath" text NOT NULL,
    "fileSize" text NOT NULL,
    "backupType" text DEFAULT 'manual'::text NOT NULL,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdBy" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "connectionId" integer NOT NULL
);


ALTER TABLE public.database_backups OWNER TO deviceflow_user;

--
-- Name: database_backups_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.database_backups_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.database_backups_id_seq OWNER TO deviceflow_user;

--
-- Name: database_backups_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.database_backups_id_seq OWNED BY public.database_backups.id;


--
-- Name: database_connections; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.database_connections (
    id integer NOT NULL,
    name text NOT NULL,
    host text NOT NULL,
    port integer DEFAULT 5432 NOT NULL,
    database text NOT NULL,
    username text NOT NULL,
    password text NOT NULL,
    "isActive" boolean DEFAULT false NOT NULL,
    "isDefault" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.database_connections OWNER TO deviceflow_user;

--
-- Name: database_connections_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.database_connections_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.database_connections_id_seq OWNER TO deviceflow_user;

--
-- Name: database_connections_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.database_connections_id_seq OWNED BY public.database_connections.id;


--
-- Name: maintenance_logs; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.maintenance_logs (
    id integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    "repairDate" text NOT NULL,
    notes text,
    result text,
    status text DEFAULT 'pending'::text NOT NULL,
    "acknowledgedDate" text,
    "warehouseName" text,
    "acknowledgedBy" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.maintenance_logs OWNER TO deviceflow_user;

--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.maintenance_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.maintenance_logs_id_seq OWNER TO deviceflow_user;

--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.maintenance_logs_id_seq OWNED BY public.maintenance_logs.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.users (
    id integer NOT NULL,
    email text NOT NULL,
    name text,
    username text DEFAULT 'user'::text,
    role text DEFAULT 'user'::text,
    phone text DEFAULT ''::text,
    photo text DEFAULT ''::text,
    status text DEFAULT 'Active'::text,
    "lastLogin" text,
    "branchLocation" text,
    "warehouseAccess" jsonb,
    permissions jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.users OWNER TO deviceflow_user;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO deviceflow_user;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: AuditLog id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."AuditLog" ALTER COLUMN id SET DEFAULT nextval('public."AuditLog_id_seq"'::regclass);


--
-- Name: Client id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Client" ALTER COLUMN id SET DEFAULT nextval('public."Client_id_seq"'::regclass);


--
-- Name: DeliveryOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeliveryOrder" ALTER COLUMN id SET DEFAULT nextval('public."DeliveryOrder_id_seq"'::regclass);


--
-- Name: MaintenanceOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceOrder" ALTER COLUMN id SET DEFAULT nextval('public."MaintenanceOrder_id_seq"'::regclass);


--
-- Name: MaintenanceReceiptOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceReceiptOrder" ALTER COLUMN id SET DEFAULT nextval('public."MaintenanceReceiptOrder_id_seq"'::regclass);


--
-- Name: Post id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post" ALTER COLUMN id SET DEFAULT nextval('public."Post_id_seq"'::regclass);


--
-- Name: Return id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Return" ALTER COLUMN id SET DEFAULT nextval('public."Return_id_seq"'::regclass);


--
-- Name: Sale id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Sale" ALTER COLUMN id SET DEFAULT nextval('public."Sale_id_seq"'::regclass);


--
-- Name: Supplier id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Supplier" ALTER COLUMN id SET DEFAULT nextval('public."Supplier_id_seq"'::regclass);


--
-- Name: SupplyOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SupplyOrder" ALTER COLUMN id SET DEFAULT nextval('public."SupplyOrder_id_seq"'::regclass);


--
-- Name: Warehouse id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Warehouse" ALTER COLUMN id SET DEFAULT nextval('public."Warehouse_id_seq"'::regclass);


--
-- Name: database_backups id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups ALTER COLUMN id SET DEFAULT nextval('public.database_backups_id_seq'::regclass);


--
-- Name: database_connections id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_connections ALTER COLUMN id SET DEFAULT nextval('public.database_connections_id_seq'::regclass);


--
-- Name: maintenance_logs id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_logs ALTER COLUMN id SET DEFAULT nextval('public.maintenance_logs_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: AuditLog; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."AuditLog" (id, "timestamp", "userId", username, operation, details) FROM stdin;
1	2025-07-25 03:13:59.998	1	admin	TEST	Test audit log creation
2	2025-07-25 03:19:38.407	1	admin	UPDATE	Updated user: System Administrator (<EMAIL>)
3	2025-07-25 03:19:38.926	1	admin	UPDATE	Updated user: System Administrator (<EMAIL>)
4	2025-07-25 03:19:45.778	1	admin	UPDATE	Updated user: System Administrator (<EMAIL>)
5	2025-07-25 03:20:13.38	1	admin	CREATE	Created warehouse: omar albkri
6	2025-07-25 03:23:37.703	1	admin	UPDATE	Updated user: System Administrator (<EMAIL>)
7	2025-07-25 03:23:52	1	admin	UPDATE	Updated user: System Administrator (<EMAIL>)
8	2025-07-25 03:43:14.582	1	admin	CREATE	Created client: omar albkri
9	2025-07-25 03:43:28.86	1	admin	CREATE	Created supplier: omar albkri
10	2025-07-25 03:44:58.234	1	admin	UPDATE	Updated user: System Administrator (<EMAIL>)
11	2025-07-25 04:16:40.884	1	admin	UPDATE	Switched database connection from 'deviceflow' to 'deviceflow_test' for connection: الاتصال الافتراضي
12	2025-07-25 04:16:46.404	1	admin	UPDATE	Switched database connection from 'deviceflow_test' to 'deviceflow_db' for connection: الاتصال الافتراضي
13	2025-07-25 04:22:13.06	1	admin	CREATE	Created database connection: اتصال تجريبي - 1753417332537
14	2025-07-25 04:28:34.718	1	admin	CREATE	Created database backup: نسخة تجريبية - ٢٥‏/٧‏/٢٠٢٥، ٧:٢٨:٣٣ ص for الاتصال الافتراضي
15	2025-07-25 04:28:35.369	1	admin	CREATE	Created database connection: اتصال تجريبي - 1753417714742
\.


--
-- Data for Name: Client; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Client" (id, name, phone, email, "createdAt", "updatedAt") FROM stdin;
1	omar albkri	0772828207	<EMAIL>	2025-07-25 03:43:14.562	2025-07-25 03:43:14.562
\.


--
-- Data for Name: DeliveryOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."DeliveryOrder" (id, "deliveryOrderNumber", "referenceNumber", date, "warehouseId", "warehouseName", "employeeName", items, notes, "attachmentName", status, "createdAt") FROM stdin;
\.


--
-- Data for Name: Device; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Device" (id, model, status, storage, price, condition, "warehouseId", "supplierId", "dateAdded", "replacementInfo") FROM stdin;
\.


--
-- Data for Name: MaintenanceOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."MaintenanceOrder" (id, "orderNumber", "referenceNumber", date, "employeeName", "maintenanceEmployeeId", "maintenanceEmployeeName", items, notes, "attachmentName", status, source, "createdAt") FROM stdin;
\.


--
-- Data for Name: MaintenanceReceiptOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."MaintenanceReceiptOrder" (id, "receiptNumber", "referenceNumber", date, "employeeName", "maintenanceEmployeeName", items, notes, "attachmentName", status, "createdAt") FROM stdin;
\.


--
-- Data for Name: Post; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Post" (id, title, content, published, "authorId") FROM stdin;
\.


--
-- Data for Name: Return; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Return" (id, "roNumber", "opReturnNumber", date, "saleId", "soNumber", "clientName", "warehouseName", items, notes, status, "processedBy", "processedDate", "employeeName", "createdAt", attachments) FROM stdin;
\.


--
-- Data for Name: Sale; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Sale" (id, "soNumber", "opNumber", date, "clientName", "warehouseName", items, notes, "warrantyPeriod", "employeeName", "createdAt", attachments) FROM stdin;
\.


--
-- Data for Name: Supplier; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Supplier" (id, name, phone, email, "createdAt", "updatedAt") FROM stdin;
1	omar albkri	0772828207	<EMAIL>	2025-07-25 03:43:28.849	2025-07-25 03:43:28.849
\.


--
-- Data for Name: SupplyOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."SupplyOrder" (id, "supplyOrderId", "supplierId", "invoiceNumber", "supplyDate", "warehouseId", "employeeName", items, notes, "invoiceFileName", "referenceNumber", "createdAt", status) FROM stdin;
\.


--
-- Data for Name: SystemSetting; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."SystemSetting" (id, "logoUrl", "companyNameAr", "companyNameEn", "addressAr", "addressEn", phone, email, website, "footerTextAr", "footerTextEn", "updatedAt", "createdAt") FROM stdin;
\.


--
-- Data for Name: Warehouse; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Warehouse" (id, name, type, location, "createdAt", "updatedAt") FROM stdin;
4	omar albkri	رئيسي	yemen	2025-07-25 03:20:13.374	2025-07-25 03:20:13.374
\.


--
-- Data for Name: database_backups; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.database_backups (id, name, description, "filePath", "fileSize", "backupType", status, "createdBy", "createdAt", "connectionId") FROM stdin;
15	نسخة تجريبية - ٢٥‏/٧‏/٢٠٢٥، ٧:٢٨:٣٣ ص	نسخة احتياطية للاختبار	C:\\Users\\<USER>\\Downloads\\111\\13\\backups\\deviceflow_db_2025-07-25T04-28-34-037Z.sql	0.04 MB	manual	completed	admin	2025-07-25 04:28:34.039	1
\.


--
-- Data for Name: database_connections; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.database_connections (id, name, host, port, database, username, password, "isActive", "isDefault", "createdAt", "updatedAt") FROM stdin;
2	اتصال تجريبي - 1753417332537	localhost	5432	test_db	test_user	$2b$10$QbRt6mtOc8PosYptTXiC7.625Xi9XnQibzJLXFxsFvlG/Hck0WMky	f	f	2025-07-25 04:22:13.057	2025-07-25 04:22:13.057
1	الاتصال الافتراضي	localhost	5432	deviceflow_db	deviceflow_user	om772828	t	t	2025-07-25 04:13:24.603	2025-07-25 04:28:22.957
3	اتصال تجريبي - 1753417714742	localhost	5432	test_db	test_user	$2b$10$SUIeIChJRljH9zpcV1FJouFf2dMPjacrrr/9OfT.fEpz8QknSIukq	f	f	2025-07-25 04:28:35.367	2025-07-25 04:28:35.367
\.


--
-- Data for Name: maintenance_logs; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.maintenance_logs (id, "deviceId", model, "repairDate", notes, result, status, "acknowledgedDate", "warehouseName", "acknowledgedBy", "createdAt") FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.users (id, email, name, username, role, phone, photo, status, "lastLogin", "branchLocation", "warehouseAccess", permissions, "createdAt", "updatedAt") FROM stdin;
1	<EMAIL>	System Administrator	admin	admin			Active	\N	\N	null	"{\\"dashboard\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"track\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"supply\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"acceptDevices\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"grading\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"inventory\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"sales\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"maintenance\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"maintenanceTransfer\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"warehouseTransfer\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"clients\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"pricing\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"returns\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"warehouses\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"users\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"reports\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"stocktaking\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"settings\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"requests\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":true,\\"viewAll\\":true,\\"manage\\":[1,2,3],\\"acceptWithoutWarranty\\":true},\\"messaging\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false}}"	2025-07-25 03:04:15.951	2025-07-25 03:44:58.225
\.


--
-- Name: AuditLog_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."AuditLog_id_seq"', 15, true);


--
-- Name: Client_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Client_id_seq"', 1, true);


--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."DeliveryOrder_id_seq"', 1, false);


--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."MaintenanceOrder_id_seq"', 1, false);


--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."MaintenanceReceiptOrder_id_seq"', 1, false);


--
-- Name: Post_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Post_id_seq"', 1, false);


--
-- Name: Return_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Return_id_seq"', 1, false);


--
-- Name: Sale_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Sale_id_seq"', 1, false);


--
-- Name: Supplier_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Supplier_id_seq"', 1, true);


--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."SupplyOrder_id_seq"', 1, false);


--
-- Name: Warehouse_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Warehouse_id_seq"', 4, true);


--
-- Name: database_backups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.database_backups_id_seq', 16, true);


--
-- Name: database_connections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.database_connections_id_seq', 3, true);


--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.maintenance_logs_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.users_id_seq', 1, true);


--
-- Name: AuditLog AuditLog_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."AuditLog"
    ADD CONSTRAINT "AuditLog_pkey" PRIMARY KEY (id);


--
-- Name: Client Client_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Client"
    ADD CONSTRAINT "Client_pkey" PRIMARY KEY (id);


--
-- Name: DeliveryOrder DeliveryOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeliveryOrder"
    ADD CONSTRAINT "DeliveryOrder_pkey" PRIMARY KEY (id);


--
-- Name: Device Device_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Device"
    ADD CONSTRAINT "Device_pkey" PRIMARY KEY (id);


--
-- Name: MaintenanceOrder MaintenanceOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceOrder"
    ADD CONSTRAINT "MaintenanceOrder_pkey" PRIMARY KEY (id);


--
-- Name: MaintenanceReceiptOrder MaintenanceReceiptOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceReceiptOrder"
    ADD CONSTRAINT "MaintenanceReceiptOrder_pkey" PRIMARY KEY (id);


--
-- Name: Post Post_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post"
    ADD CONSTRAINT "Post_pkey" PRIMARY KEY (id);


--
-- Name: Return Return_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Return"
    ADD CONSTRAINT "Return_pkey" PRIMARY KEY (id);


--
-- Name: Sale Sale_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Sale"
    ADD CONSTRAINT "Sale_pkey" PRIMARY KEY (id);


--
-- Name: Supplier Supplier_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Supplier"
    ADD CONSTRAINT "Supplier_pkey" PRIMARY KEY (id);


--
-- Name: SupplyOrder SupplyOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SupplyOrder"
    ADD CONSTRAINT "SupplyOrder_pkey" PRIMARY KEY (id);


--
-- Name: SystemSetting SystemSetting_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SystemSetting"
    ADD CONSTRAINT "SystemSetting_pkey" PRIMARY KEY (id);


--
-- Name: Warehouse Warehouse_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Warehouse"
    ADD CONSTRAINT "Warehouse_pkey" PRIMARY KEY (id);


--
-- Name: database_backups database_backups_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups
    ADD CONSTRAINT database_backups_pkey PRIMARY KEY (id);


--
-- Name: database_connections database_connections_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_connections
    ADD CONSTRAINT database_connections_pkey PRIMARY KEY (id);


--
-- Name: maintenance_logs maintenance_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_logs
    ADD CONSTRAINT maintenance_logs_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: Client_email_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Client_email_key" ON public."Client" USING btree (email);


--
-- Name: DeliveryOrder_deliveryOrderNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "DeliveryOrder_deliveryOrderNumber_key" ON public."DeliveryOrder" USING btree ("deliveryOrderNumber");


--
-- Name: MaintenanceOrder_orderNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "MaintenanceOrder_orderNumber_key" ON public."MaintenanceOrder" USING btree ("orderNumber");


--
-- Name: MaintenanceReceiptOrder_receiptNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "MaintenanceReceiptOrder_receiptNumber_key" ON public."MaintenanceReceiptOrder" USING btree ("receiptNumber");


--
-- Name: Return_roNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Return_roNumber_key" ON public."Return" USING btree ("roNumber");


--
-- Name: Sale_soNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Sale_soNumber_key" ON public."Sale" USING btree ("soNumber");


--
-- Name: Supplier_email_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Supplier_email_key" ON public."Supplier" USING btree (email);


--
-- Name: SupplyOrder_supplyOrderId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "SupplyOrder_supplyOrderId_key" ON public."SupplyOrder" USING btree ("supplyOrderId");


--
-- Name: database_connections_name_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX database_connections_name_key ON public.database_connections USING btree (name);


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: users_username_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX users_username_key ON public.users USING btree (username);


--
-- Name: Post Post_authorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post"
    ADD CONSTRAINT "Post_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: database_backups database_backups_connectionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups
    ADD CONSTRAINT "database_backups_connectionId_fkey" FOREIGN KEY ("connectionId") REFERENCES public.database_connections(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO deviceflow_user;


--
-- PostgreSQL database dump complete
--

